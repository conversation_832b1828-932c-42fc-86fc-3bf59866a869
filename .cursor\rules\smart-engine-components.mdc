---
description: 
globs: 
alwaysApply: false
---
# 智能引擎组件架构

## 核心引擎组件 (`smart_engine/`)

### 引擎核心 (`smart_engine/engine/`)
- **功能**：智能引擎的核心处理逻辑
- **职责**：数据处理、算法执行、结果生成
- **设计原则**：高性能、可扩展、模块化

### 配置系统 (`smart_engine/config/`)
- **主配置**：引擎运行参数和设置
- **备份机制** (`backup/`)：配置文件的自动备份
- **动态配置** (`generated/`)：运行时生成的配置文件
- **配置优先级**：generated > main > backup

### 工具函数 (`smart_engine/utils/`)
- **通用工具**：数据处理、文件操作、格式转换
- **引擎工具**：专用于引擎的辅助功能
- **性能工具**：监控、分析、优化相关工具

## 应用层组件 (`src/`)

### 核心业务 (`src/core/`)
- **业务逻辑**：应用的主要功能实现
- **数据模型**：业务实体和数据结构
- **服务层**：业务服务的封装和抽象

### 命令接口 (`src/commands/`)
- **CLI命令**：命令行接口的实现
- **参数处理**：命令行参数解析和验证
- **交互逻辑**：用户交互和反馈机制

### 代码生成 (`src/generators/`)
- **模板引擎**：代码模板的处理和生成
- **生成器**：各种类型代码的自动生成
- **自定义规则**：代码生成的规则和策略

## 数据管理 (`data/`)

### 日志系统
- **应用日志** (`logs/`)：应用程序运行日志
- **API日志** (`api_logs/`)：API调用和响应日志
- **日志轮转**：自动管理日志文件大小和数量

### 数据存储
- **临时数据**：处理过程中的中间数据
- **缓存数据**：提高性能的缓存存储
- **备份数据**：重要数据的备份副本

## 测试架构 (`tests/`)

### 测试数据管理
- **账户数据** (`test_data/accounts/`)：用户账户测试数据
- **API响应** (`test_data/api_responses/`)：模拟API响应数据
- **指标数据** (`test_data/metrics/`)：性能和业务指标测试数据

### 测试策略
- **单元测试**：组件级别的独立测试
- **集成测试**：模块间交互的测试
- **端到端测试**：完整流程的功能测试

## 启动和部署

### 启动器 (`launchers/`)
- **主启动器**：[launcher.py](mdc:launcher.py) - 应用程序入口
- **环境配置**：不同环境的启动配置
- **服务管理**：启动、停止、重启等操作

### 脚本工具 (`script/`)
- **部署脚本**：自动化部署和配置
- **维护脚本**：系统维护和数据处理
- **监控脚本**：系统状态监控和报告

## 开发工具 (`tools/`)
- **调试工具**：[debug_account_mapping.py](mdc:debug_account_mapping.py) 等调试脚本
- **性能分析**：性能监控和分析工具
- **数据迁移**：数据导入、导出和转换工具

## 组件交互原则

### 依赖关系
- 核心引擎独立于应用层
- 应用层依赖核心引擎提供的服务
- 工具和脚本可以调用任何层的功能

### 通信机制
- 配置文件驱动的参数传递
- 日志系统用于状态和错误报告
- 标准化的接口和协议

### 扩展性设计
- 插件化的引擎组件
- 可配置的处理流程
- 模块化的功能扩展

