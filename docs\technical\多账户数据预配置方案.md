# 多账户数据预配置方案

## 📋 方案概述

基于系统已有的配置文件驱动能力，采用**预配置数据映射**方案实现多账户数据聚合功能，替代复杂的实时计算方案。

## 🎯 设计原则

### 1. **充分利用现有能力**
- 系统已有单账户数据修改功能
- 已有配置文件驱动机制
- 已有API拦截和响应替换能力

### 2. **简化技术实现**
- 避免复杂的实时计算和分配算法
- 采用简单的数据映射和查找
- 减少代码复杂度和维护成本

### 3. **保证数据一致性**
- 配置文件设计时就保证数学关系正确
- 避免运行时计算错误
- 便于数据验证和调试

## 🏗️ 技术架构

### **数据流向**：
```
配置文件 → API识别 → 数据映射 → 响应替换
```

### **处理逻辑**：
```python
def process_multi_account_api(flow):
    api_type = identify_api_type(flow.request.url)
    
    if api_type == "multi_account_list":
        ad_type = extract_ad_type(flow.request.body)  # feed/search/all
        data = config["multi_account_data"][ad_type]
        
    elif api_type == "single_account_detail":
        account_id = extract_account_id(flow.request.url)
        data = config["single_account_data"][account_id]
    
    replace_response_data(flow, data)
```

## 📊 配置文件结构

### **完整配置示例**：
```json
{
  "multi_account_data": {
    "feed_ads": {
      "total_metrics": {
        "stat_cost": "1515.10",
        "show_cnt": "64794",
        "convert_cnt": "44",
        "conversion_cost": "34.43",
        "conversion_rate": "0.07%"
      },
      "data_list": [
        {
          "advertiser_id": ****************,
          "advertiser_name": "希赢贸易",
          "stat_cost": "1460.56",
          "show_cnt": "62445",
          "convert_cnt": "42",
          "conversion_cost": "34.77",
          "conversion_rate": "0.067%"
        },
        {
          "advertiser_id": ****************,
          "advertiser_name": "广州希赢贸易",
          "stat_cost": "51.51",
          "show_cnt": "2203",
          "convert_cnt": "2",
          "conversion_cost": "25.76",
          "conversion_rate": "0.091%"
        },
        {
          "advertiser_id": ****************,
          "advertiser_name": "广州希赢",
          "stat_cost": "3.03",
          "show_cnt": "146",
          "convert_cnt": "0",
          "conversion_cost": "0.00",
          "conversion_rate": "0.00%"
        },
        {
          "advertiser_id": 1829638227214851,
          "advertiser_name": "初仔好得意",
          "stat_cost": "0.00",
          "show_cnt": "0",
          "convert_cnt": "0",
          "conversion_cost": "0.00",
          "conversion_rate": "0.00%"
        }
      ]
    },
    "search_ads": {
      "total_metrics": {
        "stat_cost": "0.00",
        "show_cnt": "0",
        "convert_cnt": "0",
        "conversion_cost": "0.00",
        "conversion_rate": "0.00%"
      },
      "data_list": [
        /* 所有账户数据都为0 */
      ]
    },
    "all_ads": {
      /* 等于feed_ads数据，因为search_ads为0 */
    }
  },
  
  "single_account_data": {
    "****************": {
      "stat_cost": "1460.56",
      "show_cnt": "62445", 
      "convert_cnt": "42",
      "conversion_cost": "34.77",
      "conversion_rate": "0.067%",
      "ctr": "0.067%"
    },
    "****************": {
      "stat_cost": "51.51",
      "show_cnt": "2203",
      "convert_cnt": "2", 
      "conversion_cost": "25.76",
      "conversion_rate": "0.091%",
      "ctr": "0.091%"
    }
    /* 其他账户配置... */
  }
}
```

## 🔧 实现细节

### 1. **API识别机制**
```python
def identify_api_type(url):
    if "/get_account_list" in url:
        return "multi_account_list"
    elif "/account_detail" in url:  # 需要确认实际API路径
        return "single_account_detail"
    return "unknown"

def extract_ad_type(request_body):
    pricing_category = request_body.get("filter", {}).get("pricingCategory", [])
    if 2 in pricing_category:
        return "feed_ads"
    elif 1 in pricing_category:
        return "search_ads"
    else:
        return "all_ads"
```

### 2. **数据映射逻辑**
```python
def get_multi_account_data(ad_type):
    return config["multi_account_data"][ad_type]

def get_single_account_data(account_id):
    return config["single_account_data"].get(str(account_id), default_data)
```

### 3. **响应数据替换**
```python
def replace_response_data(flow, data):
    response = json.loads(flow.response.text)
    response["data"] = data
    flow.response.set_text(json.dumps(response))
```

## ✅ 方案优势

### **技术优势**：
- **简单可靠**：避免复杂计算，减少出错概率
- **性能优秀**：直接数据查找，响应速度快
- **易于调试**：配置文件可视化，问题定位容易
- **扩展性强**：添加账户只需修改配置文件

### **维护优势**：
- **配置驱动**：数据修改不需要改代码
- **数据一致性**：配置时就保证逻辑正确
- **版本控制**：配置文件可以版本管理
- **测试友好**：可以轻松创建测试数据

### **业务优势**：
- **灵活配置**：可以模拟各种业务场景
- **数据真实**：可以配置符合实际的数据分布
- **快速调整**：业务需求变化时快速响应

## 🚀 实施计划

### 阶段1：配置文件设计
- 设计完整的配置文件结构
- 确保数据数学关系正确
- 创建验证机制

### 阶段2：API识别优化
- 完善多账户列表API识别
- 添加单账户详情API识别
- 测试API识别准确性

### 阶段3：数据映射实现
- 实现配置文件加载
- 实现数据映射逻辑
- 实现响应替换功能

### 阶段4：测试验证
- 功能测试
- 数据一致性验证
- 性能测试

## 📝 注意事项

1. **配置文件验证**：需要验证配置数据的数学关系正确性
2. **API路径确认**：需要确认单账户详情页面的实际API路径
3. **错误处理**：需要处理配置文件缺失或数据不完整的情况
4. **向后兼容**：保持与现有单账户功能的兼容性

---

**📊 预配置方案，让多账户数据管理更简单高效！**

*创建时间: 2025-06-17*
