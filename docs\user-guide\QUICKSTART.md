# 🚀 快速开始指南

## 📋 开始前准备

### 环境检查
- ✅ Python 3.7+ 已安装
- ✅ Windows/MacOS/Linux 系统
- ✅ 浏览器支持HTTP代理设置
- ✅ 网络连接正常

### 时间预估
- 💨 **体验模式**：2分钟（使用默认配置）
- 🎯 **自定义模式**：5分钟（配置个人数据）
- 🔧 **高级配置**：10分钟（深度定制）

## ⚡ 方式一：2分钟极速体验

### 步骤 1：安装依赖
```bash
pip install -r requirements.txt
```

### 步骤 2：一键启动
```bash
python launcher.py
```

选择选项：
```
🚀 巨量引擎智能API管理系统
============================================================
请选择启动模式:
1. 🔍 API监控模式 - 监控和学习新的API模式
2. 👥 多账户模式 - 支持多账户智能引擎
0. ❌ 退出

请输入选择 (0-2): 2
```

### 步骤 3：配置浏览器
1. **设置代理**：`127.0.0.1:8080`
2. **安装证书**：访问 [mitm.it](http://mitm.it) 下载安装证书

### 步骤 4：查看效果
访问 [巨量引擎广告平台](https://ad.oceanengine.com/)，所有广告状态已变为"启用中"！

## 🎯 方式二：5分钟API监控模式

### 步骤 1：安装依赖
```bash
pip install -r requirements.txt
```

### 步骤 2：启动API监控
```bash
python launcher.py
# 选择 1. API监控模式
```

### 步骤 3：输入账户信息
```
🏢 请输入账户信息：
========================================
请输入自定义账户昵称 (例如: WH-我的广告公司): WH-北京科技有限公司
请输入自定义账户ID (例如: ****************): ****************

📋 账户信息预览：
  账户昵称: WH-北京科技有限公司
  账户ID: ****************
```

### 步骤 4：输入广告数据
```
🎯 请输入广告数据：
========================================
请输入广告展示数: 50000
请输入广告点击数: 2500
请输入转化数: 500
请输入广告活动的总成本(消耗): 5000.0

📊 计算结果：
========================================
点击率: 5.0%
转化率: 20.0%
每千次展示成本: 100.0
每次点击成本(平均点击单价): 2.0
每次转化成本(平均转换成本): 10.0
```

### 步骤 5：启动服务
系统自动启动，看到：
```
🔄 启动智能引擎...
==================================================
📡 代理端口: 8080
🌐 请将浏览器代理设置为: 127.0.0.1:8080
🔗 访问广告后台: https://ad.oceanengine.com/
⏹️  按Ctrl+C停止服务器
==================================================
✅ [智能引擎] 初始化完成
```

### 步骤 6：配置浏览器代理

#### Chrome浏览器设置
1. 设置 → 高级 → 系统 → 打开代理设置
2. 手动代理设置：
   - HTTP代理：`127.0.0.1:8080`
   - HTTPS代理：`127.0.0.1:8080`

#### Edge浏览器设置
1. 设置 → 系统和性能 → 打开计算机的代理设置
2. 手动代理设置：
   - 地址：`127.0.0.1`
   - 端口：`8080`

#### 证书安装（首次使用）
1. 访问 [http://mitm.it](http://mitm.it)
2. 点击对应系统下载证书
3. 安装到"受信任的根证书颁发机构"
4. 重启浏览器

### 步骤 7：体验效果
1. 访问 [https://ad.oceanengine.com/](https://ad.oceanengine.com/)
2. 登录您的账户
3. 查看效果：
   - ✅ 账户名称显示为：`WH-北京科技有限公司`
   - ✅ 所有广告状态：`启用中` / `投放中`
   - ✅ 展示数据：`50,000`
   - ✅ 点击率：`5.0%`
   - ✅ 转化率：`20.0%`

## 🔧 高级功能

### 监控模式（学习新API）
```bash
python quick_start_monitor.py
```
自动识别新的API接口，生成配置规则

### 配置文件编辑
直接编辑 `metrics.json` 精确控制数据：
```json
{
  "Account Name": "WH-我的广告公司",
  "Account ID": "****************", 
  "Show Count": 50000,
  "Click Count": 2500,
  "Convert Count": 500,
  "Stat Cost": 5000.0,
  "CTR (%)": 5.0,
  "Conversion Rate (%)": 20.0
}
```

### 多账户配置
使用统一启动器可以快速启动多账户模式：
```bash
python launcher.py
# 选择 "2. 👥 多账户模式"
```

## 🔍 问题排查

### 常见问题

#### Q: 浏览器无法访问网站
**原因**: 证书未正确安装
**解决**: 
1. 确认访问 [http://mitm.it](http://mitm.it) 能正常打开
2. 重新下载安装证书
3. 重启浏览器

#### Q: 广告状态没有改变
**原因**: 代理设置不正确
**解决**: 
1. 确认代理地址：`127.0.0.1:8080`
2. 检查控制台是否有处理日志
3. 清除浏览器缓存

#### Q: 程序启动失败
**原因**: 端口被占用
**解决**: 
```bash
# Windows查看端口占用
netstat -ano | findstr :8080

# 终止占用进程
taskkill /F /PID <进程ID>
```

#### Q: 数据显示不正确
**原因**: 配置文件问题
**解决**: 
1. 删除 `metrics.json`
2. 重新运行配置程序
3. 检查数据输入是否正确

### 调试技巧

#### 查看处理日志
程序运行时会显示详细的API处理信息：
```
✅ [新引擎] 余额API修改完成 - 账户: WH-北京科技有限公司
✅ [新引擎] 广告列表API修改完成
✅ [新引擎] 统计数据API修改完成
```

#### 验证配置文件
```bash
# 检查配置文件是否正确生成
cat metrics.json

# 检查API规则配置
cat smart_engine/config/api_rules_config.json
```

## 📊 效果验证

### 验证清单
- [ ] 浏览器代理设置正确（127.0.0.1:8080）
- [ ] CA证书已安装
- [ ] 程序正常启动，显示监听端口8080
- [ ] 访问广告平台无SSL错误
- [ ] 账户名称显示为自定义名称
- [ ] 广告状态显示为"启用中"/"投放中"
- [ ] 数据指标显示为自定义数值

### 成功标志
看到控制台输出类似信息表示配置成功：
```
✅ [智能引擎] 初始化完成
✅ [新引擎] 余额API修改完成 - 账户: WH-北京科技有限公司
✅ [新引擎] 广告列表API修改完成
```

## 🎉 下一步

### 探索更多功能
- 📖 查看 [README_账户配置.md](README_账户配置.md) 了解账户配置详情
- 📊 查看 [account_apis_summary.md](account_apis_summary.md) 了解API覆盖情况
- 🏗️ 查看 [docs/](docs/) 目录了解技术细节

### 停止服务
使用完毕后，按 `Ctrl+C` 停止代理服务，记得恢复浏览器代理设置。

---

🎯 **恭喜！您已成功配置巨量引擎广告状态修改工具，开始享受完美的广告数据展示吧！** 