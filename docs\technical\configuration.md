# 🔧 智能引擎API配置操作指南

## 📖 概述

本指南详细说明如何为智能引擎添加新的API监控和处理规则。通过简单的配置文件修改，您可以在1-2分钟内完成新API的集成，无需编写任何代码。

## 🎯 核心优势

- ⚡ **快速**: 1-2分钟完成配置
- 🔧 **简单**: 无需编程，仅需配置
- 🎯 **精确**: 支持多种匹配模式
- 🔄 **灵活**: 随时调整和优化

## 📋 配置文件位置

主配置文件：`smart_engine/config/api_rules_config.json`

## 🔍 信息收集模板

在开始配置前，请按照以下模板收集新API的信息：

```
新API信息：
- URL: /your/new/api/path
- 类型: [项目/广告/素材/统计/其他]
- 处理需求: [修改状态/注入数据/其他]
- 示例响应: [可选，JSON格式]
```

### 📊 详细信息说明

#### 1. **URL信息**
- 完整的API路径，例如：`/new/promotion/list`
- URL中的关键参数，例如：`campaign_id=`
- 请求体中的关键字段，例如：`{"action": "list"}`

#### 2. **API类型分类**
- **项目类型**: 项目列表、项目详情、项目统计等
- **广告类型**: 广告列表、广告详情、广告投放状态等
- **素材类型**: 素材列表、素材审核状态等
- **统计类型**: 数据报表、统计分析等
- **其他类型**: 新的业务类型

#### 3. **处理需求**
- **修改状态**: 将"未投放"改为"投放中"等
- **注入数据**: 添加自定义的metrics指标
- **清空字段**: 移除不需要的状态信息
- **自定义逻辑**: 特殊的处理需求

## ⚙️ 配置步骤详解

### 第一步: 确定API模式

根据您的需求，选择合适的匹配模式：

#### **URL路径匹配**
```json
{
  "your_api_name": {
    "patterns": ["/your/api/path"],
    "type": "promotion",
    "handler": "modify_promotion_list"
  }
}
```

#### **URL参数匹配**
```json
{
  "your_api_name": {
    "patterns": [
      {"url_param": "campaign_id"}
    ],
    "type": "promotion",
    "handler": "modify_promotion_detail"
  }
}
```

#### **请求体匹配**
```json
{
  "your_api_name": {
    "patterns": [
      {"request_body": ["action", "list"]}
    ],
    "type": "project",
    "handler": "modify_project_list"
  }
}
```

#### **多重匹配**
```json
{
  "your_api_name": {
    "patterns": [
      "/your/api/path",
      {"url_param": "type"},
      {"request_body": ["status"]}
    ],
    "type": "material",
    "handler": "modify_material_list"
  }
}
```

### 第二步: 选择处理器

根据API类型选择合适的处理器：

| API类型 | 推荐处理器 | 说明 |
|---------|-----------|------|
| 项目相关 | `modify_project_list` | 处理项目列表 |
| | `modify_project_detail` | 处理项目详情 |
| 广告相关 | `modify_promotion_list` | 处理广告列表 |
| | `modify_promotion_detail` | 处理广告详情 |
| 素材相关 | `modify_material_list` | 处理素材列表 |
| 统计数据 | `modify_statistics` | 处理统计查询 |
| 余额信息 | `modify_balance` | 处理账户余额 |

### 第三步: 配置状态映射（可选）

如果需要自定义状态映射，可以添加新的状态规则：

```json
{
  "status_mappings": {
    "your_new_type": {
      "fields": {
        "status_field_name": "期望的状态值",
        "another_field": "另一个值"
      },
      "clear_fields": [
        "需要清空的字段1",
        "需要清空的字段2"
      ],
      "list_fields": {
        "列表字段名": ["列表值1", "列表值2"]
      }
    }
  }
}
```

### 第四步: 配置数据模板（可选）

如果需要注入自定义数据，可以配置数据模板：

```json
{
  "metrics_template": {
    "your_template": {
      "field_name": "{Metric Name}",
      "calculated_field": "{Base Metric}|*1.2",
      "static_field": "固定值"
    }
  }
}
```

## 📝 实际配置示例

### 示例 1: 添加新的广告计划API

**需求**: 监控 `/new/campaign/performance` API，修改状态并注入数据

**配置**:
```json
{
  "api_patterns": {
    "new_campaign_performance": {
      "patterns": ["/new/campaign/performance"],
      "type": "promotion",
      "handler": "modify_promotion_list"
    }
  }
}
```

### 示例 2: 添加素材审核API

**需求**: 监控包含 `material_audit` 参数的API

**配置**:
```json
{
  "api_patterns": {
    "material_audit": {
      "patterns": [
        {"url_param": "material_audit"}
      ],
      "type": "material",
      "handler": "modify_material_list"
    }
  }
}
```

### 示例 3: 添加自定义状态类型

**需求**: 新增一个 "creative" 类型，有特殊的状态字段

**配置**:
```json
{
  "api_patterns": {
    "creative_list": {
      "patterns": ["/creative/list"],
      "type": "creative",
      "handler": "modify_creative_list"
    }
  },
  "status_mappings": {
    "creative": {
      "fields": {
        "creative_status": "审核通过",
        "creative_status_name": "正常投放"
      },
      "clear_fields": ["creative_reject_reason"]
    }
  }
}
```

## 🔄 配置流程

### 完整操作步骤

1. **收集信息**: 使用信息收集模板记录新API详情
2. **备份配置**: 备份当前的 `api_rules_config.json` 文件
3. **编辑配置**: 在配置文件中添加新的API规则
4. **验证语法**: 确保JSON格式正确
5. **重启程序**: 运行 `python mitmPro_AD.py` 应用新配置
6. **测试验证**: 访问相关页面验证API处理是否正常

### 配置验证清单

- [ ] JSON语法正确
- [ ] API模式匹配准确
- [ ] 处理器选择合适
- [ ] 状态映射完整
- [ ] 测试案例通过

## 🛠️ 常见配置场景

### 场景 1: 快速添加相似API

如果新API与现有API结构相似：

```json
{
  "similar_api": {
    "patterns": ["/new/similar/path"],
    "type": "promotion",  // 使用现有类型
    "handler": "modify_promotion_list"  // 使用现有处理器
  }
}
```

### 场景 2: 添加具有特殊参数的API

```json
{
  "special_param_api": {
    "patterns": [
      "/api/data",
      {"url_param": "special_param"}
    ],
    "type": "statistics",
    "handler": "modify_statistics"
  }
}
```

### 场景 3: 添加需要请求体匹配的API

```json
{
  "request_body_api": {
    "patterns": [
      {"request_body": ["action", "query", "type"]}
    ],
    "type": "project",
    "handler": "modify_project_list"
  }
}
```

## 🔍 故障排除

### 常见问题

#### Q: 添加配置后API没有被处理？
**A**: 检查以下几点：
- JSON语法是否正确
- 匹配模式是否准确
- 程序是否重启
- URL路径是否完整匹配

#### Q: 状态修改不生效？
**A**: 确认：
- 状态字段名是否正确
- 类型映射是否配置
- 处理器是否支持该状态类型

#### Q: 数据注入失败？
**A**: 检查：
- metrics.json文件是否存在
- 数据模板配置是否正确
- 字段名是否匹配

### 调试方法

1. **查看控制台输出**: 智能引擎会输出详细的处理日志
2. **检查匹配结果**: 观察API是否被正确识别
3. **验证配置语法**: 使用JSON验证工具检查语法
4. **逐步测试**: 先添加简单的匹配规则，再逐步完善

## 📞 技术支持

如需帮助，请提供：
- 新API的完整信息
- 期望的处理效果
- 遇到的具体问题
- 相关的错误日志

---

**🎯 通过智能引擎，让API扩展变得简单高效！** 