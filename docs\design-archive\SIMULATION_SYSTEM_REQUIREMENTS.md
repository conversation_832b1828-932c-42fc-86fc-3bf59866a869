# 🎯 沙盘系统升级需求分析

## 📋 需求概述

**提出时间**: 2025-06-03  
**需求类型**: 功能升级 - 数据模拟增强  
**优先级**: 待确定  

### 🎯 **核心需求**
基于当前沙盘系统，实现更逼真的投放数据模拟，支持时间序列数据生成、持久化存储和用户控制。

## 📊 现状分析

### 🔄 **当前系统状态**
- **现有功能**: 基于用户输入数据，实时拦截API并修改响应
- **数据特点**: 静态数据，每次显示相同的指标
- **时间维度**: 单点时间，没有历史数据
- **存储方式**: 内存中的临时数据（metrics.json）
- **用户体验**: 实时修改，即时显示

### 🎯 **系统架构现状**
```
用户输入 → metrics.json → 智能引擎 → API拦截 → 页面显示
```

## 🚀 新需求详细描述

### 1. **时间序列模拟**
- **需求**: 模拟持续的投放数据（天/周/月）
- **目标**: 提供历史数据的完整性
- **价值**: 增强沙盘系统的真实感

### 2. **数据持久化**
- **需求**: 将模拟数据存储到数据库
- **目标**: 数据的永久保存和快速查询
- **价值**: 支持长期的数据分析和展示

### 3. **用户控制**
- **需求**: 用户可以控制数据走向/趋势
- **目标**: 灵活的数据模拟策略
- **价值**: 满足不同演示场景的需求

### 4. **真实感提升**
- **需求**: 更逼真的投放数据模拟
- **目标**: 接近真实投放的数据表现
- **价值**: 提升沙盘系统的可信度

## ❓ 关键需求对齐问题

### 🎨 **1. 数据生成策略**
**问题**: 数据是基于什么规律生成？

**选项分析**:
- **A. 线性增长**: 简单但不够真实
- **B. 波动增长**: 更接近真实情况

**确认**: AB组合策略

### 🎛️ **2. 用户控制维度**
**问题**: 用户可以控制哪些方面？

**控制选项**:
- **整体趋势**: 上升/下降/平稳
- **波动幅度**: 数据变化的剧烈程度
- **投放策略变化**: 模拟投放策略调整的影响
- **预算变化**: 模拟预算调整对数据的影响

**确认**: 需要支持以上4点控制维度

### ⏰ **3. 时间粒度**
**问题**: 数据的最小时间单位是什么？

**粒度选项**:
- **小时级别**: 24个数据点/天，数据量大但精细
- **天级别**: 1个数据点/天，数据量适中
- **用户可选**: 灵活但增加复杂度

**相关考虑**:
- 数据库存储量
- 查询性能
- 页面展示效果
- 用户使用场景

**确认**: 小时级别

### 💾 **4. 数据存储结构**
**问题**: 需要存储哪些维度的数据？

**数据层级**:
```
账户级别
├── 项目级别
│   ├── 广告级别
│   │   └── 素材级别
│   └── 汇总数据
└── 总计数据
```

**指标类别**:
- **基础指标**: 展示数、点击数、转化数、消耗
- **计算指标**: CTR、转化率、CPM、CPC、转化成本
- **对比数据**: 环比增长数据
- **扩展指标**: 深度转化等高级指标

**确认**: 需要支持以上数据层级和指标类别以及api中返回的数据

### 📈 **5. 历史数据查看**
**问题**: 用户如何查看历史数据？

**查看方式**:
- **A. 现有页面集成**: 在广告后台直接显示历史趋势

**相关功能**:
- 时间范围选择
- 数据筛选功能
- 图表展示
- 数据对比

**待确认**: 采用A查看方式

### 🔗 **6. 数据一致性**
**问题**: 如何保证数据的业务逻辑一致性？

**业务规则**:
- **漏斗逻辑**: 展示数 ≥ 点击数 ≥ 转化数
- **成本计算**: 成本 = CPC × 点击数 = CPM × 展示数/1000
- **比率计算**: CTR = 点击数/展示数 × 100%
- **合理性检查**: 各项指标在合理范围内

**一致性保证**:
- 数据生成时的校验机制
- 数据修改时的连锁更新
- 异常数据的处理策略

**待确认**: 需要同时满足以上3点一致性保证机制

## 💡 初步技术方案

### 🏗️ **系统架构扩展**

#### 当前架构
```
用户输入 → metrics.json → 智能引擎 → API拦截 → 页面显示
```

#### 目标架构
```
用户输入 → 数据生成引擎 → 数据库存储 → API拦截 → 页面显示
           ↓
    趋势控制器 → 历史数据API → 数据管理界面
```

### 📊 **可能的功能模块**

#### 1. **数据生成引擎**
- **功能**: 基于规则生成时间序列数据
- **输入**: 用户基础数据 + 趋势控制参数
- **输出**: 按时间序列的完整数据集
- **算法**: 数学模型 + 随机因子 + 业务规律

#### 2. **趋势控制器**
- **功能**: 用户定义数据走向
- **控制项**: 趋势方向、波动幅度、特殊事件
- **界面**: 简单的配置界面或配置文件
- **实时性**: 支持动态调整

#### 3. **数据存储层**
- **技术选型**: SQLite/MySQL/PostgreSQL
- **表结构**: 时间序列 + 多维度数据
- **索引优化**: 时间戳 + 业务主键
- **数据保留**: 配置化的数据清理策略

#### 4. **历史数据API**
- **功能**: 提供历史数据查询接口
- **查询条件**: 时间范围、数据层级、指标类型
- **返回格式**: JSON格式，兼容现有API
- **缓存策略**: 热点数据缓存

#### 5. **数据管理界面**
- **功能**: 用户控制和查看数据
- **技术**: Web界面或集成到现有系统
- **功能**: 趋势设置、数据查看、导出功能
- **权限**: 基于用户角色的访问控制

## 🎮 用户体验设想

### 📝 **用户操作流程**

#### 阶段1: 初始化设置
1. 用户输入基础数据（保持现有方式）
2. 用户选择模拟时间范围（如30天）
3. 用户设置基本趋势（上升/平稳/下降）
4. 系统生成并存储初始数据集

#### 阶段2: 数据查看
1. 用户访问广告后台
2. 系统从数据库读取对应时间的数据
3. 页面显示历史趋势和当前数据
4. 支持时间范围切换查看

#### 阶段3: 趋势调整
1. 用户进入数据管理界面
2. 调整未来数据的趋势参数
3. 系统重新生成后续数据
4. 实时预览调整效果

#### 阶段4: 高级功能
2. 场景预设模板

### 🎯 **用户价值**
- **真实感**: 完整的历史数据支撑
- **灵活性**: 可控的数据走向
- **便捷性**: 一键生成长期数据
- **专业性**: 符合业务逻辑的数据表现

## 🔧 技术实现考虑

### 📊 **数据库设计**

#### 核心表结构（示例）
```sql
-- 时间序列数据表
CREATE TABLE simulation_data (
    id BIGINT PRIMARY KEY,
    account_id VARCHAR(50),
    project_id VARCHAR(50),
    ad_id VARCHAR(50),
    material_id VARCHAR(50),
    date_time DATETIME,
    show_cnt BIGINT,
    click_cnt BIGINT,
    convert_cnt BIGINT,
    stat_cost DECIMAL(10,2),
    -- 计算字段可以实时计算或存储
    ctr DECIMAL(5,2),
    conversion_rate DECIMAL(5,2),
    cpm DECIMAL(10,2),
    cpc DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 趋势控制配置表
CREATE TABLE trend_config (
    id INT PRIMARY KEY,
    user_id VARCHAR(50),
    trend_type ENUM('linear', 'exponential', 'wave', 'custom'),
    growth_rate DECIMAL(5,2),
    volatility DECIMAL(5,2),
    config_json TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 🔄 **集成策略**

#### 与现有系统的集成
1. **保持兼容**: 现有API拦截逻辑不变
2. **数据源切换**: 从metrics.json切换到数据库
3. **渐进升级**: 可选择使用新功能

#### 开发优先级
1. **Phase 1**: 基础数据生成和存储
2. **Phase 2**: 简单的趋势控制
3. **Phase 3**: 数据管理界面
4. **Phase 4**: 高级功能和优化

## 📋 待确认事项清单

### 🎯 **业务需求**
- [ ] 数据生成策略选择
- [ ] 用户控制维度范围
- [ ] 时间粒度确定
- [ ] 数据层级和指标范围
- [ ] 历史数据查看方式
- [ ] 数据一致性要求

### 🔧 **技术需求**
- [ ] 数据库技术选型
- [ ] 数据保留策略
- [ ] 性能要求（并发量、响应时间）
- [ ] 界面需求（新界面 vs 集成现有）
- [ ] 部署要求（独立服务 vs 集成部署）


- [ ] 维护计划

## 📞 后续讨论

**等待用户反馈**:
3. 技术方案的偏好
4. 开发优先级的安排

**建议下次讨论**:
- 确定核心功能范围
- 选择技术实现方案
- 制定开发计划
- 设计数据结构

---

**💡 备注**: 本文档为需求分析初稿，等待用户梳理后进一步完善和确认。 