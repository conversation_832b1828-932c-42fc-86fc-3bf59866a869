{"version": "2.0.0", "current_system": "smart", "migration_step": 7, "systems": {"smart": {"name": "智能引擎", "version": "2.0.0", "description": "配置驱动的智能API处理引擎", "main_file": "mitmPro_AD.py", "engine_file": "smart_api_engine_minimal.py", "config_file": "api_rules_config.json", "status": "active", "dependencies": ["ad_utils.py"]}}, "ports": {"smart": 8080}, "migration": {"started_at": "2024-01-XX", "completed_at": "2025-06-03", "current_step": 7, "total_steps": 7, "steps_completed": ["directory_structure", "basic_config", "smart_engine_moved", "legacy_system_moved", "user_input_integrated", "legacy_files_removed", "migration_completed"], "status": "completed"}}