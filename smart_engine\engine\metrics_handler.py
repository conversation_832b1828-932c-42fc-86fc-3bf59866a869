import json
from mitmproxy import http

# 使用绝对导入
from smart_engine.utils.ad_utils import format_number_with_commas, format_number_with_commas_int

class MetricsHandler:
    """Metrics数据处理器"""
    
    def __init__(self, config, metrics_data):
        """初始化metrics处理器
        
        Args:
            config: 配置字典
            metrics_data: 指标数据字典
        """
        self.config = config
        self.metrics = metrics_data
    
    def build_metrics_data(self, template_type='basic'):
        """构建metrics数据 - 增强版"""
        metrics_data = {}
        
        # 合并基础和扩展模板
        template = {}
        if 'basic' in self.config.get('metrics_template', {}):
            template.update(self.config['metrics_template']['basic'])
        if template_type == 'extended' and 'extended' in self.config.get('metrics_template', {}):
            template.update(self.config['metrics_template']['extended'])
        
        for field, pattern in template.items():
            if '{' in pattern and '}' in pattern:
                # 解析模板
                parts = pattern.split('|')
                # 提取指标名称（移除百分号）
                metric_part = parts[0].replace('{', '').replace('}', '')
                if metric_part.endswith('%'):
                    metric_key = metric_part[:-1]
                    has_percent_in_key = True
                else:
                    metric_key = metric_part
                    has_percent_in_key = False
                
                # 获取基础值
                base_value = self.metrics.get(metric_key, 0)
                
                # 处理计算表达式
                if len(parts) > 1 and parts[1].startswith('*'):
                    # 提取乘数（移除百分号）
                    multiplier_part = parts[1][1:]
                    if multiplier_part.endswith('%'):
                        multiplier_str = multiplier_part[:-1]
                        has_percent_in_multiplier = True
                    else:
                        multiplier_str = multiplier_part
                        has_percent_in_multiplier = False
                    
                    try:
                        multiplier = float(multiplier_str)
                        if isinstance(base_value, (int, float)):
                            value = base_value * multiplier
                            if field.endswith('_cnt'):
                                value = int(value)
                            else:
                                value = round(value, 2)
                        else:
                            value = base_value
                    except ValueError:
                        print(f"❌ 无法解析乘数: {multiplier_str}")
                        value = base_value
                else:
                    value = base_value
                
                # 格式化输出
                if field in ['show_cnt', 'click_cnt', 'deep_convert_cnt']:
                    value_str = format_number_with_commas_int(str(int(value)))
                elif field == 'stat_cost':
                    value_str = format_number_with_commas(str(value))
                elif pattern.endswith('%') or has_percent_in_key:
                    value_str = str(value) + '%'
                else:
                    value_str = str(value)
                
                metrics_data[field] = value_str
            else:
                metrics_data[field] = pattern
        
        return metrics_data
    
    def update_status_fields(self, data, status_type):
        """递归更新状态字段 - 增强版"""
        if not isinstance(data, (dict, list)):
            return
        
        status_config = self.config['status_mappings'].get(status_type, {})
        
        # 安全获取配置项，确保它们是字典格式
        field_mappings = status_config.get('fields', {})
        if isinstance(field_mappings, list):
            # 如果是列表，转换为字典（设置为启用状态）
            field_mappings = {field: 1 for field in field_mappings}
        
        clear_fields = status_config.get('clear_fields', [])
        list_fields = status_config.get('list_fields', {})
        
        if isinstance(data, dict):
            # 更新字段值
            for field, value in field_mappings.items():
                if field in data:
                    data[field] = value
            
            # 清空指定字段
            for field in clear_fields:
                if field in data:
                    data[field] = []
            
            # 设置列表字段
            for field, value in list_fields.items():
                if field in data:
                    data[field] = value
            
            # 递归处理嵌套结构
            for value in data.values():
                self.update_status_fields(value, status_type)
        
        elif isinstance(data, list):
            for item in data:
                self.update_status_fields(item, status_type)
    
    def generic_status_update(self, flow: http.HTTPFlow):
        """通用状态更新处理 - 增强版"""
        if not hasattr(flow.response, 'text') or not flow.response.text:
            return
        
        # 检查是否包含需要修改的状态
        response_text = flow.response.text
        
        # 扩展的状态关键词检测
        status_keywords = [
            '"project_status_first_name":"未投放"',
            '"promotion_status_first_name":"未投放"',
            '"material_status_first_name":"未投放"',
            '"project_status_name":"暂停"',
            '"promotion_status_name":"已暂停"',
            '"promotion_status_name":"新建审核中"',
            '"material_status_name":"广告已暂停"',
            '"project_status":2',
            '"promotion_status":3',
            '"campaign_status":3',
            '"material_status":3',
            '"material_status":0',
            '未投放',
            '已暂停',
            '新建审核中',
            '广告已暂停'
        ]
        
        # 检查是否包含任何需要修改的状态
        has_status_to_update = any(keyword in response_text for keyword in status_keywords)
        
        # 额外检查：是否包含状态相关字段
        status_field_keywords = [
            'status_first_name',
            'status_name',
            'status":2',
            'status":3',
            'status_second'
        ]
        has_status_fields = any(keyword in response_text for keyword in status_field_keywords)
        
        if has_status_to_update or has_status_fields:
            try:
                response_data = json.loads(response_text)
                
                # 记录修改前的状态
                print(f"🔄 [新引擎] 通用状态更新 - 发现需要处理的状态")
                
                # 尝试所有类型的状态更新
                for status_type in ['project', 'promotion', 'material']:
                    self.update_status_fields(response_data, status_type)
                
                flow.response.set_text(json.dumps(response_data))
                print(f"✅ [新引擎] 通用状态更新完成")
                
            except Exception as e:
                print(f"❌ [新引擎] 通用状态更新失败: {e}")
                raise e 