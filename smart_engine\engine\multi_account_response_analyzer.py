#!/usr/bin/env python3
"""
📊 多账户响应分析器
从 multi_account_proxy_script.py 拆分出来的响应分析逻辑
保持文件长度在200行以内
"""

import json
from typing import Dict, Any, Optional
from mitmproxy import http
from smart_engine.utils.debug_logger import info_log, warning_log, error_log, debug_log, success_log


class MultiAccountResponseAnalyzer:
    """多账户响应分析器"""
    
    def __init__(self):
        """初始化响应分析器"""
        self.real_names = ["希赢贸易", "广州希赢贸易", "广州希赢", "初仔好得意"]
        self.virtual_names = ["测试广告主A", "测试广告主B", "测试广告主C", "测试广告主D"]
    
    def analyze_initial_response(self, flow: http.HTTPFlow) -> Dict[str, Any]:
        """分析初始响应状态"""
        analysis = {
            'is_oceanengine': False,
            'is_multi_account_api': False,
            'content_length': 0,
            'has_real_names': False,
            'has_virtual_names': False,
            'found_real_names': [],
            'found_virtual_names': []
        }
        
        try:
            url = flow.request.pretty_url
            
            # 检查是否是oceanengine API
            if 'oceanengine.com' in flow.request.pretty_host:
                analysis['is_oceanengine'] = True
                debug_log(f"🔍 [响应分析器] 处理响应: {url}")

                # 🔥 关键调试：记录响应的初始状态
                if flow.response and flow.response.content:
                    initial_content = flow.response.content.decode('utf-8')
                    analysis['content_length'] = len(initial_content)
                    debug_log(f"📏 [响应分析器] 初始响应长度: {len(initial_content)} 字符")

                    # 检查是否是多账户相关API
                    analysis['is_multi_account_api'] = any(keyword in url for keyword in [
                        'get_account_list', 'get_part_data', 'multi_account', 'account_list', 'org_with_account'
                    ])

                    if analysis['is_multi_account_api']:
                        info_log(f"🎯 [响应分析器] 发现多账户相关API: {url}")

                        # 记录初始响应中的账户信息
                        analysis['has_real_names'] = any(name in initial_content for name in self.real_names)
                        analysis['has_virtual_names'] = any(name in initial_content for name in self.virtual_names)

                        debug_log(f"🔍 [响应分析器] 初始响应包含真实账户名: {analysis['has_real_names']}")
                        debug_log(f"🔍 [响应分析器] 初始响应包含虚拟账户名: {analysis['has_virtual_names']}")

                        if analysis['has_real_names']:
                            analysis['found_real_names'] = [name for name in self.real_names if name in initial_content]
                            debug_log(f"📋 [响应分析器] 初始响应中的真实账户名: {analysis['found_real_names']}")

        except Exception as e:
            error_log(f"❌ [响应分析器] 初始响应分析失败: {e}")
        
        return analysis
    
    def analyze_response_content(self, flow: http.HTTPFlow) -> Optional[Dict[str, Any]]:
        """分析响应内容结构"""
        try:
            url = flow.request.pretty_url
            
            # 特别关注多账户相关API
            if any(keyword in url for keyword in ['multi_account', 'account_list', 'org_with_account']):
                info_log(f"🎯 [响应分析器] 发现多账户相关API: {url}")

                # 🔥 新增：记录响应内容摘要
                if flow.response and flow.response.content:
                    try:
                        response_data = json.loads(flow.response.content.decode('utf-8'))
                        if 'data' in response_data:
                            data = response_data['data']
                            if isinstance(data, dict):
                                info_log(f"📊 [响应分析器] 响应数据键: {list(data.keys())}")
                                if 'org_list' in data:
                                    org_count = len(data['org_list'])
                                    info_log(f"📊 [响应分析器] 发现 {org_count} 个组织")
                                    if org_count > 0 and 'account_list' in data['org_list'][0]:
                                        account_count = len(data['org_list'][0]['account_list'])
                                        info_log(f"📊 [响应分析器] 发现 {account_count} 个账户")
                                        
                                return {
                                    'data_keys': list(data.keys()),
                                    'org_count': org_count if 'org_list' in data else 0,
                                    'account_count': account_count if 'org_list' in data and org_count > 0 and 'account_list' in data['org_list'][0] else 0
                                }
                    except Exception as e:
                        debug_log(f"⚠️ [响应分析器] 响应解析失败: {e}")
            
            return None
        except Exception as e:
            error_log(f"❌ [响应分析器] 响应内容分析失败: {e}")
            return None
    
    def analyze_processing_result(self, flow: http.HTTPFlow, original_content: str) -> Dict[str, Any]:
        """分析处理结果"""
        analysis = {
            'content_changed': False,
            'has_real_names': False,
            'has_virtual_names': False,
            'found_real_names': [],
            'found_virtual_names': [],
            'content_length': 0
        }
        
        try:
            if flow.response and flow.response.content:
                after_content = flow.response.content.decode('utf-8')
                analysis['content_length'] = len(after_content)
                debug_log(f"   📏 调用后响应长度: {len(after_content)} 字符")

                # 检查是否包含虚拟账户名
                analysis['has_virtual_names'] = any(name in after_content for name in self.virtual_names)
                debug_log(f"   🔍 包含虚拟账户名: {analysis['has_virtual_names']}")
                if analysis['has_virtual_names']:
                    analysis['found_virtual_names'] = [name for name in self.virtual_names if name in after_content]
                    debug_log(f"   📋 发现的虚拟账户名: {analysis['found_virtual_names']}")

                # 检查内容是否真的发生了变化
                analysis['content_changed'] = original_content != after_content
                debug_log(f"   🔄 响应内容已变化: {analysis['content_changed']}")

        except Exception as e:
            error_log(f"❌ [响应分析器] 处理结果分析失败: {e}")
        
        return analysis
    
    def analyze_final_response(self, flow: http.HTTPFlow, url: str) -> Dict[str, Any]:
        """分析最终响应状态"""
        analysis = {
            'is_multi_account_api': False,
            'content_length': 0,
            'has_real_names': False,
            'has_virtual_names': False,
            'found_real_names': [],
            'found_virtual_names': [],
            'conversion_status': 'unknown'
        }
        
        try:
            if flow.response and flow.response.content:
                final_content = flow.response.content.decode('utf-8')
                analysis['content_length'] = len(final_content)

                # 检查是否是多账户相关API
                analysis['is_multi_account_api'] = any(keyword in url for keyword in [
                    'get_account_list', 'get_part_data', 'multi_account', 'account_list', 'org_with_account'
                ])

                if analysis['is_multi_account_api']:
                    debug_log(f"🔍 [响应分析器] 最终响应状态检查: {url}")
                    debug_log(f"📏 [响应分析器] 最终响应长度: {len(final_content)} 字符")

                    # 检查最终响应中的账户信息
                    analysis['has_real_names'] = any(name in final_content for name in self.real_names)
                    analysis['has_virtual_names'] = any(name in final_content for name in self.virtual_names)

                    debug_log(f"🔍 [响应分析器] 最终响应包含真实账户名: {analysis['has_real_names']}")
                    debug_log(f"🔍 [响应分析器] 最终响应包含虚拟账户名: {analysis['has_virtual_names']}")

                    if analysis['has_real_names']:
                        analysis['found_real_names'] = [name for name in self.real_names if name in final_content]
                        warning_log(f"⚠️ [响应分析器] 最终响应仍包含真实账户名: {analysis['found_real_names']}")

                    if analysis['has_virtual_names']:
                        analysis['found_virtual_names'] = [name for name in self.virtual_names if name in final_content]
                        success_log(f"✅ [响应分析器] 最终响应包含虚拟账户名: {analysis['found_virtual_names']}")

                    # 🔥 关键判断：如果最终响应仍包含真实账户名，说明转换失败
                    if analysis['has_real_names'] and not analysis['has_virtual_names']:
                        analysis['conversion_status'] = 'failed'
                        error_log(f"❌ [响应分析器] 响应转换失败！最终响应仍为真实数据")
                    elif analysis['has_virtual_names'] and not analysis['has_real_names']:
                        analysis['conversion_status'] = 'success'
                        success_log(f"✅ [响应分析器] 响应转换成功！最终响应为虚拟数据")
                    elif analysis['has_real_names'] and analysis['has_virtual_names']:
                        analysis['conversion_status'] = 'partial'
                        warning_log(f"⚠️ [响应分析器] 响应转换部分成功，同时包含真实和虚拟数据")
                    else:
                        analysis['conversion_status'] = 'no_account_data'
                        debug_log(f"🔍 [响应分析器] 响应不包含账户名称数据")

        except Exception as e:
            error_log(f"❌ [响应分析器] 最终响应分析失败: {e}")
        
        return analysis
    
    def log_before_entry_proxy(self, flow: http.HTTPFlow, url: str) -> str:
        """记录调用入口代理前的状态"""
        original_content = ""
        try:
            if flow.response and flow.response.content:
                original_content = flow.response.content.decode('utf-8')
                debug_log(f"   📏 调用前响应长度: {len(original_content)} 字符")

                # 检查是否包含真实账户名
                contains_real_names = any(name in original_content for name in self.real_names)
                debug_log(f"   🔍 包含真实账户名: {contains_real_names}")
                if contains_real_names:
                    found_names = [name for name in self.real_names if name in original_content]
                    debug_log(f"   📋 发现的真实账户名: {found_names}")
        except Exception as e:
            error_log(f"❌ [响应分析器] 调用前状态记录失败: {e}")
        
        return original_content
