import json
from mitmproxy import http

# 使用绝对导入
from smart_engine.utils.ad_utils import check_nested_dict_for_key

class ListHandler:
    """列表数据处理器 - 性能优化版"""
    
    def __init__(self, metrics_handler):
        """初始化列表处理器
        
        Args:
            metrics_handler: Metrics处理器实例
        """
        self.metrics_handler = metrics_handler
        # 🚀 添加缓存机制
        self._metrics_cache = {}
        self._status_cache = {}
    
    def _get_cached_metrics(self, template_type='basic'):
        """获取缓存的指标数据"""
        if template_type not in self._metrics_cache:
            self._metrics_cache[template_type] = self.metrics_handler.build_metrics_data(template_type)
        return self._metrics_cache[template_type]
    
    def _batch_update_status(self, items, status_type):
        """批量更新状态字段 - 性能优化"""
        if not items:
            return
        
        # 🚀 预计算状态映射
        if status_type not in self._status_cache:
            status_config = self.metrics_handler.config['status_mappings'].get(status_type, {})
            field_mappings = status_config.get('fields', {})
            if isinstance(field_mappings, list):
                field_mappings = {field: 1 for field in field_mappings}
            
            self._status_cache[status_type] = {
                'field_mappings': field_mappings,
                'clear_fields': status_config.get('clear_fields', []),
                'list_fields': status_config.get('list_fields', {})
            }
        
        cached_config = self._status_cache[status_type]
        
        # 🚀 批量处理，避免递归
        for item in items:
            if not isinstance(item, dict):
                continue
                
            # 更新字段值
            for field, value in cached_config['field_mappings'].items():
                if field in item:
                    item[field] = value
            
            # 清空指定字段
            for field in cached_config['clear_fields']:
                if field in item:
                    item[field] = []
            
            # 设置列表字段
            for field, value in cached_config['list_fields'].items():
                if field in item:
                    item[field] = value
    
    def modify_project_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目列表API - 性能优化版"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
            
            # 🚀 批量处理项目列表
            if "projects" in response_data["data"]:
                projects = response_data["data"]["projects"]
                
                # 批量更新状态
                self._batch_update_status(projects, 'project')
                
                # 批量添加metrics
                cached_metrics = self._get_cached_metrics()
                for project in projects:
                    if "metrics" in project and (not project["metrics"] or project["metrics"] == {}):
                        project["metrics"] = cached_metrics.copy()  # 使用浅拷贝
            
            # 处理总计metrics
            if "total_metrics" in response_data["data"] and (not response_data["data"]["total_metrics"] or response_data["data"]["total_metrics"] == {}):
                response_data["data"]["total_metrics"] = self._get_cached_metrics()
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 项目列表API修改完成 - 优化版")
        except Exception as e:
            print(f"❌ [新引擎] 修改项目列表API出错: {e}")
            raise e
    
    def modify_promotion_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告列表API - 性能优化版"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
            
            # 🚀 批量处理广告列表
            if "ads" in response_data["data"]:
                ads = response_data["data"]["ads"]
                
                # 批量更新状态
                self._batch_update_status(ads, 'promotion')
                
                # 批量添加metrics
                cached_metrics = self._get_cached_metrics()
                for ad in ads:
                    if "metrics" in ad and (not ad["metrics"] or ad["metrics"] == {}):
                        ad["metrics"] = cached_metrics.copy()  # 使用浅拷贝
            
            # 处理总计metrics
            if "total_metrics" in response_data["data"] and (not response_data["data"]["total_metrics"] or response_data["data"]["total_metrics"] == {}):
                response_data["data"]["total_metrics"] = self._get_cached_metrics()
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 广告列表API修改完成 - 优化版")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改广告列表API出错: {e}")
            raise e
    
    def modify_material_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改素材列表API - 性能优化版"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
            
            # 🚀 批量处理素材列表
            if "materials" in response_data["data"]:
                materials = response_data["data"]["materials"]
                
                # 批量更新状态
                self._batch_update_status(materials, 'material')
                
                # 批量添加metrics（包括深度转化数据）
                cached_metrics = self._get_cached_metrics('extended')
                for material in materials:
                    if "metrics" in material and (not material["metrics"] or material["metrics"] == {}):
                        material["metrics"] = cached_metrics.copy()  # 使用浅拷贝
            
            # 处理总计metrics
            if "total_metrics" in response_data["data"] and (not response_data["data"]["total_metrics"] or response_data["data"]["total_metrics"] == {}):
                response_data["data"]["total_metrics"] = self._get_cached_metrics('extended')
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 素材列表API修改完成 - 优化版")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改素材列表API出错: {e}")
            raise e 