#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
处理器路由验证测试 - 确保使用正确的专门处理器
"""

import unittest
import json
import logging
import io
import sys
import os
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
script_dir = os.path.dirname(__file__)
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)


class TestHandlerRouting(unittest.TestCase):
    """处理器路由验证测试套件"""
    
    def setUp(self):
        """测试设置"""
        # 设置日志捕获
        self.log_capture = io.StringIO()
        self.handler = logging.StreamHandler(self.log_capture)
        self.logger = logging.getLogger()
        self.logger.addHandler(self.handler)
        self.logger.setLevel(logging.INFO)
    
    def tearDown(self):
        """测试清理"""
        self.logger.removeHandler(self.handler)
        self.handler.close()
    
    def _create_realistic_mock_flow(self, url, response_data):
        """创建更真实的mock对象"""
        mock_flow = MagicMock()
        
        # 请求对象
        mock_flow.request.url = url
        mock_flow.request.pretty_url = url
        mock_flow.request.text = ""
        mock_flow.request.pretty_host = "ad.oceanengine.com"
        
        # 响应对象 - 关键：需要正确的content属性
        response_text = json.dumps(response_data)
        mock_flow.response.text = response_text
        mock_flow.response.content = response_text.encode('utf-8')
        
        # 确保hasattr检查通过
        mock_flow.response.set_text = MagicMock()
        
        return mock_flow
    
    def test_handler_method_resolution(self):
        """测试1：处理器方法解析是否正确"""
        print("\n🧪 测试1：处理器方法解析验证")
        
        from smart_engine.engine.api_handlers import APIHandlers
        from unittest.mock import MagicMock
        
        # 创建mock处理器来测试APIHandlers类
        mock_handlers = {
            'statistics_handler': MagicMock(),
            'balance_handler': MagicMock(),
            'list_handler': MagicMock(),
            'detail_handler': MagicMock(),
            'common_handler': MagicMock()
        }
        
        api_handlers = APIHandlers(
            mock_handlers['statistics_handler'],
            mock_handlers['balance_handler'],
            mock_handlers['list_handler'],
            mock_handlers['detail_handler'],
            mock_handlers['common_handler']
        )
        
        # 测试每个API类型都能找到对应的专门处理器
        test_cases = [
            {'handler': 'modify_balance', 'expected': True, 'desc': '余额处理器'},
            {'handler': 'modify_project_list', 'expected': True, 'desc': '项目列表处理器'},
            {'handler': 'modify_promotion_list', 'expected': True, 'desc': '广告列表处理器'},
            {'handler': 'modify_statistics', 'expected': True, 'desc': '统计处理器'},
            {'handler': 'modify_unknown', 'expected': True, 'desc': '未知API处理器'},
            {'handler': 'modify_project_detail', 'expected': True, 'desc': '项目详情处理器'},
            {'handler': 'modify_promotion_detail', 'expected': True, 'desc': '广告详情处理器'},
            {'handler': 'generic_handler', 'expected': True, 'desc': '通用处理器'},
            {'handler': 'non_existent_handler', 'expected': False, 'desc': '不存在的处理器'},
        ]
        
        for case in test_cases:
            handler_name = case['handler']
            expected = case['expected']
            desc = case['desc']
            
            # 验证方法解析
            has_method = hasattr(api_handlers, handler_name)
            self.assertEqual(has_method, expected, 
                           f"{desc} {handler_name} 解析不正确")
            
            if expected:
                print(f"  ✅ {desc}: {handler_name}")
            else:
                print(f"  ⭕ {desc}: {handler_name} (预期不存在)")
        
        print("✅ 处理器方法解析验证测试通过")
    
    def test_specific_handler_usage_balance(self):
        """测试2：验证余额API使用专门处理器"""
        print("\n🧪 测试2：余额API专门处理器验证")
        
        from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
        
        engine = SmartAPIEngine()
        
        # 创建余额API的真实mock对象
        mock_flow = self._create_realistic_mock_flow(
            url="https://ad.oceanengine.com/passport/account/info/v2/",
            response_data={
                "data": {
                    "advertiser_name": "测试账户",
                    "advertiser_id": "*********",
                    "balance": 1000.0,
                    "valid_balance": 1000.0
                },
                "code": 0,
                "message": "success"
            }
        )
        
        # 使用context manager重定向stdout以捕获print输出
        from contextlib import redirect_stdout
        
        output_buffer = io.StringIO()
        with redirect_stdout(output_buffer):
            engine.process_flow(mock_flow)
        
        output = output_buffer.getvalue()
        
        # 验证使用了专门的余额处理器
        self.assertIn("modify_balance", output, "应该使用专门的余额处理器")
        self.assertNotIn("使用通用处理器", output, "不应该降级到通用处理器")
        
        # 验证处理完成
        self.assertIn("处理完成", output, "处理应该成功完成")
        
        print("  ✅ 余额API使用了专门的modify_balance处理器")
        print("✅ 余额API专门处理器验证测试通过")
    
    def test_specific_handler_usage_statistics(self):
        """测试3：验证统计API使用专门处理器"""
        print("\n🧪 测试3：统计API专门处理器验证")
        
        from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
        
        engine = SmartAPIEngine()
        
        # 创建统计API的真实mock对象
        mock_flow = self._create_realistic_mock_flow(
            url="https://ad.oceanengine.com/report/api/statistic/customize_report/data",
            response_data={
                "data": {
                    "StatsData": {
                        "Totals": {
                            "stat_cost": {"Value": 100.0, "ValueStr": "100.0"},
                            "show_cnt": {"Value": 10000, "ValueStr": "10,000"},
                            "click_cnt": {"Value": 500, "ValueStr": "500"}
                        },
                        "Rows": [
                            {
                                "Metrics": {
                                    "stat_cost": {"Value": 50.0, "ValueStr": "50.0"},
                                    "show_cnt": {"Value": 5000, "ValueStr": "5,000"}
                                }
                            }
                        ]
                    }
                },
                "code": 0,
                "message": "success"
            }
        )
        
        # 捕获输出
        from contextlib import redirect_stdout
        
        output_buffer = io.StringIO()
        with redirect_stdout(output_buffer):
            engine.process_flow(mock_flow)
        
        output = output_buffer.getvalue()
        
        # 验证使用了专门的统计处理器
        self.assertIn("modify_statistics", output, "应该使用专门的统计处理器")
        self.assertNotIn("使用通用处理器", output, "不应该降级到通用处理器")
        
        print("  ✅ 统计API使用了专门的modify_statistics处理器")
        print("✅ 统计API专门处理器验证测试通过")
    
    def test_handler_fallback_mechanism(self):
        """测试4：验证处理器降级机制"""
        print("\n🧪 测试4：处理器降级机制验证")
        
        from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
        
        engine = SmartAPIEngine()
        
        # 测试不存在的处理器应该降级到通用处理器
        mock_flow = self._create_realistic_mock_flow(
            url="https://unknown.com/unknown/api",
            response_data={"data": {"test": "value"}}
        )
        
        # 模拟未知API
        with patch.object(engine.api_identifier, 'identify_api') as mock_identify:
            mock_identify.return_value = {
                'handler': 'non_existent_handler',
                'type': 'unknown'
            }
            
            # 捕获输出
            from contextlib import redirect_stdout
            
            output_buffer = io.StringIO()
            with redirect_stdout(output_buffer):
                engine.process_flow(mock_flow)
            
            output = output_buffer.getvalue()
            
            # 验证降级到通用处理器
            self.assertIn("通用处理完成", output, "应该降级到通用处理器")
            
        print("  ✅ 不存在的处理器正确降级到通用处理器")
        print("✅ 处理器降级机制验证测试通过")
    
    def test_api_handlers_integration(self):
        """测试5：API处理器集成验证"""
        print("\n🧪 测试5：API处理器集成验证")
        
        from smart_engine.engine.api_handlers import APIHandlers
        from unittest.mock import MagicMock
        
        # 创建mock处理器
        mock_handlers = {
            'statistics_handler': MagicMock(),
            'balance_handler': MagicMock(),
            'list_handler': MagicMock(),
            'detail_handler': MagicMock(),
            'common_handler': MagicMock()
        }
        
        api_handlers = APIHandlers(
            mock_handlers['statistics_handler'],
            mock_handlers['balance_handler'],
            mock_handlers['list_handler'],
            mock_handlers['detail_handler'],
            mock_handlers['common_handler']
        )
        
        # 测试每个方法都存在
        required_methods = [
            'modify_balance', 'modify_project_list', 'modify_promotion_list',
            'modify_statistics', 'modify_unknown', 'generic_handler',
            'modify_project_detail', 'modify_promotion_detail'
        ]
        
        for method_name in required_methods:
            self.assertTrue(hasattr(api_handlers, method_name),
                           f"APIHandlers缺少方法: {method_name}")
            
            # 验证方法可调用
            method = getattr(api_handlers, method_name)
            self.assertTrue(callable(method),
                           f"方法{method_name}不可调用")
            
            print(f"  ✅ {method_name}: 存在且可调用")
        
        print("✅ API处理器集成验证测试通过")
    
    def test_config_handler_consistency(self):
        """测试6：配置文件与处理器一致性验证"""
        print("\n🧪 测试6：配置文件与处理器一致性验证")
        
        try:
            # 加载API配置
            from smart_engine.utils.config_paths import get_api_rules_config_path
            config_path = get_api_rules_config_path()
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            from smart_engine.engine.api_handlers import APIHandlers
            from unittest.mock import MagicMock
            
            api_handlers = APIHandlers(
                statistics_handler=MagicMock(),
                balance_handler=MagicMock(),
                list_handler=MagicMock(),
                detail_handler=MagicMock(),
                common_handler=MagicMock()
            )
            
            # 验证配置中的每个handler都有对应的方法
            for api_name, api_config in config['api_patterns'].items():
                handler_name = api_config['handler']
                
                self.assertTrue(hasattr(api_handlers, handler_name),
                               f"配置中的handler {handler_name} 在APIHandlers中不存在")
                
                print(f"  ✅ {api_name}: {handler_name}")
            
            print("✅ 配置文件与处理器一致性验证测试通过")
            
        except FileNotFoundError:
            print("  ⚠️ 配置文件不存在，跳过一致性测试")
            self.skipTest("配置文件不存在")


def run_handler_routing_tests():
    """运行Handler路由验证测试套件"""
    print("🔄 运行Handler路由验证测试套件...")
    
    import unittest
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestHandlerRouting)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果统计
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    success_tests = total_tests - failures - errors
    
    print(f"\n📊 Handler路由验证测试结果统计:")
    print(f"   ✅ 成功: {success_tests}个")
    print(f"   ❌ 失败: {failures}个") 
    print(f"   💥 错误: {errors}个")
    print(f"   📈 总计: {total_tests}个")
    
    if failures == 0 and errors == 0:
        print(f"🎉 Handler路由验证测试全部通过!")
        return True
    else:
        print(f"⚠️ Handler路由验证测试存在问题")
        return False


if __name__ == '__main__':
    # 支持直接运行和通过run_tests.py调用
    success = run_handler_routing_tests()
    if not success:
        sys.exit(1) 