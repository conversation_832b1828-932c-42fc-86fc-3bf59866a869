#!/usr/bin/env python3
"""
🚪 多账户入口代理
简化的"入口层"模式，只在关键点插入处理逻辑

🎯 功能：
- 拦截账户列表API，替换显示名称
- 拦截账户跳转请求，映射虚拟ID到真实ID
- 为选中账户配置正确的AccountMapper
"""

import json
import os
from typing import Dict, Any, Optional
from mitmproxy import http
# 简化：移除复杂的配置管理器依赖
from smart_engine.utils.debug_logger import info_log, success_log, warning_log, error_log, debug_log


class MultiAccountEntryProxy:
    """
    多账户入口代理
    
    设计哲学：
    1. 只在两个关键点插入处理逻辑
    2. 一旦用户进入具体账户页面，完全交给原有的单账户引擎
    3. 不重新发明轮子，只做简单的映射
    """
    
    def __init__(self, config_path: str = "smart_engine/config/multi_account_mapping.json"):
        """
        初始化多账户入口代理

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.mappings = {}
        self.current_account_config = None  # 当前选中账户的配置
        self.current_account_id = None      # 当前选中账户ID

        # 简化：不再使用复杂的配置管理器

        # 加载配置
        self._load_mappings()

        # 初始化处理器
        self._init_handlers()

        # 简化：移除复杂的验证逻辑
        
    def _load_mappings(self):
        """加载多账户映射配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.mappings = config.get('mappings', {})
                    
                success_log(f"✅ [入口代理] 加载了 {len(self.mappings)} 个账户映射")
                for _, mapping in self.mappings.items():
                    info_log(f"   📋 {mapping['real_name']} → {mapping['virtual_name']}")
            else:
                warning_log(f"⚠️ [入口代理] 配置文件不存在: {self.config_path}")
                
        except Exception as e:
            error_log(f"❌ [入口代理] 配置加载失败: {e}")

    def _init_handlers(self):
        """初始化各种处理器"""
        try:
            from smart_engine.engine.multi_account_url_handler import MultiAccountUrlHandler
            from smart_engine.engine.multi_account_config_handler import MultiAccountConfigHandler
            from smart_engine.engine.multi_account_response_handler import MultiAccountResponseHandler
            from smart_engine.engine.multi_account_request_handler import MultiAccountRequestHandler
            from smart_engine.engine.multi_account_utils import MultiAccountUtils
            from smart_engine.engine.multi_account_data_processor import MultiAccountDataProcessor

            # 初始化URL处理器
            self.url_handler = MultiAccountUrlHandler(self.mappings)

            # 初始化配置处理器（简化版）
            self.config_handler = MultiAccountConfigHandler(self.mappings)

            # 初始化响应处理器
            self.response_handler = MultiAccountResponseHandler(
                self.mappings,
                self.url_handler,
                MultiAccountDataProcessor
            )

            # 初始化请求处理器
            self.request_handler = MultiAccountRequestHandler(
                self.mappings,
                self.url_handler,
                self.config_handler
            )

            # 初始化工具类
            self.utils = MultiAccountUtils(self.mappings)

            debug_log(f"✅ [入口代理] 处理器初始化完成")
        except Exception as e:
            error_log(f"❌ [入口代理] 处理器初始化失败: {e}")

    def handle_account_list_response(self, flow: http.HTTPFlow) -> bool:
        """
        处理账户列表响应，替换显示名称

        Args:
            flow: HTTP流对象

        Returns:
            bool: 是否处理了响应
        """
        return self.response_handler.handle_account_list_response(flow)

    def handle_account_redirect_request(self, flow: http.HTTPFlow) -> bool:
        """
        处理账户跳转请求，映射虚拟ID到真实ID并配置AccountMapper

        Args:
            flow: HTTP流对象

        Returns:
            bool: 是否处理了请求
        """
        result = self.request_handler.handle_account_redirect_request(flow)
        if result:
            # 同步当前账户ID
            self.current_account_id = self.request_handler.get_current_account_id()
        return result
    

    def get_current_account_config(self) -> Optional[Dict[str, Any]]:
        """获取当前选中账户的AccountMapper配置"""
        return self.current_account_config
    
    def get_account_config_by_id(self, account_id: str) -> Optional[Dict[str, Any]]:
        """根据账户ID获取专用配置"""
        return self.config_handler.get_account_config(account_id)
    
    def process_request(self, flow: http.HTTPFlow) -> bool:
        """
        处理HTTP请求
        
        Args:
            flow: HTTP流对象
            
        Returns:
            bool: 是否处理了请求
        """
        # 只处理账户跳转请求
        return self.handle_account_redirect_request(flow)
    
    def process_response(self, flow: http.HTTPFlow) -> bool:
        """
        处理HTTP响应
        
        Args:
            flow: HTTP流对象
            
        Returns:
            bool: 是否处理了响应
        """
        # 只处理账户列表响应
        return self.handle_account_list_response(flow)
    
    def extract_account_id_from_url(self, url: str) -> Optional[str]:
        """公共方法：从URL中提取账户ID（供代理脚本使用）"""
        return self.url_handler.extract_account_id_from_url(url)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.utils.get_stats()
    
    def get_all_account_mappings(self) -> Dict[str, Dict[str, str]]:
        """获取所有账户映射配置"""
        return self.utils.get_all_account_mappings()

    def configure_account(self, account_id: str) -> bool:
        """配置指定账户"""
        if self.config_handler.configure_account(account_id):
            # 设置当前账户
            self.current_account_id = account_id
            return True
        return False

    def handle_account_switch_request(self, flow: http.HTTPFlow) -> bool:
        """处理账户跳转请求"""
        result = self.request_handler.handle_account_switch_request(flow)
        if result:
            # 同步当前账户ID
            self.current_account_id = self.request_handler.get_current_account_id()
        return result