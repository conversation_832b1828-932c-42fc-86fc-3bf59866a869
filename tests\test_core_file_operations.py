#!/usr/bin/env python3
"""
🧪 核心文件操作测试模块
专门测试配置文件加载、保存等文件操作功能
文件长度限制：200行以内
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, mock_open

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器和要测试的模块
from tests.test_data_loader import test_data_loader
from smart_engine.utils import ad_utils

class TestCoreFileOperations(unittest.TestCase):
    """核心文件操作测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_metrics_file_operations(self):
        """测试指标文件操作"""
        print("\n🧪 测试指标文件操作")
        
        # 测试保存和加载
        test_data = {
            'test_metric': 123.45,
            'test_string': 'hello',
            'test_nested': {'inner': 'value'}
        }
        
        # 保存文件
        ad_utils.save_metrics_to_file(test_data, 'test_metrics.json')
        self.assertTrue(os.path.exists('test_metrics.json'))
        
        # 加载文件
        loaded_data = ad_utils.load_metrics_from_file('test_metrics.json')
        self.assertEqual(loaded_data['test_metric'], 123.45)
        self.assertEqual(loaded_data['test_string'], 'hello')
        self.assertEqual(loaded_data['test_nested']['inner'], 'value')
        
        # 测试文件不存在的情况
        nonexistent_data = ad_utils.load_metrics_from_file('nonexistent.json')
        self.assertEqual(nonexistent_data, {})
        
        print("✅ 指标文件操作测试通过")

    def test_config_loading_basic(self):
        """测试基础配置加载"""
        print("\n🧪 测试基础配置加载")
        
        # 测试正常加载
        metrics = ad_utils.load_metrics_from_file('metrics.json')
        self.assertEqual(metrics['Account Name'], 'WH-测试公司')
        self.assertEqual(metrics['Show Count'], 50000)
        self.assertEqual(metrics['CTR (%)'], 5.0)
        
        # 验证数据类型
        self.assertIsInstance(metrics['Show Count'], int)
        self.assertIsInstance(metrics['CTR (%)'], float)
        self.assertIsInstance(metrics['Account Name'], str)
        
        print("✅ 基础配置加载测试通过")

    def test_config_loading_edge_cases(self):
        """测试配置加载的边界情况"""
        print("\n🧪 测试配置加载边界情况")
        
        # 测试空文件
        with open('empty.json', 'w', encoding='utf-8') as f:
            f.write('{}')
        
        empty_data = ad_utils.load_metrics_from_file('empty.json')
        self.assertEqual(empty_data, {})
        
        # 测试无效JSON文件
        with open('invalid.json', 'w', encoding='utf-8') as f:
            f.write('invalid json content')
        
        # 应该返回空字典而不是抛出异常
        try:
            invalid_data = ad_utils.load_metrics_from_file('invalid.json')
            # 如果函数有错误处理，应该返回空字典
            self.assertEqual(invalid_data, {})
        except json.JSONDecodeError:
            # 如果函数没有错误处理，这是预期的异常
            pass
        
        # 测试权限问题（模拟）
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            try:
                permission_data = ad_utils.load_metrics_from_file('permission_test.json')
                # 如果有错误处理，应该返回空字典
                self.assertEqual(permission_data, {})
            except PermissionError:
                # 如果没有错误处理，这是预期的异常
                pass
        
        print("✅ 配置加载边界情况测试通过")

    def test_config_saving_edge_cases(self):
        """测试配置保存的边界情况"""
        print("\n🧪 测试配置保存边界情况")
        
        # 测试保存复杂数据结构
        complex_data = {
            'string': 'test',
            'number': 123,
            'float': 123.45,
            'boolean': True,
            'null': None,
            'list': [1, 2, 3, 'test'],
            'nested_dict': {
                'inner': 'value',
                'deep': {
                    'deeper': 'deep_value'
                }
            }
        }
        
        # 保存复杂数据
        ad_utils.save_metrics_to_file(complex_data, 'complex.json')
        self.assertTrue(os.path.exists('complex.json'))
        
        # 加载并验证
        loaded_complex = ad_utils.load_metrics_from_file('complex.json')
        self.assertEqual(loaded_complex['string'], 'test')
        self.assertEqual(loaded_complex['number'], 123)
        self.assertEqual(loaded_complex['float'], 123.45)
        self.assertEqual(loaded_complex['boolean'], True)
        self.assertEqual(loaded_complex['null'], None)
        self.assertEqual(loaded_complex['list'], [1, 2, 3, 'test'])
        self.assertEqual(loaded_complex['nested_dict']['inner'], 'value')
        self.assertEqual(loaded_complex['nested_dict']['deep']['deeper'], 'deep_value')
        
        # 测试保存空数据
        ad_utils.save_metrics_to_file({}, 'empty_save.json')
        self.assertTrue(os.path.exists('empty_save.json'))
        
        empty_loaded = ad_utils.load_metrics_from_file('empty_save.json')
        self.assertEqual(empty_loaded, {})
        
        print("✅ 配置保存边界情况测试通过")

    def test_file_encoding_handling(self):
        """测试文件编码处理"""
        print("\n🧪 测试文件编码处理")
        
        # 测试包含中文的数据
        chinese_data = {
            'account_name': '测试公司',
            'description': '这是一个包含中文的测试数据',
            'emoji': '🎯📊💼',
            'mixed': 'English + 中文 + 123'
        }
        
        # 保存包含中文的数据
        ad_utils.save_metrics_to_file(chinese_data, 'chinese.json')
        self.assertTrue(os.path.exists('chinese.json'))
        
        # 加载并验证中文数据
        loaded_chinese = ad_utils.load_metrics_from_file('chinese.json')
        self.assertEqual(loaded_chinese['account_name'], '测试公司')
        self.assertEqual(loaded_chinese['description'], '这是一个包含中文的测试数据')
        self.assertEqual(loaded_chinese['emoji'], '🎯📊💼')
        self.assertEqual(loaded_chinese['mixed'], 'English + 中文 + 123')
        
        print("✅ 文件编码处理测试通过")

    def test_large_file_handling(self):
        """测试大文件处理"""
        print("\n🧪 测试大文件处理")
        
        # 创建一个相对较大的数据结构
        large_data = {
            'large_list': list(range(1000)),  # 1000个数字
            'large_dict': {f'key_{i}': f'value_{i}' for i in range(100)},  # 100个键值对
            'nested_large': {
                f'level_{i}': {
                    f'inner_{j}': f'data_{i}_{j}' for j in range(10)
                } for i in range(10)
            }
        }
        
        # 保存大数据
        ad_utils.save_metrics_to_file(large_data, 'large.json')
        self.assertTrue(os.path.exists('large.json'))
        
        # 验证文件大小（应该大于1KB）
        file_size = os.path.getsize('large.json')
        self.assertGreater(file_size, 1024)  # 至少1KB
        
        # 加载并验证部分数据
        loaded_large = ad_utils.load_metrics_from_file('large.json')
        self.assertEqual(len(loaded_large['large_list']), 1000)
        self.assertEqual(len(loaded_large['large_dict']), 100)
        self.assertEqual(loaded_large['large_list'][0], 0)
        self.assertEqual(loaded_large['large_list'][999], 999)
        self.assertEqual(loaded_large['large_dict']['key_0'], 'value_0')
        
        print("✅ 大文件处理测试通过")

def run_core_file_operation_tests():
    """运行核心文件操作测试"""
    print("🚀 开始核心文件操作测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestCoreFileOperations)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 文件操作测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有文件操作测试通过！")
    else:
        print("❌ 部分文件操作测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_core_file_operation_tests()
