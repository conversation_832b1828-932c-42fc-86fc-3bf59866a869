#!/usr/bin/env python3
"""
🔍 通用映射服务
直接查询配置文件的虚拟→真实映射接口，保持数据源一致性
"""

import json
import os
from typing import Dict, Any, Optional, Set
from smart_engine.utils.debug_logger import debug_log, error_log, info_log


class UniversalMappingService:
    """通用映射服务 - 统一的配置文件查询接口"""
    
    def __init__(self, config_path: str = "smart_engine/config/multi_account_mapping.json"):
        self.config_path = config_path
        self._mappings_cache = None
        self._virtual_to_real_cache = None
        self._real_to_virtual_cache = None
        self._last_modified = None
        
    def _load_mappings(self) -> Dict[str, Any]:
        """加载配置文件映射"""
        try:
            if not os.path.exists(self.config_path):
                error_log(f"❌ [映射服务] 配置文件不存在: {self.config_path}")
                return {}
            
            # 检查文件修改时间，实现缓存
            current_modified = os.path.getmtime(self.config_path)
            if (self._mappings_cache is not None and 
                self._last_modified is not None and 
                current_modified == self._last_modified):
                return self._mappings_cache
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            mappings = config.get('mappings', {})
            
            # 更新缓存
            self._mappings_cache = mappings
            self._last_modified = current_modified
            self._build_lookup_caches()
            
            debug_log(f"🔧 [映射服务] 加载了 {len(mappings)} 个账户映射")
            return mappings
            
        except Exception as e:
            error_log(f"❌ [映射服务] 加载配置文件失败: {e}")
            return {}
    
    def _build_lookup_caches(self):
        """构建查找缓存以提高性能"""
        if not self._mappings_cache:
            return
        
        self._virtual_to_real_cache = {}
        self._real_to_virtual_cache = {}
        
        for real_id, mapping in self._mappings_cache.items():
            real_name = mapping.get('real_name', '')
            virtual_name = mapping.get('virtual_name', '')
            
            # 获取虚拟ID
            account_config = mapping.get('account_mapper_config', {}).get('account_mapping', {})
            virtual_account = account_config.get('virtual_account', {})
            virtual_id = virtual_account.get('id', f'VIRTUAL_{virtual_name.replace("测试广告主", "")}')
            
            # 构建虚拟→真实映射
            self._virtual_to_real_cache[virtual_id] = real_id
            self._virtual_to_real_cache[virtual_name] = real_name
            
            # 构建真实→虚拟映射
            self._real_to_virtual_cache[real_id] = virtual_id
            self._real_to_virtual_cache[real_name] = virtual_name
    
    def get_real_value(self, virtual_value: str) -> Optional[str]:
        """
        获取虚拟值对应的真实值
        
        Args:
            virtual_value: 虚拟ID或虚拟昵称
            
        Returns:
            Optional[str]: 对应的真实值，如果没有找到返回None
        """
        self._load_mappings()  # 确保数据是最新的
        
        if self._virtual_to_real_cache is None:
            return None
        
        return self._virtual_to_real_cache.get(virtual_value)
    
    def get_virtual_value(self, real_value: str) -> Optional[str]:
        """
        获取真实值对应的虚拟值
        
        Args:
            real_value: 真实ID或真实昵称
            
        Returns:
            Optional[str]: 对应的虚拟值，如果没有找到返回None
        """
        self._load_mappings()  # 确保数据是最新的
        
        if self._real_to_virtual_cache is None:
            return None
        
        return self._real_to_virtual_cache.get(real_value)
    
    def is_virtual_value(self, value: str) -> bool:
        """
        检查值是否为虚拟值
        
        Args:
            value: 要检查的值
            
        Returns:
            bool: 是否为虚拟值
        """
        self._load_mappings()
        
        if self._virtual_to_real_cache is None:
            return False
        
        return value in self._virtual_to_real_cache
    
    def is_real_value(self, value: str) -> bool:
        """
        检查值是否为真实值
        
        Args:
            value: 要检查的值
            
        Returns:
            bool: 是否为真实值
        """
        self._load_mappings()
        
        if self._real_to_virtual_cache is None:
            return False
        
        return value in self._real_to_virtual_cache
    
    def get_all_virtual_values(self) -> Set[str]:
        """
        获取所有虚拟值
        
        Returns:
            Set[str]: 所有虚拟ID和虚拟昵称
        """
        self._load_mappings()
        
        if self._virtual_to_real_cache is None:
            return set()
        
        return set(self._virtual_to_real_cache.keys())
    
    def get_all_real_values(self) -> Set[str]:
        """
        获取所有真实值
        
        Returns:
            Set[str]: 所有真实ID和真实昵称
        """
        self._load_mappings()
        
        if self._real_to_virtual_cache is None:
            return set()
        
        return set(self._real_to_virtual_cache.keys())
    
    def get_mapping_summary(self) -> Dict[str, Any]:
        """
        获取映射摘要信息
        
        Returns:
            Dict: 映射摘要
        """
        self._load_mappings()
        
        virtual_values = self.get_all_virtual_values()
        real_values = self.get_all_real_values()
        
        return {
            'total_mappings': len(self._mappings_cache) if self._mappings_cache else 0,
            'virtual_values_count': len(virtual_values),
            'real_values_count': len(real_values),
            'virtual_values': list(virtual_values),
            'real_values': list(real_values),
            'config_path': self.config_path,
            'last_modified': self._last_modified
        }
    
    def batch_convert_virtual_to_real(self, virtual_values: Set[str]) -> Dict[str, str]:
        """
        批量转换虚拟值为真实值
        
        Args:
            virtual_values: 虚拟值集合
            
        Returns:
            Dict[str, str]: 虚拟值→真实值映射
        """
        result = {}
        for virtual_value in virtual_values:
            real_value = self.get_real_value(virtual_value)
            if real_value:
                result[virtual_value] = real_value
        return result
    
    def batch_convert_real_to_virtual(self, real_values: Set[str]) -> Dict[str, str]:
        """
        批量转换真实值为虚拟值
        
        Args:
            real_values: 真实值集合
            
        Returns:
            Dict[str, str]: 真实值→虚拟值映射
        """
        result = {}
        for real_value in real_values:
            virtual_value = self.get_virtual_value(real_value)
            if virtual_value:
                result[real_value] = virtual_value
        return result
    
    def reload_config(self):
        """强制重新加载配置文件"""
        self._mappings_cache = None
        self._virtual_to_real_cache = None
        self._real_to_virtual_cache = None
        self._last_modified = None
        self._load_mappings()
        info_log("🔄 [映射服务] 配置文件已重新加载")


# 全局映射服务实例
universal_mapping_service = UniversalMappingService()


def get_real_value(virtual_value: str) -> Optional[str]:
    """
    便捷函数：获取虚拟值对应的真实值
    
    Args:
        virtual_value: 虚拟ID或虚拟昵称
        
    Returns:
        Optional[str]: 对应的真实值
    """
    return universal_mapping_service.get_real_value(virtual_value)


def get_virtual_value(real_value: str) -> Optional[str]:
    """
    便捷函数：获取真实值对应的虚拟值
    
    Args:
        real_value: 真实ID或真实昵称
        
    Returns:
        Optional[str]: 对应的虚拟值
    """
    return universal_mapping_service.get_virtual_value(real_value)


def is_virtual_value(value: str) -> bool:
    """
    便捷函数：检查值是否为虚拟值
    
    Args:
        value: 要检查的值
        
    Returns:
        bool: 是否为虚拟值
    """
    return universal_mapping_service.is_virtual_value(value)
