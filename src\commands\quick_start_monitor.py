#!/usr/bin/env python3
"""
🚀 快速启动API监控模式
简化版启动脚本，用于快速测试监控功能
"""
import os
import sys

def check_dependencies():
    """检查依赖"""
    try:
        import mitmproxy
        print("✅ mitmproxy 已安装")
        return True
    except ImportError:
        print("❌ 缺少 mitmproxy 依赖")
        print("请运行: pip install mitmproxy")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = [
        'data/api_logs',
        'smart_engine/config'
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"📁 创建目录: {dir_path}")

def start_monitor():
    """启动监控模式"""
    if not check_dependencies():
        return False
    
    create_directories()
    
    print("\n🔍 巨量引擎API监控系统")
    print("=" * 40)
    print("📊 功能:")
    print("  • 监控所有oceanengine.com的API")
    print("  • 自动分析API结构")
    print("  • 生成智能引擎配置")
    print("")
    
    # 确认启动
    confirm = input("是否启动监控模式? (y/n): ").lower().strip()
    if confirm not in ['y', 'yes', '是']:
        print("取消启动")
        return False
    
    try:
        print("\n🚀 启动中...")
        print("📡 代理端口: 8080")
        print("🌐 浏览器代理设置: 127.0.0.1:8080")
        print("🔗 访问: https://ad.oceanengine.com/")
        print("⏹️  按Ctrl+C停止")
        print("=" * 40)
        
        # 启动mitmproxy
        from mitmproxy.tools.main import mitmdump
        mitmdump([
            '-s', 'smart_engine/engine/smart_api_engine_with_monitor.py',
            '--listen-port', '8080'
        ])
        
    except KeyboardInterrupt:
        print("\n\n✅ 监控已停止")
        print("📊 查看生成的配置文件")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == "__main__":
    if start_monitor():
        print("\n🎉 监控会话完成!")
        print("📂 查看 smart_engine/config/ 目录下的配置文件")
    else:
        print("\n⚠️ 监控启动失败") 