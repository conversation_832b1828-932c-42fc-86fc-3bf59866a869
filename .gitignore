# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 项目特定文件
metrics.json
erro_response.txt

# 虚拟环境
venv/
ENV/
.env
.venv

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/

# 操作系统
.DS_Store
Thumbs.db
desktop.ini

# 日志文件
*.log
logs/
log/

# mitmproxy 特定文件
.mitmproxy/
mitmproxy-ca-cert.p12
mitmproxy-ca-cert.pem
mitmproxy-ca.p12
mitmproxy-ca.pem
mitmproxy-dhparam.pem 

.venv/

# 备份文件
*.backup_*
metrics.json.backup*
*_backup_*/
configs/accounts/backup/
smart_engine/config/backup/