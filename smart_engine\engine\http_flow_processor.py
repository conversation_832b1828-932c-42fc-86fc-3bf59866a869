"""
HTTP流处理器模块

负责HTTP请求和响应的处理逻辑，减少主引擎文件的复杂度
"""

from mitmproxy import http
from smart_engine.utils.ad_utils import load_metrics_from_file

class HTTPFlowProcessor:
    """HTTP流处理器"""
    
    def __init__(self, factory, initializer):
        """初始化HTTP流处理器"""
        self.factory = factory
        self.initializer = initializer
        
    def process_request_flow(self, engine, flow: http.HTTPFlow) -> None:
        """处理HTTP请求流 - 账户信息转换"""
        try:
            # 只处理oceanengine相关请求
            if 'oceanengine.com' not in flow.request.pretty_host:
                return
                
            print(f"🔍 [请求转换] 处理请求: {flow.request.pretty_url}")
            
            # 检查账户映射器状态
            if engine.account_mapper is None:
                print(f"⚠️  [请求转换] 账户映射器未激活，跳过转换")
                return
                
            # 执行请求转换：虚拟 → 真实
            print(f"🔄 [请求转换] 开始账户ID转换...")
            original_url = flow.request.pretty_url
            transformed = engine.account_mapper.transform_request(flow)
            new_url = flow.request.pretty_url
            
            if original_url != new_url:
                print(f"✅ [请求转换] 请求已转换:")
                print(f"   📤 原始: {original_url}")
                print(f"   📥 转换: {new_url}")
            else:
                print(f"ℹ️  [请求转换] 请求无需转换（无账户ID参数）")
                
        except (UnicodeDecodeError, UnicodeEncodeError):
            # 完全忽略编码错误
            pass
        except Exception as e:
            # 只记录非编码相关的错误
            if 'codec' not in str(e).lower() and 'decode' not in str(e).lower():
                print(f"❌ [请求转换] 处理异常: {e}")
                import traceback
                traceback.print_exc()
    
    def process_response_flow(self, engine, flow: http.HTTPFlow) -> None:
        """处理HTTP响应流 - 增强版（使用工厂模式）"""
        # 加载最新的metrics数据
        engine.metrics = load_metrics_from_file('metrics.json')
        
        # 使用初始化器按需创建处理器
        engine.handlers = self.initializer.initialize_handlers_if_needed(
            engine.handlers, engine.metrics
        )
        
        # 更新账户提取器
        if engine.account_extractor is None or engine.account_extractor.metrics != engine.metrics:
            engine.account_extractor = self.factory.create_account_extractor(
                engine.metrics, engine.account_mapper
            )
        
        # 🔍 自动提取真实账户信息（如果缺少映射配置且映射器未激活）
        if engine.account_mapper is None and 'account_mapping' not in engine.metrics:
            if engine.account_extractor.auto_extract_real_account(flow):
                # 如果提取成功，重新加载配置并初始化映射器
                engine.metrics = engine.account_extractor.metrics
                engine.account_mapper = self.factory.create_account_mapper(engine.metrics)
                # 更新账户提取器的映射器引用
                engine.account_extractor.account_mapper = engine.account_mapper
        
        # 识别API类型
        api_info = engine.api_identifier.identify_api(flow)
        
        # 🔄 先执行账户信息转换：真实 → 虚拟，确保后续处理器使用正确的账户信息
        engine.account_extractor.apply_response_account_mapping(flow)
        
        if api_info:
            # 调用对应的处理器（此时使用的是已转换的虚拟账户信息）
            handler_name = api_info.get('handler')
            api_handlers = engine.handlers.get('api_handlers')
            
            if api_handlers and hasattr(api_handlers, handler_name):
                try:
                    getattr(api_handlers, handler_name)(flow, api_info)
                    print(f"✅ [新引擎] {handler_name} 处理完成（基于虚拟账户信息）")
                except Exception as e:
                    print(f"❌ [新引擎] 处理API出错 [{handler_name}]: {e}")
                    api_handlers._log_error(flow, e)
            else:
                # 使用通用处理器
                if api_handlers:
                    api_handlers.generic_handler(flow, api_info)
                    print(f"✅ [新引擎] 通用处理完成（基于虚拟账户信息）")
        else:
            # 尝试通用状态更新
            metrics_handler = engine.handlers.get('metrics_handler')
            if metrics_handler:
                metrics_handler.generic_status_update(flow)
                print(f"ℹ️ [新引擎] 通用状态更新完成")

class HTTPFlowEncodingFixer:
    """HTTP流编码修复器"""
    
    @staticmethod
    def fix_flow_encoding(flow: http.HTTPFlow):
        """修复flow对象的编码问题"""
        try:
            # 修复请求编码
            if hasattr(flow, 'request') and flow.request:
                HTTPFlowEncodingFixer._fix_message_encoding(flow.request)
            
            # 修复响应编码  
            if hasattr(flow, 'response') and flow.response:
                HTTPFlowEncodingFixer._fix_message_encoding(flow.response)
        except:
            # 静默处理所有编码错误
            pass

    @staticmethod
    def _fix_message_encoding(message):
        """修复消息对象的编码问题"""
        try:
            # 如果有content但没有正确的text，尝试重新解码
            if hasattr(message, 'content') and message.content:
                if not hasattr(message, 'text') or not message.text:
                    # 尝试多种编码
                    for encoding in ['utf-8', 'gbk', 'latin-1']:
                        try:
                            decoded_text = message.content.decode(encoding)
                            # 成功解码后不设置text属性，让mitmproxy自己处理
                            break
                        except UnicodeDecodeError:
                            continue
        except:
            # 静默处理所有错误
            pass 