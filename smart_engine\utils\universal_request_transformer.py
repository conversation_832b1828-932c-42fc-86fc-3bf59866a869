#!/usr/bin/env python3
"""
🔄 通用请求转换器
能够处理所有HTTP请求中的虚拟→真实数据转换，不限制域名
"""

import json
import re
from typing import Dict, Set, Any, Optional
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from mitmproxy import http

from smart_engine.utils.universal_virtual_detector import universal_detector
from smart_engine.utils.universal_mapping_service import universal_mapping_service
from smart_engine.utils.debug_logger import debug_log, info_log, error_log


class UniversalRequestTransformer:
    """通用请求转换器"""
    
    def __init__(self):
        self.detector = universal_detector
        self.mapping_service = universal_mapping_service
        
    def transform_request(self, flow: http.HTTPFlow) -> bool:
        """
        转换HTTP请求中的所有虚拟数据为真实数据
        
        Args:
            flow: mitmproxy的HTTP流对象
            
        Returns:
            bool: 是否进行了转换
        """
        # 1. 检测请求中的虚拟数据
        detected_virtual_data = self.detector.detect_virtual_data_in_request(flow)
        
        if not detected_virtual_data['virtual_ids'] and not detected_virtual_data['virtual_names']:
            # 没有检测到虚拟数据，无需转换
            return False
        
        debug_log(f"🔍 [通用转换器] 检测到虚拟数据: {self.detector.get_detected_summary(flow)}")
        debug_log(f"🔍 [通用转换器] 处理请求: {flow.request.pretty_url}")
        
        # 2. 获取所有需要转换的映射
        all_virtual_values = detected_virtual_data['virtual_ids'] | detected_virtual_data['virtual_names']
        conversion_map = self.mapping_service.batch_convert_virtual_to_real(all_virtual_values)
        
        if not conversion_map:
            debug_log(f"⚠️ [通用转换器] 未找到有效的映射关系")
            return False
        
        debug_log(f"🔧 [通用转换器] 转换映射: {conversion_map}")
        
        # 3. 执行转换
        transformed = False
        
        # 转换URL参数
        if self._transform_url(flow, conversion_map):
            transformed = True
        
        # 转换请求体
        if self._transform_request_body(flow, conversion_map):
            transformed = True
        
        # 转换请求头
        if self._transform_headers(flow, conversion_map):
            transformed = True
        
        if transformed:
            info_log(f"✅ [通用转换器] 请求转换完成: {flow.request.pretty_url}")
        
        return transformed
    
    def _transform_url(self, flow: http.HTTPFlow, conversion_map: Dict[str, str]) -> bool:
        """转换URL中的虚拟数据"""
        original_url = flow.request.url
        transformed = False
        
        try:
            parsed_url = urlparse(original_url)
            
            # 转换查询参数
            if parsed_url.query:
                query_params = parse_qs(parsed_url.query, keep_blank_values=True)
                
                for param_name, param_values in query_params.items():
                    for i, value in enumerate(param_values):
                        if value in conversion_map:
                            old_value = value
                            new_value = conversion_map[value]
                            query_params[param_name][i] = new_value
                            transformed = True
                            info_log(f"🔄 [通用转换器] URL参数转换: {param_name}={old_value} → {new_value}")
                
                if transformed:
                    # 重建URL
                    new_query = urlencode(query_params, doseq=True)
                    new_parsed_url = parsed_url._replace(query=new_query)
                    new_url = urlunparse(new_parsed_url)
                    flow.request.url = new_url
                    debug_log(f"🔧 [通用转换器] URL已更新: {new_url}")
            
            # 转换URL路径中的虚拟数据
            path_transformed = False
            new_path = parsed_url.path
            for virtual_value, real_value in conversion_map.items():
                if virtual_value in new_path:
                    new_path = new_path.replace(virtual_value, real_value)
                    path_transformed = True
                    info_log(f"🔄 [通用转换器] URL路径转换: {virtual_value} → {real_value}")
            
            if path_transformed:
                new_parsed_url = parsed_url._replace(path=new_path)
                new_url = urlunparse(new_parsed_url)
                flow.request.url = new_url
                transformed = True
                debug_log(f"🔧 [通用转换器] URL路径已更新: {new_url}")
        
        except Exception as e:
            error_log(f"❌ [通用转换器] URL转换失败: {e}")
        
        return transformed
    
    def _transform_request_body(self, flow: http.HTTPFlow, conversion_map: Dict[str, str]) -> bool:
        """转换请求体中的虚拟数据"""
        if not flow.request.content:
            return False
        
        transformed = False
        
        try:
            # 获取请求体内容
            body_text = flow.request.content.decode('utf-8', errors='ignore')
            original_body = body_text
            
            # 尝试解析为JSON
            try:
                json_data = json.loads(body_text)
                json_transformed = self._transform_json_recursive(json_data, conversion_map)
                
                if json_transformed:
                    new_body_text = json.dumps(json_data, ensure_ascii=False, separators=(',', ':'))
                    flow.request.content = new_body_text.encode('utf-8')
                    transformed = True
                    info_log(f"🔄 [通用转换器] JSON请求体转换完成")
                    debug_log(f"🔧 [通用转换器] JSON转换详情: {len(conversion_map)} 个映射应用")
            
            except json.JSONDecodeError:
                # 不是JSON，进行文本替换
                new_body_text = body_text
                for virtual_value, real_value in conversion_map.items():
                    if virtual_value in new_body_text:
                        new_body_text = new_body_text.replace(virtual_value, real_value)
                        transformed = True
                        info_log(f"🔄 [通用转换器] 请求体文本转换: {virtual_value} → {real_value}")
                
                if transformed:
                    flow.request.content = new_body_text.encode('utf-8')
                    debug_log(f"🔧 [通用转换器] 请求体文本已更新")
        
        except Exception as e:
            error_log(f"❌ [通用转换器] 请求体转换失败: {e}")
        
        return transformed
    
    def _transform_json_recursive(self, data: Any, conversion_map: Dict[str, str]) -> bool:
        """递归转换JSON数据中的虚拟值"""
        transformed = False
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str) and value in conversion_map:
                    old_value = value
                    new_value = conversion_map[value]
                    data[key] = new_value
                    transformed = True
                    info_log(f"🔄 [通用转换器] JSON字段转换: {key}={old_value} → {new_value}")
                elif isinstance(value, (dict, list)):
                    if self._transform_json_recursive(value, conversion_map):
                        transformed = True
        
        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, str) and item in conversion_map:
                    old_value = item
                    new_value = conversion_map[item]
                    data[i] = new_value
                    transformed = True
                    info_log(f"🔄 [通用转换器] JSON数组转换: [{i}]={old_value} → {new_value}")
                elif isinstance(item, (dict, list)):
                    if self._transform_json_recursive(item, conversion_map):
                        transformed = True
        
        return transformed
    
    def _transform_headers(self, flow: http.HTTPFlow, conversion_map: Dict[str, str]) -> bool:
        """转换请求头中的虚拟数据"""
        transformed = False
        
        try:
            # 检查和转换请求头
            for header_name, header_value in flow.request.headers.items():
                new_header_value = header_value
                
                for virtual_value, real_value in conversion_map.items():
                    if virtual_value in new_header_value:
                        new_header_value = new_header_value.replace(virtual_value, real_value)
                        transformed = True
                        info_log(f"🔄 [通用转换器] 请求头转换: {header_name}: {virtual_value} → {real_value}")
                
                if new_header_value != header_value:
                    flow.request.headers[header_name] = new_header_value
        
        except Exception as e:
            error_log(f"❌ [通用转换器] 请求头转换失败: {e}")
        
        return transformed
    
    def get_transformation_summary(self, flow: http.HTTPFlow) -> Dict[str, Any]:
        """
        获取转换摘要信息
        
        Args:
            flow: mitmproxy的HTTP流对象
            
        Returns:
            Dict: 转换摘要
        """
        detected_virtual_data = self.detector.detect_virtual_data_in_request(flow)
        all_virtual_values = detected_virtual_data['virtual_ids'] | detected_virtual_data['virtual_names']
        conversion_map = self.mapping_service.batch_convert_virtual_to_real(all_virtual_values)
        
        return {
            'url': flow.request.pretty_url,
            'method': flow.request.method,
            'detected_virtual_ids': list(detected_virtual_data['virtual_ids']),
            'detected_virtual_names': list(detected_virtual_data['virtual_names']),
            'conversion_map': conversion_map,
            'has_virtual_data': len(all_virtual_values) > 0,
            'can_transform': len(conversion_map) > 0
        }


# 全局转换器实例
universal_transformer = UniversalRequestTransformer()


def transform_request(flow: http.HTTPFlow) -> bool:
    """
    便捷函数：转换HTTP请求中的虚拟数据
    
    Args:
        flow: mitmproxy的HTTP流对象
        
    Returns:
        bool: 是否进行了转换
    """
    return universal_transformer.transform_request(flow)
