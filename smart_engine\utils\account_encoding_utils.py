#!/usr/bin/env python3
"""
🔧 账户编码工具类
处理编码、URL参数和表单数据的相关功能

🎯 包含功能：
- 安全编码处理
- URL参数转换
- 表单数据转换
- 数据预览工具
"""

import json
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, parse_qs, urlencode


class AccountEncodingUtils:
    """账户编码工具类"""
    
    def __init__(self, real_account: Dict[str, Any], virtual_account: Dict[str, Any]):
        """初始化编码工具
        
        Args:
            real_account: 真实账户信息
            virtual_account: 虚拟账户信息
        """
        self.real_account = real_account
        self.virtual_account = virtual_account
    
    def safe_get_request_text(self, request):
        """安全获取请求文本，处理编码问题"""
        try:
            if hasattr(request, 'content') and request.content:
                # 尝试UTF-8解码
                try:
                    return request.content.decode('utf-8')
                except UnicodeDecodeError:
                    # 如果UTF-8失败，尝试其他编码
                    try:
                        return request.content.decode('gbk')
                    except UnicodeDecodeError:
                        try:
                            return request.content.decode('latin-1')
                        except UnicodeDecodeError:
                            return None
            # 避免访问可能有编码问题的text属性
        except Exception:
            pass
        return None
    
    def safe_set_request_text(self, request, text):
        """安全设置请求文本"""
        try:
            if hasattr(request, 'content'):
                request.content = text.encode('utf-8')
            if hasattr(request, 'text'):
                request.text = text
        except Exception:
            pass
    
    def get_sample_data(self, data: Any, max_depth: int = 2) -> Any:
        """获取数据样本，用于日志记录"""
        if max_depth <= 0:
            return "..."
        
        if isinstance(data, dict):
            sample = {}
            for i, (key, value) in enumerate(data.items()):
                if i >= 5:  # 只取前5个字段
                    sample["..."] = f"还有{len(data) - 5}个字段"
                    break
                if isinstance(value, (dict, list)):
                    sample[key] = self.get_sample_data(value, max_depth - 1)
                else:
                    sample[key] = self.safe_value_preview(value)
            return sample
        elif isinstance(data, list):
            if len(data) > 3:
                return [self.get_sample_data(data[0], max_depth - 1), "...", f"共{len(data)}个元素"]
            else:
                return [self.get_sample_data(item, max_depth - 1) for item in data]
        else:
            return self.safe_value_preview(data)
    
    def safe_value_preview(self, value: Any, max_length: int = 100) -> str:
        """安全的值预览，避免过长的字符串"""
        try:
            value_str = str(value)
            if len(value_str) > max_length:
                return value_str[:max_length] + "..."
            return value_str
        except:
            return f"<{type(value).__name__}>"
    
    def transform_url_params(self, flow) -> bool:
        """转换URL参数
        
        🎯 修复说明：支持多账户场景，检测URL中的任何已知虚拟ID并进行转换
        """
        # 使用项目日志系统
        from smart_engine.utils.debug_logger import debug_log, error_log, info_log
        
        # 🔧 修复：统一使用虚拟ID管理器获取所有账户映射
        try:
            from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
            from smart_engine.utils.virtual_id_manager import VirtualIdManager

            # 创建临时的入口代理来获取所有账户映射
            temp_proxy = MultiAccountEntryProxy()
            # 使用统一的虚拟ID管理器
            all_mappings = VirtualIdManager.get_all_standard_mappings(temp_proxy.mappings)

            debug_log(f"🔍 [多账户转换] 检测到 {len(all_mappings)} 个账户映射")

        except Exception as e:
            # 🚨 删除单账户模式回退，强制使用统一映射
            error_log(f"❌ [URL转换] 多账户映射获取失败: {e}")
            debug_log(f"🔧 [URL转换] 尝试从配置文件直接加载映射")

            # 直接从配置文件加载
            try:
                import json
                import os
                config_path = "smart_engine/config/multi_account_mapping.json"
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    mappings = config.get('mappings', {})
                    all_mappings = VirtualIdManager.get_all_standard_mappings(mappings)
                    debug_log(f"🔧 [URL转换] 从配置文件加载了 {len(all_mappings)} 个映射")
                else:
                    error_log(f"❌ [URL转换] 配置文件不存在，无法进行转换")
                    return False
            except Exception as e2:
                error_log(f"❌ [URL转换] 配置文件加载失败: {e2}")
                return False
        
        url_str = flow.request.url
        debug_log(f"🔍 [URL转换] 检查URL: {url_str}")
        
        # 🔧 检查URL中是否包含任何已知的虚拟ID
        found_mapping = None
        for virtual_id, mapping in all_mappings.items():
            if virtual_id and virtual_id in url_str:
                found_mapping = mapping
                debug_log(f"✅ [URL转换] 发现虚拟ID '{virtual_id}' 在URL中")
                break
        
        if not found_mapping:
            debug_log(f"ℹ️ [URL转换] URL中未包含任何已知虚拟ID，跳过转换")
            return False
            
        virtual_id = found_mapping['virtual_id']
        real_id = found_mapping['real_id']
        
        debug_log(f"🔄 [URL转换] 使用映射: {virtual_id} → {real_id}")
        
        transformed = False
        
        # 需要替换的参数名称
        id_params = ['aadvid', 'advertiser_id', 'account_id', 'user_id']
        
        # 解析查询参数
        parsed_url = urlparse(flow.request.url)
        
        if parsed_url.query:
            query_params = parse_qs(parsed_url.query)
            
            # 替换参数值
            for param_name in id_params:
                if param_name in query_params:
                    for i, value in enumerate(query_params[param_name]):
                        if value == virtual_id:
                            query_params[param_name][i] = real_id
                            transformed = True
                            info_log(f"🔄 [映射器] URL参数转换: {param_name}={virtual_id} → {real_id}")
            
            if transformed:
                # 重建URL
                new_query = urlencode(query_params, doseq=True)
                new_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
                if new_query:
                    new_url += f"?{new_query}"
                if parsed_url.fragment:
                    new_url += f"#{parsed_url.fragment}"
                    
                flow.request.url = new_url
        else:
            # 🔧 处理URL中直接包含虚拟ID但没有标准查询参数的情况
            if virtual_id in url_str:
                new_url = url_str.replace(virtual_id, real_id)
                if new_url != url_str:
                    flow.request.url = new_url
                    transformed = True
                    info_log(f"🔄 [映射器] URL直接替换: {virtual_id} → {real_id}")
            
        return transformed
    
    def transform_form_data(self, flow) -> bool:
        """转换表单数据 - 使用统一的虚拟ID映射"""
        try:
            body = self.safe_get_request_text(flow.request)
            if not body:
                return False

            # 🔧 修复：使用统一的虚拟ID管理器获取所有映射
            try:
                from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
                from smart_engine.utils.virtual_id_manager import VirtualIdManager

                temp_proxy = MultiAccountEntryProxy()
                all_mappings = VirtualIdManager.get_all_standard_mappings(temp_proxy.mappings)

            except Exception as e:
                # 直接从配置文件加载
                try:
                    import json
                    import os
                    config_path = "smart_engine/config/multi_account_mapping.json"
                    if os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        mappings = config.get('mappings', {})
                        all_mappings = VirtualIdManager.get_all_standard_mappings(mappings)
                    else:
                        return False
                except Exception:
                    return False

            transformed = False

            # 检查所有虚拟ID映射
            for virtual_id, mapping in all_mappings.items():
                real_id = mapping['real_id']
                if virtual_id and real_id and virtual_id in body:
                    new_body = body.replace(virtual_id, real_id)
                    if new_body != body:
                        body = new_body  # 更新body以支持多次替换
                        transformed = True
                        print(f"🔄 [映射器] 表单数据转换: {virtual_id} → {real_id}")

            if transformed:
                self.safe_set_request_text(flow.request, body)

            return transformed
        except Exception:
            return False

class AccountTransformUtils:
    """账户转换工具类 - 核心转换逻辑"""
    
    def __init__(self, real_account: Dict[str, Any], virtual_account: Dict[str, Any]):
        """初始化转换工具
        
        Args:
            real_account: 真实账户信息
            virtual_account: 虚拟账户信息
        """
        self.real_account = real_account
        self.virtual_account = virtual_account
        self.encoding_utils = AccountEncodingUtils(real_account, virtual_account)
    
    def transform_url_params(self, flow) -> bool:
        """转换URL参数：虚拟 → 真实"""
        return self.encoding_utils.transform_url_params(flow)
    
    def transform_form_data(self, flow) -> bool:
        """转换表单数据：虚拟 → 真实"""
        return self.encoding_utils.transform_form_data(flow)
    
    def is_request_account_field(self, field_name: str, request_fields: List[str]) -> bool:
        """判断字段是否是请求账户相关字段"""
        return field_name in request_fields
    
    def get_virtual_id(self) -> str:
        """获取虚拟账户ID"""
        return self.virtual_account.get('id', '')
    
    def get_real_id(self) -> str:
        """获取真实账户ID"""
        return self.real_account.get('id', '')
    
    def get_virtual_name(self) -> str:
        """获取虚拟账户名称"""
        return self.virtual_account.get('name', '')
    
    def get_real_name(self) -> str:
        """获取真实账户名称"""
        return self.real_account.get('name', '') 