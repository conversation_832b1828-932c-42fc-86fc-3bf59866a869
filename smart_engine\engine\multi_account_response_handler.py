#!/usr/bin/env python3
"""
📨 多账户响应处理器
从 multi_account_entry_proxy.py 拆分出来的响应处理逻辑
保持文件长度在200行以内
"""

import json
from typing import Dict, Any, Optional
from mitmproxy import http
from smart_engine.utils.debug_logger import info_log, debug_log, error_log, warning_log, success_log, process_log


class MultiAccountResponseHandler:
    """多账户响应处理器"""
    
    def __init__(self, mappings: Dict[str, Dict[str, Any]], url_handler, data_processor_class):
        """
        初始化响应处理器
        
        Args:
            mappings: 账户映射配置
            url_handler: URL处理器
            data_processor_class: 数据处理器类
        """
        self.mappings = mappings
        self.url_handler = url_handler
        self.data_processor_class = data_processor_class
    
    def handle_account_list_response(self, flow: http.HTTPFlow) -> bool:
        """
        处理账户列表响应，替换显示名称

        Args:
            flow: HTTP流对象

        Returns:
            bool: 是否处理了响应
        """
        try:
            # 🔥 增强调试：记录所有API调用
            url = flow.request.pretty_url
            debug_log(f"🔍 [响应处理器] 检查API: {url}")

            # 使用URL处理器检查API
            if self.url_handler.is_oceanengine_api(url):
                # 检查是否包含关键词
                found_keywords = self.url_handler.find_oceanengine_keywords(url)
                if found_keywords:
                    info_log(f"🔍 [响应处理器] Oceanengine API包含关键词 {found_keywords}: {url}")

                # 🔥 特别关注：记录所有business.oceanengine.com的API
                if self.url_handler.is_business_oceanengine_api(url):
                    info_log(f"🎯 [响应处理器] Business API: {url}")

                    # 如果有响应内容，记录响应结构
                    if flow.response and flow.response.content:
                        try:
                            response_data = json.loads(flow.response.content.decode('utf-8'))
                            if 'data' in response_data:
                                data_keys = list(response_data['data'].keys()) if isinstance(response_data['data'], dict) else 'non-dict'
                                info_log(f"📊 [响应处理器] 响应数据键: {data_keys}")
                        except:
                            pass

            # 🔥 增强调试：详细记录API匹配过程
            debug_log(f"🔍 [响应处理器] 开始检查API: {url}")

            # 使用URL处理器匹配API模式
            matched_pattern = self.url_handler.match_api_pattern(url)
            debug_log(f"🔍 [响应处理器] API模式匹配结果: {matched_pattern}")

            # 检查是否是多账户列表API
            is_account_list_api = matched_pattern is not None

            if is_account_list_api:
                info_log(f"🎯 [响应处理器] 匹配到多账户列表API: {url}")
                info_log(f"   📋 匹配的模式: {matched_pattern}")
            else:
                # 🔥 调试：检查是否是其他可能的多账户API
                found_keywords = self.url_handler.check_potential_multi_account_api(url)

                if found_keywords:
                    warning_log(f"⚠️ [响应处理器] 可能的多账户API但未匹配: {url}")
                    warning_log(f"   📋 发现的关键词: {found_keywords}")
                else:
                    debug_log(f"🔍 [响应处理器] 非多账户API，跳过处理: {url}")
                return False
            
            # 解析响应数据
            if not flow.response.content:
                return False
                
            response_data = json.loads(flow.response.content.decode('utf-8'))
            
            # 检查响应格式并处理不同的API格式
            data = response_data.get('data', {})
            modified = False

            # 创建数据处理器实例
            processor = self.data_processor_class(self.mappings)

            # 处理新格式：/nbs/api/bm/promotion/ad/get_account_list
            if 'data_list' in data:
                modified = self._handle_data_list_format(data, flow.request, processor)

            # 处理原格式：/platform/api/v1/bp/multi_accounts/org_with_account_list/
            elif 'org_list' in data:
                modified = self._handle_org_list_format(data, processor)

            # 🔥 新增：处理概览数据API /bp/api/promotion/promotion_common/get_overview_data
            elif ('/bp/api/promotion/promotion_common/get_overview_data' in url and
                  'list' in data and isinstance(data['list'], list)):
                modified = self._handle_overview_data_format(data, flow.request, processor)

            # 🔥 新增：处理新格式 /nbs/api/bm/promotion/get_part_data
            elif isinstance(data, dict) and all(key.isdigit() for key in data.keys() if key):
                modified = self._handle_part_data_format(data, processor)

            # 🔥 新增：处理消息通知API /nbs/api/bm/msg_center/notification/list_new_notifications
            elif 'notifications' in data and isinstance(data['notifications'], list):
                modified = self._handle_notifications_format(data, processor)

            else:
                # 未知格式，跳过处理
                debug_log(f"⚠️ [响应处理器] 未知的响应格式，跳过处理")
                return False
            
            if modified:
                return self._update_response_content(flow, response_data)
                
        except Exception as e:
            error_log(f"❌ [响应处理器] 账户列表响应处理失败: {e}")
            
        return False

    def _handle_data_list_format(self, data: dict, request, processor) -> bool:
        """处理data_list格式的响应"""
        # 🔥 新增：识别广告类型
        ad_type = processor.identify_ad_type_from_request(request)
        info_log(f"🎯 [响应处理器] 识别广告类型: {ad_type}")

        # 🔥 新增：聚合虚拟数据用于账户列表和概览
        aggregated_data = processor.aggregate_virtual_data_for_overview(ad_type)

        modified = False

        # 🔥 新增：处理概览数据聚合（多账户列表页面的汇总数据）
        if aggregated_data and 'overview' in aggregated_data:
            overview_data = aggregated_data['overview']
            modified = self._replace_overview_data_in_response(data, overview_data) or modified
            info_log(f"✅ [响应处理器] 概览数据聚合完成")

        # 处理账户列表数据
        for account in data['data_list']:
            account_id = str(account.get('advertiser_id', ''))
            if account_id in self.mappings:
                # 替换显示名称
                original_name = account.get('advertiser_name', '')
                virtual_name = self.mappings[account_id]['virtual_name']
                account['advertiser_name'] = virtual_name

                # 🔥 新增：替换账户的投放数据为虚拟数据
                if aggregated_data and 'accounts' in aggregated_data:
                    account_virtual_data = aggregated_data['accounts'].get(account_id)
                    if account_virtual_data:
                        modified = self._replace_account_performance_data(account, account_virtual_data) or modified

                # 🔥 新增：替换账户投放数据
                if aggregated_data and account_id in aggregated_data['accounts']:
                    account_virtual_data = aggregated_data['accounts'][account_id]
                    account.update({
                        'stat_cost': f"{account_virtual_data['cost']:.2f}",
                        'show_cnt': str(account_virtual_data['show']),
                        'click_cnt': str(account_virtual_data['click']),
                        'convert_cnt': str(account_virtual_data['convert']),
                        'conversion_cost': f"{account_virtual_data['conversion_cost']:.2f}",
                        'conversion_rate': f"{account_virtual_data['conversion_rate']:.2f}%",
                        'ctr': f"{account_virtual_data['ctr']:.2f}%",
                        'cpm_platform': f"{account_virtual_data['cpm']:.2f}"
                    })

                modified = True
                process_log(f"🔄 [响应处理器] 账户虚拟化: {original_name} → {virtual_name}")

        # 🔥 新增：处理概览数据 (total_metrics)
        if 'total_metrics' in data and aggregated_data:
            overview_data = aggregated_data['overview']
            data['total_metrics'].update({
                'stat_cost': f"{overview_data['cost']:.2f}",
                'show_cnt': str(overview_data['show']),
                'click_cnt': str(overview_data['click']),
                'convert_cnt': str(overview_data['convert']),
                'conversion_cost': f"{overview_data['conversion_cost']:.2f}",
                'conversion_rate': f"{overview_data['conversion_rate']:.2f}%",
                'ctr': f"{overview_data['ctr']:.2f}%",
                'cpm_platform': f"{overview_data['cpm']:.2f}"
            })
            modified = True
            success_log(f"✅ [响应处理器] 概览数据虚拟化完成: 消耗¥{overview_data['cost']} 展示{overview_data['show']:,} 点击{overview_data['click']:,} 转化{overview_data['convert']}")

        return modified

    def _replace_overview_data_in_response(self, data: dict, overview_data: dict) -> bool:
        """
        替换响应中的概览数据

        Args:
            data: 响应数据
            overview_data: 虚拟概览数据

        Returns:
            bool: 是否进行了替换
        """
        modified = False

        try:
            # 查找并替换概览相关字段
            overview_fields_mapping = {
                'stat_cost': 'cost',
                'show_cnt': 'show',
                'click_cnt': 'click',
                'convert_cnt': 'convert'
            }

            # 在data中查找概览数据字段并替换
            for response_field, overview_field in overview_fields_mapping.items():
                if response_field in data and overview_field in overview_data:
                    original_value = data[response_field]
                    new_value = overview_data[overview_field]
                    data[response_field] = new_value
                    process_log(f"🔄 [响应处理器] 概览字段 {response_field}: {original_value} → {new_value}")
                    modified = True

            # 处理可能的嵌套结构
            if 'summary' in data and isinstance(data['summary'], dict):
                for response_field, overview_field in overview_fields_mapping.items():
                    if response_field in data['summary'] and overview_field in overview_data:
                        original_value = data['summary'][response_field]
                        new_value = overview_data[overview_field]
                        data['summary'][response_field] = new_value
                        process_log(f"🔄 [响应处理器] 概览汇总字段 {response_field}: {original_value} → {new_value}")
                        modified = True

        except Exception as e:
            error_log(f"❌ [响应处理器] 概览数据替换失败: {e}")

        return modified

    def _replace_account_performance_data(self, account: dict, virtual_data: dict) -> bool:
        """
        替换单个账户的投放数据

        Args:
            account: 账户数据
            virtual_data: 虚拟投放数据

        Returns:
            bool: 是否进行了替换
        """
        modified = False

        try:
            # 投放数据字段映射
            performance_fields_mapping = {
                'stat_cost': 'cost',
                'show_cnt': 'show',
                'click_cnt': 'click',
                'convert_cnt': 'convert'
            }

            for response_field, virtual_field in performance_fields_mapping.items():
                if response_field in account and virtual_field in virtual_data:
                    original_value = account[response_field]
                    new_value = virtual_data[virtual_field]
                    account[response_field] = new_value
                    debug_log(f"🔄 [响应处理器] 账户 {account.get('advertiser_id', '')} {response_field}: {original_value} → {new_value}")
                    modified = True

        except Exception as e:
            error_log(f"❌ [响应处理器] 账户投放数据替换失败: {e}")

        return modified

    def _handle_org_list_format(self, data: dict, processor) -> bool:
        """处理org_list格式的响应"""
        modified = False
        for org in data['org_list']:
            if 'account_list' in org:
                # 🔥 新增：聚合虚拟投放数据
                aggregated_data = processor.aggregate_virtual_performance_data()

                for account in org['account_list']:
                    account_id = str(account.get('id', ''))
                    if account_id in self.mappings:
                        # 替换显示名称
                        original_name = account.get('name', '')
                        virtual_name = self.mappings[account_id]['virtual_name']
                        account['name'] = virtual_name

                        # 🔥 新增：添加虚拟投放数据
                        if aggregated_data and account_id in aggregated_data:
                            account_data = aggregated_data[account_id]
                            account.update({
                                'cost': account_data.get('cost', 0),
                                'show': account_data.get('show', 0),
                                'click': account_data.get('click', 0),
                                'convert': account_data.get('convert', 0),
                                'ctr': account_data.get('ctr', 0),
                                'cvr': account_data.get('cvr', 0),
                                'cpc': account_data.get('cpc', 0),
                                'cpm': account_data.get('cpm', 0)
                            })

                        modified = True
                        process_log(f"🔄 [响应处理器] 账户虚拟化: {original_name} → {virtual_name} (消耗:{account_data.get('cost', 0) if aggregated_data and account_id in aggregated_data else 0}元)")

        return modified

    def _handle_overview_data_format(self, data: dict, request, processor) -> bool:
        """处理概览数据格式的响应"""
        info_log(f"🔍 [响应处理器] 检测到概览数据API响应")

        # 识别广告类型
        ad_type = processor.identify_ad_type_from_request(request)
        info_log(f"🎯 [响应处理器] 概览数据广告类型: {ad_type}")

        # 获取虚拟概览数据
        virtual_overview = processor.get_virtual_overview_data(ad_type)
        if virtual_overview:
            # 替换概览数据
            for item in data['list']:
                field = item.get('field', '')
                if field in virtual_overview:
                    original_total = item.get('total', 0)
                    item['total'] = virtual_overview[field]

                    # 更新时间序列数据
                    if 'nowData' in item:
                        for point in item['nowData']:
                            point['value'] = virtual_overview[field] // len(item['nowData']) if len(item['nowData']) > 0 else 0
                    if 'lastData' in item:
                        for point in item['lastData']:
                            point['value'] = virtual_overview[field] // len(item['lastData']) if len(item['lastData']) > 0 else 0

                    process_log(f"🔄 [响应处理器] 概览字段 {field}: {original_total} → {virtual_overview[field]}")

            process_log(f"🔄 [响应处理器] 概览数据虚拟化完成")
            return True

        return False

    def _handle_part_data_format(self, data: dict, processor) -> bool:
        """处理part_data格式的响应"""
        info_log(f"🔍 [响应处理器] 检测到新格式多账户API响应")

        # 聚合虚拟投放数据
        aggregated_data = processor.aggregate_virtual_performance_data()

        modified = False
        for account_id in data.keys():
            if account_id in self.mappings and aggregated_data and account_id in aggregated_data:
                # 获取虚拟数据
                account_data = aggregated_data[account_id]

                # 更新账户数据
                data[account_id] = {
                    'stat_cost': account_data.get('cost', 0),
                    'show_cnt': account_data.get('show', 0),
                    'click_cnt': account_data.get('click', 0),
                    'convert_cnt': account_data.get('convert', 0),
                    'ctr': account_data.get('ctr', 0),
                    'conversion_rate': account_data.get('cvr', 0),
                    'cpc_platform': account_data.get('cpc', 0),
                    'cpm_platform': account_data.get('cpm', 0),
                    # 添加账户名称信息
                    'advertiser_name': self.mappings[account_id]['virtual_name'],
                    'advertiser_id': account_id
                }

                modified = True
                virtual_name = self.mappings[account_id]['virtual_name']
                process_log(f"🔄 [响应处理器] 新格式账户虚拟化: {account_id} → {virtual_name} (消耗:{account_data.get('cost', 0)}元)")

        return modified

    def _handle_notifications_format(self, data: dict, processor) -> bool:
        """处理通知格式的响应"""
        info_log(f"🔍 [响应处理器] 检测到消息通知API响应")

        modified = False
        # 处理每条通知中的账户名称
        for notification in data['notifications']:
            # 替换通知内容中的真实账户名
            if 'content' in notification:
                original_content = notification['content']
                modified_content = processor.replace_account_names_in_text(original_content)
                if modified_content != original_content:
                    notification['content'] = modified_content
                    modified = True

            # 替换发送者名称
            if 'fromUserName' in notification:
                original_name = notification['fromUserName']
                for account_id, mapping in self.mappings.items():
                    if mapping['real_name'] == original_name:
                        notification['fromUserName'] = mapping['virtual_name']
                        modified = True
                        break

        if modified:
            process_log(f"🔄 [响应处理器] 消息通知虚拟化完成")

        return modified

    def _update_response_content(self, flow: http.HTTPFlow, response_data: dict) -> bool:
        """更新响应内容"""
        try:
            # 🔥 增强调试：记录修改前后的响应内容
            original_content = flow.response.content.decode('utf-8') if flow.response.content else ""
            new_content = json.dumps(response_data, ensure_ascii=False)

            debug_log(f"🔍 [响应处理器] 响应修改详情:")
            debug_log(f"   📏 原始响应长度: {len(original_content)} 字符")
            debug_log(f"   📏 修改后响应长度: {len(new_content)} 字符")
            debug_log(f"   🔄 内容是否相同: {original_content == new_content}")

            # 记录关键字段的变化
            try:
                original_data = json.loads(original_content)
                if 'data' in original_data and 'data_list' in original_data['data']:
                    original_names = [item.get('advertiser_name', '') for item in original_data['data']['data_list']]
                    modified_names = [item.get('advertiser_name', '') for item in response_data['data']['data_list']]
                    debug_log(f"   📋 原始账户名: {original_names}")
                    debug_log(f"   📋 修改后账户名: {modified_names}")
            except Exception as e:
                debug_log(f"   ⚠️ 无法解析账户名变化: {e}")

            # 更新响应内容
            flow.response.content = new_content.encode('utf-8')

            # 🔥 验证响应内容是否真的被修改
            verification_content = flow.response.content.decode('utf-8')
            debug_log(f"🔍 [响应处理器] 响应修改验证:")
            debug_log(f"   ✅ 设置成功: {verification_content == new_content}")
            debug_log(f"   📏 验证长度: {len(verification_content)} 字符")

            # 🔥 额外验证：检查flow.response的其他属性
            if hasattr(flow.response, 'text'):
                debug_log(f"🔍 [响应处理器] 同时更新response.text属性")
                flow.response.text = new_content

            success_log(f"✅ [响应处理器] 账户列表响应处理完成")
            return True

        except Exception as e:
            error_log(f"❌ [响应处理器] 响应内容更新失败: {e}")
            return False
