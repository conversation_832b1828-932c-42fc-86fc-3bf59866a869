# 海洋引擎多账户API处理功能增强

## 📋 概述

本次更新为海洋引擎多账户页面增加了更全面的API响应虚拟化处理，确保所有显示投放数据的区域都能正确显示虚拟数据。

## 🎯 问题背景

之前系统只处理了部分API的响应（如 `get_account_list` 和 `get_part_data`），但多账户页面有多个显示投放数据的区域，每个区域对应不同的API，导致部分区域仍显示真实数据。

## 🔧 解决方案

### 新增API处理

1. **概览数据API**: `/bp/api/promotion/promotion_common/get_overview_data`
   - 处理多账户页面顶部的投放数据图表
   - 根据广告类型（通投/搜索）返回不同的虚拟数据
   - 支持时间序列数据的虚拟化

2. **消息通知API**: `/nbs/api/bm/msg_center/notification/list_new_notifications`
   - 处理消息通知列表中的账户名称
   - 替换通知内容中的真实账户名为虚拟名称
   - 替换发送者名称

### 新增辅助方法

1. **`_get_virtual_overview_data(ad_type)`**
   - 生成虚拟概览数据用于图表显示
   - 支持通投广告和搜索广告的不同数据策略
   - 搜索广告返回0数据（符合业务逻辑）

2. **`_replace_account_names_in_text(text)`**
   - 替换文本中的真实账户名为虚拟名称
   - 支持账户ID的虚拟化替换
   - 用于处理消息通知等文本内容

## 📊 处理逻辑

### 概览数据处理
```python
# 检测概览数据API响应
elif 'list' in data and isinstance(data['list'], list):
    # 识别广告类型
    ad_type = self._identify_ad_type_from_request(flow.request)
    
    # 获取虚拟概览数据
    virtual_overview = self._get_virtual_overview_data(ad_type)
    
    # 替换概览数据
    for item in data['list']:
        field = item.get('field', '')
        if field in virtual_overview:
            item['total'] = virtual_overview[field]
            # 更新时间序列数据
```

### 消息通知处理
```python
# 检测消息通知API响应
elif 'notifications' in data and isinstance(data['notifications'], list):
    # 处理每条通知中的账户名称
    for notification in data['notifications']:
        # 替换通知内容中的真实账户名
        if 'content' in notification:
            original_content = notification['content']
            modified_content = self._replace_account_names_in_text(original_content)
            if modified_content != original_content:
                notification['content'] = modified_content
```

## 🎯 虚拟数据策略

### 通投广告数据
- 消耗：1,460元
- 展示：45,280次
- 点击：892次
- 转化：23次
- 转化成本：63.48元
- 转化率：2.58%

### 搜索广告数据
- 所有指标均为0（符合业务实际情况）

## 📈 完整API覆盖

现在系统处理的API包括：

1. ✅ `/platform/api/v1/bp/multi_accounts/org_with_account_list/` - 账户组织列表
2. ✅ `/nbs/api/bm/promotion/ad/get_account_list` - 账户列表数据
3. ✅ `/nbs/api/bm/promotion/get_part_data` - 账户部分数据
4. ✅ `/bp/api/promotion/promotion_common/get_overview_data` - **新增：概览数据图表**
5. ✅ `/nbs/api/bm/msg_center/notification/list_new_notifications` - **新增：消息通知**

## 🔍 测试验证

### 验证要点
1. 多账户页面顶部图表显示虚拟数据
2. 消息通知中的账户名称已虚拟化
3. 不同广告类型标签页数据一致性
4. 时间序列图表数据正确性

### 测试方法
1. 访问多账户管理页面
2. 切换不同广告类型标签（全部/通投/搜索）
3. 检查顶部概览图表数据
4. 查看消息通知列表
5. 验证所有显示区域的数据一致性

## 📝 注意事项

1. **广告类型识别**：系统会自动识别请求的广告类型，为不同类型返回相应的虚拟数据
2. **数据一致性**：确保概览数据与列表数据的逻辑一致性
3. **文本替换**：消息通知中的账户名替换支持多种格式
4. **向后兼容**：新增功能不影响现有API处理逻辑

## 🚀 部署说明

1. 修改已应用到 `smart_engine/engine/multi_account_entry_proxy.py`
2. 无需额外配置，系统会自动识别和处理新增的API
3. 建议在测试环境验证后再部署到生产环境

## 📋 后续优化

1. 可根据实际需求调整虚拟数据的数值
2. 可扩展支持更多类型的API响应处理
3. 可增加更详细的日志记录用于调试

---

**更新时间**: 2025-06-17  
**版本**: v1.2.0  
**状态**: ✅ 已完成
