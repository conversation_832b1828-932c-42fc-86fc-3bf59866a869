#!/usr/bin/env python3
"""
🔍 多账户API调试工具
专门用于调试多账户列表API的拦截问题
"""
import sys
import os
import json
from mitmproxy import http

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy

# 全局变量
entry_proxy = None
api_call_count = 0

def load_debug_system():
    """加载调试系统"""
    global entry_proxy
    
    try:
        print("🔍 多账户API调试系统初始化...")
        entry_proxy = MultiAccountEntryProxy()
        print(f"✅ 调试系统初始化完成，支持{len(entry_proxy.mappings)}个账户")
        return True
    except Exception as e:
        print(f"❌ 调试系统初始化失败: {e}")
        return False

def request(flow: http.HTTPFlow) -> None:
    """处理HTTP请求 - 调试版"""
    global entry_proxy, api_call_count
    
    if entry_proxy is None:
        if not load_debug_system():
            return
    
    # 只记录oceanengine相关的请求
    if 'oceanengine.com' in flow.request.pretty_host:
        api_call_count += 1
        url = flow.request.pretty_url
        
        print(f"\n🔍 [调试] API请求 #{api_call_count}")
        print(f"   URL: {url}")
        print(f"   方法: {flow.request.method}")
        print(f"   主机: {flow.request.pretty_host}")
        
        # 检查是否包含多账户相关关键词
        keywords = ['multi_account', 'account_list', 'org_with_account', 'get_account_list']
        for keyword in keywords:
            if keyword in url:
                print(f"   🎯 包含关键词: {keyword}")
                break

def response(flow: http.HTTPFlow) -> None:
    """处理HTTP响应 - 调试版"""
    global entry_proxy
    
    if entry_proxy is None:
        return
    
    # 只处理oceanengine相关的响应
    if 'oceanengine.com' in flow.request.pretty_host:
        url = flow.request.pretty_url
        
        print(f"\n📥 [调试] API响应")
        print(f"   URL: {url}")
        print(f"   状态码: {flow.response.status_code}")
        print(f"   内容长度: {len(flow.response.content) if flow.response.content else 0}")
        
        # 检查是否是多账户列表API
        is_multi_account_api = (
            '/platform/api/v1/bp/multi_accounts/org_with_account_list/' in url or
            '/nbs/api/bm/promotion/ad/get_account_list' in url or
            'multi_account' in url or
            'account_list' in url
        )
        
        if is_multi_account_api:
            print(f"   🎯 [重要] 这是多账户相关API！")
            
            # 尝试解析响应内容
            try:
                if flow.response.content:
                    response_data = json.loads(flow.response.content.decode('utf-8'))
                    print(f"   📊 响应数据结构:")
                    print(f"      - 顶级键: {list(response_data.keys())}")
                    
                    if 'data' in response_data:
                        data = response_data['data']
                        print(f"      - data键: {list(data.keys()) if isinstance(data, dict) else type(data)}")
                        
                        # 检查是否包含账户列表
                        if isinstance(data, dict):
                            if 'org_list' in data:
                                print(f"      - 发现org_list!")
                                org_list = data['org_list']
                                if org_list and len(org_list) > 0:
                                    first_org = org_list[0]
                                    if 'account_list' in first_org:
                                        account_list = first_org['account_list']
                                        print(f"      - 账户数量: {len(account_list)}")
                                        if account_list:
                                            first_account = account_list[0]
                                            print(f"      - 第一个账户字段: {list(first_account.keys())}")
                                            print(f"      - 账户名称: {first_account.get('name', 'N/A')}")
                                            print(f"      - 账户ID: {first_account.get('id', 'N/A')}")
                            
                            elif 'data_list' in data:
                                print(f"      - 发现data_list!")
                                data_list = data['data_list']
                                print(f"      - 账户数量: {len(data_list)}")
                                if data_list:
                                    first_account = data_list[0]
                                    print(f"      - 第一个账户字段: {list(first_account.keys())}")
                                    print(f"      - 账户名称: {first_account.get('advertiser_name', 'N/A')}")
                                    print(f"      - 账户ID: {first_account.get('advertiser_id', 'N/A')}")
                    
                    # 🔥 关键测试：尝试调用入口代理处理
                    print(f"   🧪 测试入口代理处理...")
                    result = entry_proxy.handle_account_list_response(flow)
                    if result:
                        print(f"   ✅ 入口代理成功处理了响应！")
                    else:
                        print(f"   ❌ 入口代理没有处理响应")
                        
            except Exception as e:
                print(f"   ❌ 响应解析失败: {e}")
        
        # 检查是否包含账户ID参数
        if 'aadvid=' in url:
            import re
            aadvid_match = re.search(r'aadvid=(\d+)', url)
            if aadvid_match:
                account_id = aadvid_match.group(1)
                print(f"   🔑 包含账户ID: {account_id}")
                if account_id in entry_proxy.mappings:
                    mapping = entry_proxy.mappings[account_id]
                    print(f"   🎭 映射信息: {mapping['real_name']} → {mapping['virtual_name']}")

def print_summary():
    """打印调试摘要"""
    print(f"\n📊 调试摘要:")
    print(f"   总API调用数: {api_call_count}")
    if entry_proxy:
        print(f"   支持的账户数: {len(entry_proxy.mappings)}")
        for account_id, mapping in entry_proxy.mappings.items():
            print(f"   - {mapping['real_name']} → {mapping['virtual_name']} (ID: {account_id})")

if __name__ == "__main__":
    print("🔍 多账户API调试工具")
    print("=" * 60)
    print("使用方法:")
    print("mitmdump -s tools/debug_multi_account_api.py --listen-port 8080")
    print("然后在浏览器中访问多账户页面")
    print("=" * 60)
