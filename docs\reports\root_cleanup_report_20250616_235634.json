{"cleanup_summary": {"completed_at": "2025-06-16T23:56:34.406016", "total_deleted_files": 16, "total_moved_files": 1, "total_deleted_directories": 11, "total_kept_files": 8, "total_errors": 0}, "deleted_files": ["cleanup_report_20250616_213324.json", "cleanup_report_20250616_213630.json", "config_optimization_report_20250616_231337.json", "documentation_organization_report_20250616_233910.json", "erro_response.txt", "quick_cleanup.py", "quick_test_diagnosis.py", "simple_test_fix.py", "test_account_mapper_activation.py", "test_account_mapper_debug.py", "test_account_switch_fix.py", "test_intelligent_account_detection.py", "test_new_structure.py", "test_response_transform_fix.py", "test_simple_detection.py", "verify_detection_logic.py"], "moved_files": [{"from": "script/scan_large_files.py", "to": "tools/scan_large_files.py"}], "deleted_directories": ["launchers/", "script/", "__pycache__", ".venv\\Lib\\site-packages\\win32\\lib\\__pycache__", ".venv\\Lib\\site-packages\\_distutils_hack\\__pycache__", "smart_engine\\__pycache__", "smart_engine\\engine\\__pycache__", "smart_engine\\utils\\__pycache__", "src\\__pycache__", "src\\launchers\\__pycache__", "tests\\__pycache__"], "kept_files": [".cursorinde<PERSON><PERSON><PERSON>", ".giti<PERSON>re", "cleanup_script.py", "config_version.json", "launcher.py", "metrics.json", "README.md", "requirements.txt"], "errors": [], "final_root_structure": ["📁 .cursor/", "📁 .git/", "📁 .pytest_cache/", "📁 .specstory/", "📁 .venv/", "📁 config/", "📁 configs/", "📁 data/", "📁 docs/", "📁 smart_engine/", "📁 src/", "📁 tests/", "📁 tools/", "📄 .cursorinde<PERSON><PERSON><PERSON>", "📄 .gitignore", "📄 README.md", "📄 cleanup_script.py", "📄 config_version.json", "📄 launcher.py", "📄 metrics.json", "📄 requirements.txt"]}