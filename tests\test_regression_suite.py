#!/usr/bin/env python3
"""
🧪 回归测试套件类 - 第一部分
从test_regression.py拆分出来的基础测试，符合200行文件长度限制
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock, mock_open

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.utils import ad_utils

class TestRegressionSuite(unittest.TestCase):
    """回归测试主套件 - 第一部分测试（测试1-4）"""
    
    def setUp(self):
        """测试前准备 - 使用数据驱动的测试数据"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 🆕 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json（保持向后兼容）
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 🆕 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_01_config_loading(self):
        """测试1：配置系统 - 配置文件加载和处理"""
        print("\n🧪 测试1：配置系统")
        
        # 测试正常加载
        metrics = ad_utils.load_metrics_from_file('metrics.json')
        self.assertEqual(metrics['Account Name'], 'WH-测试公司')
        self.assertEqual(metrics['Show Count'], 50000)
        
        # 测试文件不存在时的默认处理
        metrics_empty = ad_utils.load_metrics_from_file('nonexistent.json')
        self.assertEqual(metrics_empty, {})
        
        # 测试保存功能
        test_data = {'test_key': 'test_value'}
        ad_utils.save_metrics_to_file(test_data, 'test_save.json')
        loaded_data = ad_utils.load_metrics_from_file('test_save.json')
        self.assertEqual(loaded_data['test_key'], 'test_value')
        
        print("✅ 配置系统测试通过")

    def test_02_ad_metrics_calculation(self):
        """测试2：广告指标计算功能 - 数据驱动版本"""
        print("\n🧪 测试2：广告指标计算（数据驱动）")
        
        # 🆕 获取所有测试用例
        test_cases = test_data_loader.get_test_metrics()
        
        for test_case in test_cases:
            case_name = test_case.get('name', 'unknown')
            input_data = test_case.get('input', {})
            expected = test_case.get('expected', {})
            
            print(f"  🔍 测试场景: {case_name}")
            
            # 执行计算
            metrics = ad_utils.calculate_ad_metrics(
                input_data.get('show_cnt', 0),
                input_data.get('click_cnt', 0), 
                input_data.get('convert_cnt', 0),
                input_data.get('stat_cost', 0.0)
            )
            
            # 验证计算结果
            for key, expected_value in expected.items():
                self.assertEqual(metrics[key], expected_value, 
                    f"场景 {case_name}: {key} 期望 {expected_value}, 实际 {metrics[key]}")
            
            # 验证比较数据生成（非零值情况）
            if input_data.get('show_cnt', 0) > 0:
                self.assertIn('Comparison Data', metrics)
                self.assertIn('CTR', metrics['Comparison Data'])
        
        print("✅ 广告指标计算测试通过（所有数据驱动场景）")

    def test_03_utility_functions(self):
        """测试3：工具函数"""
        print("\n🧪 测试3：工具函数")
        
        # 测试嵌套字典查找
        nested_dict = {
            'level1': {
                'level2': {
                    'target_key': 'found'
                }
            },
            'other_key': 'value'
        }
        
        self.assertTrue(ad_utils.check_nested_dict_for_key(nested_dict, 'target_key'))
        self.assertTrue(ad_utils.check_nested_dict_for_key(nested_dict, 'level2'))
        self.assertFalse(ad_utils.check_nested_dict_for_key(nested_dict, 'nonexistent'))
        
        # 测试数字格式化
        formatted_int = ad_utils.format_number_with_commas_int("12345")
        self.assertEqual(formatted_int, "12,345")
        
        formatted_float = ad_utils.format_number_with_commas("12345.67")
        self.assertEqual(formatted_float, "12,345.67")
        
        # 测试无效输入
        invalid_result = ad_utils.format_number_with_commas("invalid")
        self.assertEqual(invalid_result, "Invalid input")
        
        print("✅ 工具函数测试通过")

    def test_04_ratio_generation(self):
        """测试4：涨幅数据生成"""
        print("\n🧪 测试4：涨幅数据生成")
        
        # 测试不同指标的涨幅生成
        ctr_ratio = ad_utils.generate_ratio_and_ratio_str('CTR (%)', 5.0)
        self.assertIn('Value', ctr_ratio)
        self.assertIn('Ratio', ctr_ratio)
        self.assertIn('RatioStr', ctr_ratio)
        self.assertTrue(ctr_ratio['Ratio'] > 0)  # CTR应该是正向增长
        
        # 测试成本类指标（应该是负向，即降低）
        cost_ratio = ad_utils.generate_ratio_and_ratio_str('Conversion Cost', 2.5)
        self.assertTrue(cost_ratio['Ratio'] < 0)  # 转化成本应该降低
        
        # 测试CPC类指标
        cpc_ratio = ad_utils.generate_ratio_and_ratio_str('CPC', 0.5)
        self.assertTrue(cpc_ratio['Ratio'] < 0)  # CPC应该降低
        
        # 测试消耗类指标
        cost_total_ratio = ad_utils.generate_ratio_and_ratio_str('Stat Cost', 100.0)
        self.assertTrue(cost_total_ratio['Ratio'] > 0)  # 总消耗应该增长
        
        print("✅ 涨幅数据生成测试通过") 