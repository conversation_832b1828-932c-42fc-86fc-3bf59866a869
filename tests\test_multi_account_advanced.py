#!/usr/bin/env python3
"""
🧪 多账户系统高级测试模块
专门测试账户检测、配置隔离、并发操作、错误处理等高级功能
文件长度限制：200行以内
"""

import os
import sys
import json
import unittest
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
try:
    from smart_engine.utils.multi_account_config_manager import MultiAccountConfigManager
except ImportError as e:
    print(f"⚠️ 导入多账户模块失败: {e}")
    MultiAccountConfigManager = None

class TestMultiAccountAdvanced(unittest.TestCase):
    """多账户系统高级功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建测试用的configs目录结构
        os.makedirs('configs/accounts', exist_ok=True)
        
        # 从测试数据加载器获取测试账户
        self.test_accounts = test_data_loader.get_test_accounts()
        
        # 创建测试账户配置文件
        for account in self.test_accounts:
            account_id = account.get('real_account', {}).get('id')
            if account_id:
                config_file = f'configs/accounts/metrics_{account_id}.json'
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        'Account Name': account.get('virtual_account', {}).get('name', 'Test Account'),
                        'Account ID': account.get('virtual_account', {}).get('id', '123456'),
                        'account_mapping': {
                            'real_account': account.get('real_account', {}),
                            'virtual_account': account.get('virtual_account', {})
                        }
                    }, f, ensure_ascii=False, indent=2)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_account_detection_from_response(self):
        """测试从响应检测账户ID"""
        print("\n🧪 测试从响应检测账户ID")
        
        # 模拟不同的API响应
        test_responses = [
            {
                'name': '余额API响应',
                'data': {
                    'data': {
                        'advertiser_id': '****************',
                        'advertiser_name': '希赢贸易',
                        'balance': 1000.0
                    }
                },
                'expected_id': '****************'
            },
            {
                'name': '统计API响应',
                'data': {
                    'data': {
                        'list': [
                            {
                                'advertiser_id': '****************',
                                'stat_cost': 100.0
                            }
                        ]
                    }
                },
                'expected_id': '****************'
            },
            {
                'name': '无账户ID的响应',
                'data': {
                    'data': {
                        'message': 'success'
                    }
                },
                'expected_id': None
            }
        ]
        
        for test_case in test_responses:
            print(f"  🔍 测试: {test_case['name']}")
            
            response_data = test_case['data']
            expected_id = test_case['expected_id']
            
            # 简单的ID提取逻辑测试
            extracted_id = None
            
            # 检查data.advertiser_id
            if 'data' in response_data and isinstance(response_data['data'], dict):
                if 'advertiser_id' in response_data['data']:
                    extracted_id = response_data['data']['advertiser_id']
                # 检查data.list[0].advertiser_id
                elif 'list' in response_data['data'] and response_data['data']['list']:
                    first_item = response_data['data']['list'][0]
                    if isinstance(first_item, dict) and 'advertiser_id' in first_item:
                        extracted_id = first_item['advertiser_id']
            
            if expected_id:
                self.assertEqual(extracted_id, expected_id)
            else:
                self.assertIsNone(extracted_id)
        
        print("✅ 从响应检测账户ID测试通过")

    def test_multi_account_config_isolation(self):
        """测试多账户配置隔离"""
        print("\n🧪 测试多账户配置隔离")
        
        if MultiAccountConfigManager is None:
            self.skipTest("MultiAccountConfigManager模块不可用")
        
        config_manager = MultiAccountConfigManager()
        
        # 获取两个不同的账户配置
        test_account_ids = [
            account.get('real_account', {}).get('id') 
            for account in self.test_accounts 
            if account.get('real_account', {}).get('id')
        ][:2]
        
        if len(test_account_ids) < 2:
            self.skipTest("需要至少2个测试账户")
        
        # 加载两个账户的配置
        config1 = config_manager.get_account_config(test_account_ids[0])
        config2 = config_manager.get_account_config(test_account_ids[1])
        
        self.assertIsNotNone(config1)
        self.assertIsNotNone(config2)
        
        # 验证配置隔离 - 修改一个不应该影响另一个
        original_name1 = config1.get('Account Name')
        original_name2 = config2.get('Account Name')
        
        # 修改config1
        config1['Account Name'] = 'Modified Name 1'
        
        # 重新加载config2，应该不受影响
        config2_reloaded = config_manager.get_account_config(test_account_ids[1])
        self.assertEqual(config2_reloaded.get('Account Name'), original_name2)
        
        print("✅ 多账户配置隔离测试通过")

    def test_concurrent_account_operations(self):
        """测试并发账户操作"""
        print("\n🧪 测试并发账户操作")
        
        if MultiAccountConfigManager is None:
            self.skipTest("MultiAccountConfigManager模块不可用")
        
        config_manager = MultiAccountConfigManager()
        
        # 模拟并发访问不同账户
        test_account_ids = [
            account.get('real_account', {}).get('id') 
            for account in self.test_accounts 
            if account.get('real_account', {}).get('id')
        ]
        
        if not test_account_ids:
            self.skipTest("没有可用的测试账户")
        
        # 同时访问多个账户配置
        configs = []
        for account_id in test_account_ids:
            config = config_manager.get_account_config(account_id)
            if config:
                configs.append(config)
        
        # 验证所有配置都成功加载
        self.assertEqual(len(configs), len(test_account_ids))
        
        # 验证配置的唯一性
        account_ids_from_configs = [
            config.get('account_mapping', {}).get('real_account', {}).get('id')
            for config in configs
        ]
        
        # 去重后长度应该等于原长度（如果所有ID都不同）
        unique_ids = set(account_ids_from_configs)
        self.assertEqual(len(unique_ids), len(account_ids_from_configs))
        
        print("✅ 并发账户操作测试通过")

    def test_error_handling_and_recovery(self):
        """测试错误处理和恢复机制"""
        print("\n🧪 测试错误处理和恢复")
        
        if MultiAccountConfigManager is None:
            self.skipTest("MultiAccountConfigManager模块不可用")
        
        config_manager = MultiAccountConfigManager()
        
        # 测试访问不存在的账户
        nonexistent_config = config_manager.get_account_config('nonexistent_account_id')
        self.assertIsNone(nonexistent_config)
        
        # 测试无效的账户ID格式
        invalid_ids = ['', None, '123', 'invalid_id', '!@#$%^&*()']
        for invalid_id in invalid_ids:
            try:
                result = config_manager.get_account_config(invalid_id)
                # 应该返回None而不是抛出异常
                self.assertIsNone(result)
            except Exception as e:
                # 如果抛出异常，应该是合理的异常类型
                self.assertIsInstance(e, (ValueError, TypeError, FileNotFoundError))
        
        print("✅ 错误处理和恢复测试通过")

def run_multi_account_advanced_tests():
    """运行多账户系统高级测试"""
    print("🚀 开始多账户系统高级测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestMultiAccountAdvanced)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 多账户高级测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有多账户高级测试通过！")
    else:
        print("❌ 部分多账户高级测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_multi_account_advanced_tests()
