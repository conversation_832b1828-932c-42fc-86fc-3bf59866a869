{"api_patterns": {"balance": {"patterns": ["/platform/api/v1/statistics/dashboard/account_info", "/ad/api/account/info", "/ad/api/account/conf", "/platform/api/v1/statistics/dashboard/account_info/", "/superior/api/ad/account/contract_sign_info", "/superior/api/agw/account_trans", "/nbs/api/promote/account/update_notify_status", "/nbs/api/account/navigator/nav_type_list", "/nbs/api/account/contract/get_employee_info", "/nbs/api/promote/account/add_only_cookie", "/superior/api/dashboard/account_wallet", "/report/api/account/context", "/nbs/api/account/contract/query_customers_from_es", "/material_center/api/v1/common/get_group_info_by_account_id", "/passport/account/info/v2/", "/superior/api/account/check/qual", "/account/jssdk/account/current/app_v2", "/nbs/api/account/contract/get_consume_info", "/superior/api/account/douplus/user_id", "/platform/api/v1/bp/multi_accounts/org_with_account_list/", "/passport/safe/account_info/", "/superior/api/coupon/balance/detail", "/site/account-center/account/security/level", "/account/api/v2/dcar/check_adv_pop_up", "/nbs/api/bm/promotion/ad/get_account_list", "/superior/api/promote/account/get_deposit_notify", "/superior/api/promote/account/show_recharge"], "type": "balance", "handler": "modify_balance"}, "project_list": {"patterns": ["/superior/api/promote/project/list", {"request_body": ["data-raw", "project_status"]}], "type": "project", "handler": "modify_project_list"}, "promotion_list": {"patterns": ["promotion/ads/list", "promotion/ads/attribute/list"], "type": "promotion", "handler": "modify_promotion_list"}, "material_list": {"patterns": ["promotion/materials/list"], "type": "material", "handler": "modify_material_list"}, "statistics": {"patterns": ["/report/api/statistic/customize_report/data", "/superior/api/agw/statistics_sophonx/statQuery", "/report/api/tool/agw/automatic_rule/get_rule_execute_stats", "/superior/api/task_status/obtain", "/report/api/statistic/customize_report/get_custom_templates", "/superior/api/task_status/update", "/apps/littlesmart/web/assistant/dialog/stat"], "type": "statistics", "handler": "modify_statistics"}, "unknown": {"patterns": ["/copilot/api/v1/agw/session/human/latest", "/copilot/api/v1/agw/commonPush/pull/data", "/superior/api/agw/suggestion_center/suggestion_list", "/superior/api/promote/get_consume_cost_info", "/gw/api/generic/industry_category", "/nbs/api/bm/brand_matrix/record", "/superior/api/budget/get_nobid_predicted_result", "/nbs/api/bm/brand_matrix/list", "/nbs/api/bm/user/global_var/", "/passport/safe/login_op_log/", "/bp/api/navigator/survey/get_nps_survey_permission", "/report/api/tool/agw/custom_column/get_tmpls", "/copilot/api/v1/config/fe", "/copilot/api/v1/agw/abTest", "/platform/api/v1/orange/normal_list/", "/bp/api/", "/api/ebp/ebp_info/get_root_ebp", "/platform/api/v1/rta/display/", "/superior/api/agw/notice/list", "/report/api/tool/agw/custom_column/get_data_set_user_conf", "/nbs/api/common/ssa/info", "/support/backend/process/plugin/detail", "/superior/api/custom_columns/batch_get_diagnosis", "/nbs/api/bm/msg_center/notification/list_new_system_notifications", "/bp/api/common/template/get_user_conf", "/superior/api/promote/quota", "/copilot/vmok/ocean_copilot_sdk/vmok-manifest.json", "/superior/api/promote/get_operator_num", "/idt/service_link", "/nbs/api/bm/msg_center/notification/list_new_notifications", "/platform/api/v1/quota/hack_warning/", "/platform/api/v1/aweme_user/get_login_type", "/platform/api/v1/aweme_user/get_login_type/", "/copilot/api/v1/agw/user/preinit"], "type": "unknown", "handler": "modify_unknown"}, "project": {"patterns": ["/promotion/promote-manage/project", "/superior/api/promote/project/query", "/ad/api/promotion/projects/list"], "type": "project", "handler": "modify_project_list", "count": 6}, "promotion": {"patterns": ["/facade/oe/tool/query_advertiser_type", "/ad/api/navigator/get_dpa_access_auth", "/superior/api/promote/ads/query_group_values", "/superior/api/promote/ads/diagnosis_pause_list", "/report/api/tool/agw/automatic_rule/query_adv_quota", "/superior/api/agw/material/get_adv_repair_material_tasks", "/superior/api/agw/promotion/get_auto_clean_info", "/bp/api/promotion/promotion_common/query_violation_overview", "/superior/api/promote/ads/list", "/ad/api/agw/promotion/get_clue_customer_type", "/material_center/api/v1/common/adv/info", "/ad/api/agw/navigator/get_adv_avatar", "/ad/api/tcc/get_list", "/nbs/api/bm/promotion/get_part_data", "/api/ebp/promotion/common/get_all_ebp_list", "/ad/api/agw/star/can/used_task", "/nbs/api/bm/promotion/get_common_targets", "/ad/api/promotion/ads/get_invalid_list", "/superior/api/agw/ad/customer/coupon/label", "/ad/api/agw/promotion/promotion_management_guide", "/ad/api/agw/promotion/check_app_package_force", "/dolphin/advertiser/ad/authorization/get", "/ad/api/agw/contract/get_self_sign_prompt_info", "/ad/api/agw/aweme/get_aweme_cancel_count", "/ad/api/agw/contract/get_need_resign_cont_customer", "/ad/api/navigator/growth_center/check", "/facade/tool/advertiser_info", "/ad/api/agw/query_experiment_scenes", "/bp/api/promotion/promotion_common/get_overview_data"], "type": "promotion", "handler": "modify_promotion_list", "count": 79}, "material": {"patterns": ["/material_center/api/v1/management/video/material_list", "/material_center/api/v1/common/user/info", "/bp/material/Create%20WebSocket", "/material_center/management/Create%20WebSocket", "/material_center/api/v1/common/navigator/info", "/material_center/api/v1/management/video/material_filter", "/bp/material/orange.html", "/material_center/api/v1/management/video/auto_clean_info", "/material_center/api/v1/common/use_new_rule", "/material_center/management/video", "/material_center/api/v1/common/template/get_template", "/material_center/api/v1/common/query_authorize_group_assets", "/material_center/api/v1/common/group/info", "/material_center/api/v1/common/can_use_star_task", "/material_center/api/v1/management/material_tags"], "type": "material", "handler": "modify_material_list", "count": 23}}, "status_mappings": {"project": {"fields": {"project_status_first_name": "启用中", "project_status_name": "投放中", "project_status": 0, "project_status_first": 0, "campaign_status": 0, "campaign_opt_status": 0}, "clear_fields": ["project_status_second", "project_status_second_name"]}, "promotion": {"fields": {"promotion_status_first_name": "启用中", "promotion_status_name": "投放中", "promotion_status": 1, "promotion_status_first": 0, "ad_opt_status": 0, "diagnosis_interfere_status": "0", "learning_status": 0, "project_status": 0, "project_status_name": "投放中", "project_status_first": 0, "project_status_first_name": "启用中"}, "clear_fields": ["promotion_status_second", "promotion_status_second_name", "project_status_second", "project_status_second_name"], "list_fields": {"promotion_status_second": [1], "promotion_status_second_name": ["投放中"]}}, "material": {"fields": {"material_status_first_name": "投放中", "material_status_name": "投放中", "material_status": 2, "material_status_first": 0}, "clear_fields": ["material_status_second", "material_status_second_name"], "list_fields": {"material_status_second": [2], "material_status_second_name": ["投放中"]}}, "unknown": {"fields": ["status", "status_code", "StatusCode", "StatusMessage"], "enabled_value": 1, "disabled_value": 0}, "balance": {"fields": ["state_machine", "StatusMessage", "ad_status_new", "notify_status", "BP_promotion_status_renew", "auto_ad_sync_status_exempt", "item_default_status_white_list", "status", "GetInterfereStatusOp", "tianchi_ad_status", "ad_creator_statement_aigc", "StatusCode", "ab_status_machine"], "enabled_value": 1, "disabled_value": 0}, "statistics": {"fields": ["StatusCode", "StatusMessage"], "enabled_value": 1, "disabled_value": 0}}, "generic_patterns": {"status_keywords": ["\"project_status_first_name\":\"未投放\"", "\"promotion_status_first_name\":\"未投放\"", "\"material_status_first_name\":\"未投放\"", "\"project_status_name\":\"暂停\"", "\"promotion_status_name\":\"已暂停\"", "\"promotion_status_name\":\"新建审核中\"", "\"material_status_name\":\"广告已暂停\"", "\"project_status\":2", "\"promotion_status\":3", "\"campaign_status\":3"], "domain": "oceanengine.com"}, "last_updated": "2025-06-12T17:25:24.745873", "merge_history": [{"session_id": "未知", "merged_at": "2025-06-10T19:35:24.620533", "source_file": "2025-06-10T19:33:17.523511"}, {"session_id": "未知", "merged_at": "2025-06-11T00:15:11.759072", "source_file": "2025-06-11T00:00:23.466169"}, {"session_id": "session_20250610_235559", "merged_at": "2025-06-11T00:15:33.749813", "source_file": "2025-06-10T23:57:17.206401"}, {"session_id": "未知", "merged_at": "2025-06-11T00:42:46.318376", "source_file": "2025-06-11T00:33:30.822239"}, {"session_id": "session_20250611_002908", "merged_at": "2025-06-11T00:43:21.989711", "source_file": "2025-06-11T00:33:26.187807"}, {"session_id": "未知", "merged_at": "2025-06-11T01:44:33.157168", "source_file": "2025-06-11T00:33:30.822239", "success_count": 10, "error_count": 0}, {"session_id": "session_20250611_002908", "merged_at": "2025-06-11T01:45:02.364029", "source_file": "2025-06-11T00:33:26.187807", "success_count": 3, "error_count": 0}, {"session_id": "session_20250612_171651", "merged_at": "2025-06-12T17:25:24.745873", "source_file": "2025-06-12T17:18:24.644000", "success_count": 6, "error_count": 0}]}