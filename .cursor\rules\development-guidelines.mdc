---
description: 
globs: 
alwaysApply: false
---
# 开发指导原则
遇到不能确定，或者缺失信息的时候，主动让我给你提供，不要自己虚构或者猜测


## 代码质量标准

### 文件长度控制
- **严格限制**：每个代码文件不得超过200行
- **拆分策略**：
  - 功能模块化：将大文件拆分为多个功能模块
  - 组件化开发：前端组件保持简洁
  - 工具函数分离：将通用函数提取到 `utils/` 目录
- **例外处理**：特殊情况需要团队讨论并在代码中注释说明原因

### 架构设计原则
- **避免过度设计**：保持简单有效的解决方案
- **模块化开发**：功能独立，接口清晰
- **可维护性优先**：代码清晰易懂，便于维护
- **测试驱动**：编写可测试的代码

## 目录组织规范

### 核心代码结构
- `src/core/` - 核心业务逻辑，保持最小复杂度
- `src/commands/` - 命令行接口实现
- `src/generators/` - 代码生成器和模板

### 配置管理
- `config/` - 项目级配置文件
- `smart_engine/config/` - 引擎配置
- `smart_engine/config/backup/` - 配置备份
- `smart_engine/config/generated/` - 自动生成的配置

### 数据和日志
- `data/logs/` - 应用程序日志
- `data/api_logs/` - API调用日志
- `tests/test_data/` - 测试数据集

## 开发工作流程

### 项目启动
1. 检查 [requirements.txt](mdc:requirements.txt) 确保依赖已安装
2. 使用 [launcher.py](mdc:launcher.py) 启动主程序
3. 根据需要修改配置文件

### 调试和开发
- 使用 [debug_account_mapping.py](mdc:debug_account_mapping.py) 进行账户映射调试
- 查看 `data/logs/` 目录下的日志文件
- 测试数据位于 `tests/test_data/` 各子目录

### 代码提交前检查
- 确保文件长度不超过200行
- 运行测试确保功能正常
- 检查代码风格和注释完整性
- 验证配置文件的正确性

## 最佳实践

### 错误处理
- 使用适当的异常处理机制
- 记录详细的错误日志到 `data/logs/`
- 提供有意义的错误消息

### 性能优化
- 避免不必要的复杂度
- 合理使用缓存机制
- 监控和分析性能指标

### 文档维护
- 保持 `docs/` 目录下文档的及时更新
- API文档与代码同步
- 用户指南反映最新功能

