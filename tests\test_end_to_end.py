#!/usr/bin/env python3
"""
🚀 端到端测试框架 - Task 2 实现
测试完整的广告修改流程、账户映射和代理服务器集成
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class EndToEndTestSuite(unittest.TestCase):
    """端到端测试套件"""
    
    def setUp(self):
        """每个测试前的准备"""
        self.test_dir = tempfile.mkdtemp(prefix='e2e_test_')
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 创建测试配置
        os.makedirs('smart_engine/config', exist_ok=True)
        
        api_config = {
            "api_patterns": {
                "account_info": {
                    "type": "account",
                    "handler": "modify_account_info",
                    "patterns": ["/ad/api/account/info"]
                },
                "balance_api": {
                    "type": "balance",
                    "handler": "modify_balance",
                    "patterns": ["/advertiser/info", "/fund/daily_stat"]
                },
                "statistics_api": {
                    "type": "statistics", 
                    "handler": "modify_statistics",
                    "patterns": ["/report/advertiser/get"]
                }
            },
            "status_mappings": {
                "project": {"fields": {"status": 1, "status_name": "投放中"}},
                "promotion": {"fields": {"status": 1, "status_name": "启用中"}}
            },
            "metrics_template": {
                "basic": {
                    "show_cnt": "{Show Count}",
                    "stat_cost": "{Stat Cost}",
                    "click_cnt": "{Click Count}"
                }
            }
        }
        
        with open('smart_engine/config/api_rules_config.json', 'w', encoding='utf-8') as f:
            json.dump(api_config, f)
        
        test_metrics = {
            'Account Name': 'WH-测试公司',
            'Account ID': '****************',
            'Show Count': 50000,
            'Click Count': 2500,
            'Stat Cost': 250.0,
            'CTR (%)': 5.0,
            'account_mapping': {
                'real_account': {'name': '真实测试账户', 'id': '****************'},
                'virtual_account': {'name': 'WH-测试公司', 'id': '****************'}
            }
        }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(test_metrics, f)
    
    def tearDown(self):
        """每个测试后的清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_01_smart_engine_initialization(self):
        """测试1：智能引擎初始化"""
        print("\n🧪 测试1：智能引擎初始化")
        
        try:
            from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
            engine = SmartAPIEngine()
            
            # 验证引擎实例化成功
            self.assertIsNotNone(engine)
            self.assertIsNotNone(engine.config)
            self.assertIsNotNone(engine.api_identifier)
            
            # 验证配置加载
            self.assertIn('api_patterns', engine.config)
            
            print("✅ 智能引擎初始化测试通过")
            
        except Exception as e:
            self.fail(f"智能引擎初始化失败: {e}")
    
    def test_02_test_data_loader_integration(self):
        """测试2：测试数据加载器集成"""
        print("\n🧪 测试2：测试数据加载器集成")
        
        try:
            from tests.test_data_loader import test_data_loader
            
            # 验证账户数据加载
            account = test_data_loader.get_account_by_name('basic_account')
            self.assertIsNotNone(account)
            self.assertIn('virtual_account', account)
            self.assertEqual(account['virtual_account']['name'], 'WH-测试公司')
            
            # 验证指标数据加载
            metrics = test_data_loader.get_test_metrics()
            self.assertGreater(len(metrics), 0)
            
            print("✅ 测试数据加载器集成测试通过")
            
        except Exception as e:
            self.fail(f"测试数据加载器集成失败: {e}")
    
    def test_03_api_identification(self):
        """测试3：API识别测试"""
        print("\n🧪 测试3：API识别测试")
        
        try:
            from mitmproxy.test import tflow
            from smart_engine.engine.api_identifier import APIIdentifier
            
            config = {
                "api_patterns": {
                    "account_info": {
                        "type": "account",
                        "handler": "modify_account_info",
                        "patterns": ["/ad/api/account/info"]
                    }
                }
            }
            
            identifier = APIIdentifier(config)
            
            # 测试API识别
            flow = tflow.tflow()
            flow.request.url = "https://ad.oceanengine.com/ad/api/account/info"
            
            api_info = identifier.identify_api(flow)
            self.assertIsNotNone(api_info)
            self.assertEqual(api_info['type'], 'account')
            
            print("✅ API识别测试通过")
            
        except Exception as e:
            self.fail(f"API识别测试失败: {e}")
    
    def test_04_complete_api_processing_flow(self):
        """测试4：完整API处理流程 - 新增端到端场景"""
        print("\n🧪 测试4：完整API处理流程")
        
        try:
            from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
            
            engine = SmartAPIEngine()
            
            # 创建模拟HTTP流 - 使用MagicMock替代mitmproxy
            mock_flow = MagicMock()
            mock_flow.request.url = "https://ad.oceanengine.com/openapi/2/advertiser/info/"
            mock_flow.request.text = ""
            
            # 模拟API响应
            balance_response = {
                "data": {
                    "advertiser_name": "原始账户名",
                    "advertiser_id": "****************",
                    "balance": 1000.0
                },
                "code": 0,
                "message": "success"
            }
            mock_flow.response.text = json.dumps(balance_response)
            
            # 执行完整处理流程
            engine.process_flow(mock_flow)
            
            # 验证set_text被调用（表示响应被修改）
            self.assertTrue(mock_flow.response.set_text.called)
            
            print("✅ 完整API处理流程测试通过")
            
        except Exception as e:
            self.fail(f"完整API处理流程失败: {e}")
    
    def test_05_statistics_api_end_to_end(self):
        """测试5：统计API端到端流程 - 新增"""
        print("\n🧪 测试5：统计API端到端流程")
        
        try:
            from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
            
            engine = SmartAPIEngine()
            
            # 创建统计API流 - 使用MagicMock
            mock_flow = MagicMock()
            mock_flow.request.url = "https://ad.oceanengine.com/openapi/2/report/advertiser/get/"
            mock_flow.request.text = ""
            
            # 模拟统计API响应
            stats_response = {
                "data": {
                    "StatsData": {
                        "Totals": {
                            "stat_cost": {"Value": 100.0, "ValueStr": "100.0"},
                            "show_cnt": {"Value": 10000, "ValueStr": "10,000"},
                            "click_cnt": {"Value": 500, "ValueStr": "500"}
                        },
                        "Rows": [
                            {
                                "Metrics": {
                                    "stat_cost": {"Value": 50.0, "ValueStr": "50.0"},
                                    "show_cnt": {"Value": 5000, "ValueStr": "5,000"}
                                }
                            }
                        ]
                    }
                },
                "code": 0,
                "message": "success"
            }
            mock_flow.response.text = json.dumps(stats_response)
            
            # 执行处理
            engine.process_flow(mock_flow)
            
            # 验证set_text被调用（表示响应被处理）
            self.assertTrue(mock_flow.response.set_text.called)
            
            print("✅ 统计API端到端流程测试通过")
            
        except Exception as e:
            self.fail(f"统计API端到端流程失败: {e}")
    
    def test_06_error_handling_end_to_end(self):
        """测试6：错误处理端到端流程 - 新增"""
        print("\n🧪 测试6：错误处理端到端流程")
        
        try:
            from smart_engine.engine.smart_api_engine_minimal import SmartAPIEngine
            
            engine = SmartAPIEngine()
            
            # 测试无效JSON响应处理 - 使用MagicMock
            mock_flow = MagicMock()
            mock_flow.request.url = "https://ad.oceanengine.com/openapi/2/advertiser/info/"
            mock_flow.response.text = "invalid json response"
            
            # 处理应该不会抛出异常
            engine.process_flow(mock_flow)
            
            # 测试未知API处理
            mock_flow2 = MagicMock()
            mock_flow2.request.url = "https://unknown.com/unknown/api"
            mock_flow2.response.text = '{"data": {"test": "value"}}'
            
            engine.process_flow(mock_flow2)
            
            print("✅ 错误处理端到端流程测试通过")
            
        except Exception as e:
            self.fail(f"错误处理端到端流程失败: {e}")
    
    def test_07_account_mapping_end_to_end(self):
        """测试7：账户映射端到端流程 - 新增"""
        print("\n🧪 测试7：账户映射端到端流程")
        
        try:
            from tests.test_data_loader import test_data_loader
            
            # 获取多个测试账户进行映射测试
            accounts = test_data_loader.get_test_accounts()
            
            for account in accounts:
                account_name = account.get('name')
                real_account = account.get('real_account', {})
                virtual_account = account.get('virtual_account', {})
                
                print(f"  🔍 测试账户映射: {account_name}")
                
                # 验证映射的完整性
                self.assertIn('name', real_account)
                self.assertIn('id', real_account)
                self.assertIn('name', virtual_account)
                self.assertIn('id', virtual_account)
                
                # 验证ID格式（应该是数字字符串）
                self.assertTrue(real_account['id'].isdigit())
                self.assertTrue(virtual_account['id'].isdigit())
            
            print("✅ 账户映射端到端流程测试通过")
            
        except Exception as e:
            self.fail(f"账户映射端到端流程失败: {e}")

def run_end_to_end_tests():
    """运行端到端测试套件"""
    print("🚀 启动端到端测试框架")
    print("=" * 60)
    
    suite = unittest.TestLoader().loadTestsFromTestCase(EndToEndTestSuite)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n📊 端到端测试结果摘要")
    print("=" * 60)
    print(f"✅ 测试通过: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 测试失败: {len(result.failures)}")
    print(f"💥 测试错误: {len(result.errors)}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_end_to_end_tests()
    sys.exit(0 if success else 1)