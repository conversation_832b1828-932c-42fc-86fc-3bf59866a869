#!/usr/bin/env python3
"""
🔄 账户映射管理器 (增强版)
统一管理虚拟←→真实账户信息的双向映射

🎯 核心功能：
- 双向映射：虚拟账户 ←→ 真实账户
- 请求转换：将请求中的虚拟信息替换为真实信息（全字段转换）
- 响应转换：只转换指定的展示数据字段（白名单模式）
- 智能识别：自动识别需要替换的字段
- 完整日志：详细记录所有转换操作和映射信息
"""

import json
import re
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, parse_qs, urlencode
from smart_engine.utils.account_transform_utils import AccountTransformUtils
from smart_engine.utils.account_data_processor import AccountDataProcessor

class AccountMapper:
    """账户映射管理器"""
    
    # 📋 响应转换白名单 - 只有这些字段在响应中会被转换
    RESPONSE_TRANSFORM_WHITELIST = [
        # 账户显示信息（仅在特定显示API中转换）
        'account_name',
        'account_id',      # 账户显示ID
        'advertiser_name',
        'advertiser_id',   # 仅在账户信息显示API中
        'screen_name',
        'display_name',
        'company_name',
        'name',            # 🔥 关键修复：多账户列表API中的账户名称字段
        # 数据展示字段
        'show_cnt',
        'click_cnt',
        'convert_cnt',
        'stat_cost',
        # 其他明确的展示字段
        'user_name',       # 仅在用户信息显示API中
        'user_id',         # 仅在用户信息显示API中
    ]
    
    # 📋 请求转换字段 - 请求中所有账户相关字段都转换
    REQUEST_TRANSFORM_FIELDS = [
        'advertiser_id', 'advertiser_name',
        'account_id', 'account_name', 
        'user_id', 'user_name',
        'screen_name', 'display_name',
        'company_name'
    ]
    
    def __init__(self, config: Dict[str, Any]):
        """初始化账户映射器

        Args:
            config: 包含account_mapping配置的字典
        """
        from smart_engine.utils.debug_logger import debug_log, info_log, warning_log, error_log, success_log

        debug_log(f"🔄 [AccountMapper] 开始初始化账户映射器")

        self.config = config
        self.account_mapping = config.get('account_mapping', {})

        # 验证配置完整性
        if not self.account_mapping:
            error_log(f"❌ [AccountMapper] 配置中缺少account_mapping字段")
            raise ValueError("配置中缺少account_mapping字段")

        # 解析真实和虚拟账户信息
        self.real_account = self.account_mapping.get('real_account', {})
        self.virtual_account = self.account_mapping.get('virtual_account', {})

        # 验证账户信息完整性
        if not self.real_account or not self.virtual_account:
            error_log(f"❌ [AccountMapper] 账户信息不完整")
            error_log(f"   real_account: {self.real_account}")
            error_log(f"   virtual_account: {self.virtual_account}")
            raise ValueError("账户信息不完整")

        # 验证必要字段
        for account_type, account_info in [('real_account', self.real_account), ('virtual_account', self.virtual_account)]:
            for field in ['name', 'id']:
                if field not in account_info or not account_info[field]:
                    error_log(f"❌ [AccountMapper] {account_type}缺少{field}字段")
                    raise ValueError(f"{account_type}缺少{field}字段")

        info_log(f"📋 [AccountMapper] 账户信息验证通过")
        info_log(f"   🔑 真实账户: {self.real_account.get('name')} ({self.real_account.get('id')})")
        info_log(f"   🎭 虚拟账户: {self.virtual_account.get('name')} ({self.virtual_account.get('id')})")

        # 初始化转换工具
        try:
            debug_log(f"🔄 [AccountMapper] 初始化转换工具")
            self.transform_utils = AccountTransformUtils(self.real_account, self.virtual_account)
            success_log(f"✅ [AccountMapper] 转换工具初始化成功")
        except Exception as e:
            error_log(f"❌ [AccountMapper] 转换工具初始化失败: {e}")
            raise

        # 初始化数据处理器
        try:
            debug_log(f"🔄 [AccountMapper] 初始化数据处理器")
            self.data_processor = AccountDataProcessor(
                self.transform_utils,
                self.REQUEST_TRANSFORM_FIELDS,
                self.RESPONSE_TRANSFORM_WHITELIST
            )
            success_log(f"✅ [AccountMapper] 数据处理器初始化成功")
        except Exception as e:
            error_log(f"❌ [AccountMapper] 数据处理器初始化失败: {e}")
            raise

        # 会话统计
        self.session_stats = {
            "requests_processed": 0,
            "requests_transformed": 0,
            "responses_processed": 0,
            "responses_transformed": 0,
            "session_start_time": None
        }

        # 构建映射关系
        try:
            debug_log(f"🔄 [AccountMapper] 构建映射关系")
            self._build_mappings()
            success_log(f"✅ [AccountMapper] 映射关系构建成功")
        except Exception as e:
            error_log(f"❌ [AccountMapper] 映射关系构建失败: {e}")
            raise
        
    def _build_mappings(self):
        """构建双向映射关系"""
        from smart_engine.utils.debug_logger import debug_log, info_log, success_log, process_log, set_mapping_context
        from datetime import datetime

        debug_log(f"🔄 [AccountMapper] 开始构建映射关系")

        # 虚拟 → 真实
        self.virtual_to_real = {
            'name': {
                self.virtual_account.get('name', ''): self.real_account.get('name', '')
            },
            'id': {
                self.virtual_account.get('id', ''): self.real_account.get('id', '')
            }
        }

        # 真实 → 虚拟
        self.real_to_virtual = {
            'name': {
                self.real_account.get('name', ''): self.virtual_account.get('name', '')
            },
            'id': {
                self.real_account.get('id', ''): self.virtual_account.get('id', '')
            }
        }

        # 记录映射详情
        debug_log(f"📋 [AccountMapper] 虚拟→真实映射:")
        debug_log(f"   名称: {self.virtual_account.get('name')} → {self.real_account.get('name')}")
        debug_log(f"   ID: {self.virtual_account.get('id')} → {self.real_account.get('id')}")

        debug_log(f"📋 [AccountMapper] 真实→虚拟映射:")
        debug_log(f"   名称: {self.real_account.get('name')} → {self.virtual_account.get('name')}")
        debug_log(f"   ID: {self.real_account.get('id')} → {self.virtual_account.get('id')}")

        self.session_stats["session_start_time"] = datetime.now()

        # 设置日志上下文
        set_mapping_context(self.real_account, self.virtual_account)

        process_log("账户映射器初始化完成")
        info_log(f"  🔑 真实账户: {self.real_account.get('name')} ({self.real_account.get('id')})")
        info_log(f"  🎭 虚拟账户: {self.virtual_account.get('name')} ({self.virtual_account.get('id')})")
        info_log(f"  📋 响应转换白名单: {len(self.RESPONSE_TRANSFORM_WHITELIST)}个字段")
        info_log(f"  📤 请求转换字段: {len(self.REQUEST_TRANSFORM_FIELDS)}个字段")

        success_log(f"✅ [AccountMapper] 账户映射器完全初始化成功")
    
    def transform_request(self, flow) -> bool:
        """转换请求：虚拟 → 真实
        
        Args:
            flow: mitmproxy的HTTPFlow对象
            
        Returns:
            bool: 是否进行了转换
        """
        from smart_engine.utils.debug_logger import debug_log, info_log
        
        self.session_stats["requests_processed"] += 1
        
        if not self.data_processor.should_transform_request(flow):
            # 每100个跳过的请求记录一次
            if self.session_stats["requests_processed"] % 100 == 0:
                debug_log(f"已跳过{self.session_stats['requests_processed']}个请求")
            return False
            
        debug_log(f"开始转换请求: {flow.request.url}")
        debug_log(f"🚀 [调试] transform_request被调用，URL: {flow.request.url}")
        transformed = False
        url_for_logging = flow.request.url
        
        # 转换URL参数
        debug_log(f"🚀 [调试] 准备调用transform_url_params")
        try:
            if self.transform_utils.transform_url_params(flow):
                debug_log(f"🚀 [调试] transform_url_params返回True")
                transformed = True
            else:
                debug_log(f"🚀 [调试] transform_url_params返回False")
        except Exception as e:
            error_log(f"🚀 [调试] transform_url_params异常: {e}")
            import traceback
            traceback.print_exc()
            
        # 转换请求体
        if self.data_processor.transform_request_body(flow, url_for_logging):
            transformed = True
            
        # 转换请求头
        if self.data_processor.transform_request_headers(flow):
            transformed = True
            
        if transformed:
            self.session_stats["requests_transformed"] += 1
            info_log(f"✅ 请求转换成功: {url_for_logging}")
        else:
            debug_log(f"请求无需转换: {url_for_logging}")
            
        return transformed
    
    def transform_response(self, response_data: Dict[str, Any], url: str = None) -> bool:
        """转换响应：真实 → 虚拟
        
        Args:
            response_data: 响应数据字典
            url: 请求URL（用于日志记录）
            
        Returns:
            bool: 是否进行了转换
        """
        from smart_engine.utils.debug_logger import transform_log, info_log
        
        self.session_stats["responses_processed"] += 1
        
        # 🔍 记录转换前的原始数据（仅在DEBUG级别）
        transform_log("开始分析响应数据", {
            "data_type": type(response_data).__name__,
            "data_keys": list(response_data.keys()) if isinstance(response_data, dict) else "非字典类型",
            "url": url
        })
        
        # 执行转换
        result = self.data_processor.transform_response_recursive(response_data, url)
        
        if result:
            self.session_stats["responses_transformed"] += 1
            info_log("✅ 响应转换成功")
        
        # 🔍 记录转换结果
        transform_log(f"响应转换完成，结果: {'有转换' if result else '无转换'}")
        
        return result
    
    def _is_account_field(self, field_name: str) -> bool:
        """判断字段是否是账户相关字段（兼容旧代码）"""
        # 保持向后兼容，但推荐使用更具体的方法
        return self.transform_utils.is_request_account_field(field_name, self.REQUEST_TRANSFORM_FIELDS)
    
    def get_mapping_info(self) -> Dict[str, Any]:
        """获取映射信息"""
        return {
            'real_account': self.real_account,
            'virtual_account': self.virtual_account,
            'has_mapping': bool(self.real_account and self.virtual_account)
        }
    
    def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        stats = self.session_stats.copy()
        
        # 添加数据处理器的统计
        processor_stats = self.data_processor.get_session_stats()
        stats.update(processor_stats)
        
        # 计算转换率
        if stats["requests_processed"] > 0:
            stats["request_transform_rate"] = round(stats["requests_transformed"] / stats["requests_processed"] * 100, 2)
        
        if stats["responses_processed"] > 0:
            stats["response_transform_rate"] = round(stats["responses_transformed"] / stats["responses_processed"] * 100, 2)
        
        return stats
    
    def log_session_summary(self):
        """记录会话摘要"""
        from smart_engine.utils.debug_logger import info_log
        from datetime import datetime
        
        stats = self.get_session_stats()
        
        if stats.get("session_start_time"):
            duration = datetime.now() - stats["session_start_time"]
            duration_str = f"{duration.total_seconds():.1f}秒"
        else:
            duration_str = "未知"
        
        summary = f"""
=== 账户映射会话摘要 ===
映射配置: {self.virtual_account.get('name', '虚拟账户')} ←→ {self.real_account.get('name', '真实账户')}
会话时长: {duration_str}
请求处理: {stats['requests_processed']} (转换: {stats['requests_transformed']})
响应处理: {stats['responses_processed']} (转换: {stats['responses_transformed']})
字段处理: {stats.get('fields_processed', 0)}
转换操作: {stats.get('transformations_applied', 0)}
请求转换率: {stats.get('request_transform_rate', 0)}%
响应转换率: {stats.get('response_transform_rate', 0)}%
========================"""
        
        info_log(summary)
    
    def __del__(self):
        """析构函数，记录会话结束"""
        try:
            self.log_session_summary()
        except:
            pass
 