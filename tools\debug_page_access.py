#!/usr/bin/env python3
"""
🔍 多账户页面访问调试指南
帮助用户确认是否访问了正确的多账户页面
"""

def print_page_access_guide():
    """打印页面访问指南"""
    print("🔍 多账户虚拟化功能调试指南")
    print("=" * 80)
    
    print("\n📋 **问题诊断**：")
    print("   从日志分析发现，代理正在运行，但没有拦截到多账户列表API")
    print("   这说明用户可能没有访问正确的多账户页面")
    
    print("\n🎯 **正确的访问步骤**：")
    print()
    print("1. 🚀 **启动代理服务**：")
    print("   mitmdump -s smart_engine/engine/multi_account_proxy_script.py --listen-port 8080")
    print()
    print("2. 🌐 **配置浏览器代理**：")
    print("   - HTTP代理：127.0.0.1:8080")
    print("   - HTTPS代理：127.0.0.1:8080")
    print("   - 确保代理设置已生效")
    print()
    print("3. 📱 **访问多账户管理页面**：")
    print("   🔗 https://business.oceanengine.com/site/account-manage/ad/bidding/superior/account")
    print()
    print("   ⚠️ **注意**：必须是这个特定的URL，不是其他页面！")
    print()
    print("4. 🔍 **确认页面内容**：")
    print("   页面应该显示：")
    print("   - 多个广告账户的列表")
    print("   - 账户名称（如：广州希赢贸易、希赢贸易等）")
    print("   - 账户状态和基本信息")
    print()
    print("5. 📊 **观察日志输出**：")
    print("   如果访问正确，应该看到：")
    print("   - 🎯 [入口代理] 匹配到多账户列表API")
    print("   - 🔄 [入口代理] 账户虚拟化: 真实名称 → 虚拟名称")
    print("   - ✅ [入口代理] 账户列表响应处理完成")
    
    print("\n❌ **常见错误**：")
    print()
    print("1. **访问了错误的页面**：")
    print("   ❌ 单账户页面：https://business.oceanengine.com/site/...")
    print("   ❌ 其他管理页面")
    print("   ✅ 多账户列表页面：.../account-manage/ad/bidding/superior/account")
    print()
    print("2. **代理配置问题**：")
    print("   - 浏览器代理设置错误")
    print("   - 代理服务没有启动")
    print("   - 端口冲突")
    print()
    print("3. **缓存问题**：")
    print("   - 浏览器缓存了旧的页面数据")
    print("   - 需要强制刷新（Ctrl+F5）")
    
    print("\n🔧 **故障排除**：")
    print()
    print("1. **检查代理状态**：")
    print("   - 确认mitmdump进程正在运行")
    print("   - 检查端口8080是否被占用")
    print()
    print("2. **检查浏览器设置**：")
    print("   - 确认代理设置正确")
    print("   - 尝试访问其他网站确认代理生效")
    print()
    print("3. **检查URL**：")
    print("   - 确保访问的是完整的多账户管理URL")
    print("   - 不要通过书签或历史记录访问")
    print()
    print("4. **清除缓存**：")
    print("   - 清除浏览器缓存")
    print("   - 使用无痕模式访问")
    
    print("\n📈 **预期效果**：")
    print()
    print("如果一切正常，您应该看到：")
    print("✅ 账户名称变为：测试广告主A、测试广告主B、测试广告主C、测试广告主D")
    print("✅ 显示虚拟投放数据：消耗、展示、点击、转化等")
    print("✅ 日志显示成功的API拦截和数据转换")
    
    print("\n🆘 **如果仍然无效**：")
    print()
    print("1. 检查日志中是否有 'Oceanengine API包含关键词' 的记录")
    print("2. 确认访问的页面确实会调用多账户列表API")
    print("3. 尝试刷新页面或重新登录")
    print("4. 检查账户权限是否允许访问多账户管理")

def analyze_current_logs():
    """分析当前日志状态"""
    print("\n🔍 **当前日志分析**：")
    print("=" * 40)
    
    try:
        with open('data/logs/account_mapping_debug.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 统计API调用
        api_calls = []
        oceanengine_apis = []
        
        for line in lines[-50:]:  # 最近50行
            if '🔍 [入口代理] 检查API:' in line:
                api_url = line.split('检查API: ')[-1].strip()
                api_calls.append(api_url)
                
                if 'oceanengine.com' in api_url:
                    oceanengine_apis.append(api_url)
        
        print(f"📊 最近API调用统计：")
        print(f"   总调用数：{len(api_calls)}")
        print(f"   Oceanengine API：{len(oceanengine_apis)}")
        
        if oceanengine_apis:
            print(f"\n🌐 Oceanengine API列表：")
            unique_apis = list(set(oceanengine_apis))
            for api in unique_apis[-5:]:  # 显示最近5个不同的API
                print(f"   {api}")
        else:
            print(f"\n❌ 没有发现Oceanengine API调用")
            print(f"   这说明用户可能没有访问Oceanengine网站")
        
        # 检查是否有多账户相关的API
        multi_account_keywords = ['multi_account', 'account_list', 'org_with_account']
        found_multi_apis = []
        
        for line in lines:
            for keyword in multi_account_keywords:
                if keyword in line and 'API' in line:
                    found_multi_apis.append(line.strip())
        
        if found_multi_apis:
            print(f"\n🎯 发现多账户相关API：")
            for api in found_multi_apis[-3:]:
                print(f"   {api}")
        else:
            print(f"\n❌ 没有发现多账户相关API")
            print(f"   这说明用户没有访问多账户列表页面")
        
    except FileNotFoundError:
        print("❌ 日志文件不存在")
    except Exception as e:
        print(f"❌ 日志分析失败：{e}")

def main():
    """主函数"""
    print_page_access_guide()
    analyze_current_logs()
    
    print("\n" + "=" * 80)
    print("🎯 **下一步行动**：")
    print("1. 确认代理正在运行")
    print("2. 访问正确的多账户页面URL")
    print("3. 观察日志输出变化")
    print("4. 如果看到多账户API调用，功能应该立即生效")

if __name__ == "__main__":
    main()
