@echo off
echo 🚀 启动多账户虚拟化代理服务
echo =====================================
echo.

echo 📋 代理配置:
echo    监听端口: 8080
echo    脚本文件: smart_engine/engine/multi_account_proxy_script.py
echo    日志文件: data/logs/account_mapping_debug.log
echo.

echo 🌐 浏览器代理设置:
echo    HTTP代理:  127.0.0.1:8080
echo    HTTPS代理: 127.0.0.1:8080
echo.

echo 📱 测试页面:
echo    https://business.oceanengine.com/site/account-manage/ad/bidding/superior/account
echo.

echo 🔍 启动代理服务...
mitmdump -s smart_engine/engine/multi_account_proxy_script.py --listen-port 8080

pause
