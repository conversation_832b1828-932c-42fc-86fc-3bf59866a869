#!/usr/bin/env python3
"""
🧪 回归测试套件类 - 扩展部分
从test_regression.py拆分出来的高级功能测试，符合200行文件长度限制
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock, mock_open

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.utils import ad_utils

class TestRegressionSuiteExtended(unittest.TestCase):
    """回归测试主套件 - 扩展部分测试（测试9-14）"""
    
    def setUp(self):
        """测试前准备 - 使用数据驱动的测试数据"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 🆕 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json（保持向后兼容）
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 🆕 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_09_main_program_flow(self):
        """测试9：主程序流程"""
        print("\n🧪 测试9：主程序流程")
        
        # 导入主程序模块
        from src.core import mitmPro_AD
        
        # 模拟用户输入
        with patch('builtins.input', side_effect=['真实测试公司', '111111', '测试公司', '123456']):
            account_data = mitmPro_AD.get_account_data()
            self.assertEqual(account_data['Account Name'], '测试公司')
            self.assertEqual(account_data['Account ID'], '123456')
        
        # 测试默认值处理
        with patch('builtins.input', side_effect=['真实公司', '999999', '', '']):
            account_data = mitmPro_AD.get_account_data()
            self.assertEqual(account_data['Account Name'], 'WH-我的广告公司')
            self.assertEqual(account_data['Account ID'], '****************')
        
        # 测试广告数据计算（使用工具函数）
        from smart_engine.utils.ad_utils import calculate_ctr
        ctr = calculate_ctr(500, 10000)  # 点击数, 展示数
        self.assertEqual(ctr, 5.0)
        
        print("✅ 主程序流程测试通过")

    def test_10_launcher_scripts(self):
        """测试10：启动器脚本"""
        print("\n🧪 测试10：启动器脚本")

        # 测试quick_start_monitor中的关键函数
        try:
            from src.commands import quick_start_monitor
            self.assertTrue(hasattr(quick_start_monitor, 'start_monitor'))
            print("✅ API监控模式启动器正常")
        except ImportError:
            print("⚠️ quick_start_monitor模块导入失败，跳过测试")

        # 测试多账户启动器核心
        try:
            from src.launchers import multi_account_launcher_core
            self.assertTrue(hasattr(multi_account_launcher_core, 'start_multi_account_mode'))
            print("✅ 多账户模式启动器正常")
        except ImportError:
            print("⚠️ multi_account_launcher_core模块导入失败，跳过测试")

        print("✅ 启动器脚本测试通过")

    def test_11_config_generation(self):
        """测试11：配置生成工具"""
        print("\n🧪 测试11：配置生成工具")
        
        try:
            from src.generators import generate_config_simple
            self.assertTrue(hasattr(generate_config_simple, 'main'))
        except ImportError:
            print("⚠️ generate_config_simple模块导入失败，跳过测试")
        
        print("✅ 配置生成工具测试通过")

    def test_12_error_handling(self):
        """测试12：错误处理机制"""
        print("\n🧪 测试12：错误处理机制")
        
        # 测试无效JSON处理
        with patch('builtins.open', mock_open(read_data='invalid json')):
            try:
                ad_utils.load_metrics_from_file('invalid.json')
            except json.JSONDecodeError:
                pass  # 预期的异常
        
        # 测试零除法保护
        safe_metrics = ad_utils.calculate_ad_metrics(0, 0, 0, 0)
        self.assertEqual(safe_metrics['CTR (%)'], 0)
        self.assertEqual(safe_metrics['Conversion Rate (%)'], 0)
        
        # 测试格式化函数的错误输入
        invalid_format = ad_utils.format_number_with_commas("not_a_number")
        self.assertEqual(invalid_format, "Invalid input")
        
        print("✅ 错误处理机制测试通过")

    def test_13_integration_config_loading(self):
        """测试13：配置系统集成测试"""
        print("\n🧪 测试13：配置系统集成测试")
        
        # 测试多个配置文件的联动
        accounts = test_data_loader.get_test_accounts()
        self.assertIsInstance(accounts, list)
        if accounts:
            self.assertIn('name', accounts[0])
            print("  ✅ 账户配置加载正常")
        
        metrics = test_data_loader.get_test_metrics()
        self.assertIsInstance(metrics, list)
        if metrics:
            self.assertIn('name', metrics[0])
            print("  ✅ 指标配置加载正常")
        
        balance_responses = test_data_loader.get_api_responses('balance_api')
        statistics_responses = test_data_loader.get_api_responses('statistics_api')
        self.assertIsInstance(balance_responses, dict)
        self.assertIsInstance(statistics_responses, dict)
        print("  ✅ API响应配置加载正常")
        
        print("✅ 配置系统集成测试通过")
    
    def test_14_data_driven_account_mapping(self):
        """测试14：数据驱动的账户映射测试"""
        print("\n🧪 测试14：数据驱动账户映射")
        
        # 获取所有测试账户进行映射测试
        test_accounts = test_data_loader.get_test_accounts()
        
        for account in test_accounts:
            account_name = account.get('name', 'unknown')
            real_account = account.get('real_account', {})
            virtual_account = account.get('virtual_account', {})
            
            print(f"  🔍 测试账户映射: {account_name}")
            
            # 创建包含映射的测试数据
            test_metrics_with_mapping = {
                **self.test_metrics,
                'account_mapping': {
                    'real_account': real_account,
                    'virtual_account': virtual_account
                }
            }
            
            # 验证映射数据结构
            self.assertIn('real_account', test_metrics_with_mapping['account_mapping'])
            self.assertIn('virtual_account', test_metrics_with_mapping['account_mapping'])
            
            # 验证账户信息字段
            if real_account:
                self.assertIn('name', real_account)
                self.assertIn('id', real_account)
            
            if virtual_account:
                self.assertIn('name', virtual_account)
                self.assertIn('id', virtual_account)
        
        print("✅ 数据驱动账户映射测试通过") 