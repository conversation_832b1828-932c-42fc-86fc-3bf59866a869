#!/usr/bin/env python3
"""
📊 实时日志监控工具
监控多账户虚拟化功能的日志输出
"""
import time
import os
from datetime import datetime

def monitor_logs():
    """实时监控日志文件"""
    log_file = "data/logs/account_mapping_debug.log"
    
    print("📊 开始实时监控多账户虚拟化日志")
    print("=" * 60)
    print(f"📁 日志文件: {log_file}")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    print()
    
    # 获取当前文件大小，从末尾开始监控
    try:
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                f.seek(0, 2)  # 移动到文件末尾
                current_pos = f.tell()
        else:
            current_pos = 0
            print("⚠️ 日志文件不存在，等待创建...")
    except Exception as e:
        print(f"❌ 无法打开日志文件: {e}")
        return
    
    print("🔍 等待新的日志输出...")
    print("   (配置浏览器代理并访问多账户页面)")
    print()
    
    last_activity = time.time()
    
    try:
        while True:
            try:
                if os.path.exists(log_file):
                    with open(log_file, 'r', encoding='utf-8') as f:
                        f.seek(current_pos)
                        new_lines = f.readlines()
                        
                        if new_lines:
                            last_activity = time.time()
                            for line in new_lines:
                                line = line.strip()
                                if line:
                                    # 高亮重要日志
                                    if '🎯' in line or '匹配到多账户列表API' in line:
                                        print(f"🎯 **重要**: {line}")
                                    elif '✅' in line and ('账户列表' in line or '虚拟化' in line):
                                        print(f"✅ **成功**: {line}")
                                    elif '🔄' in line and '账户虚拟化' in line:
                                        print(f"🔄 **处理**: {line}")
                                    elif 'Oceanengine API包含关键词' in line:
                                        print(f"🔍 **发现**: {line}")
                                    elif '入口代理' in line:
                                        print(f"📋 {line}")
                                    else:
                                        print(f"   {line}")
                            
                            current_pos = f.tell()
                        
                # 检查是否长时间无活动
                if time.time() - last_activity > 30:
                    print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - 等待API调用...")
                    print("   💡 提示：确保浏览器代理已配置并访问了多账户页面")
                    last_activity = time.time()
                
                time.sleep(1)  # 每秒检查一次
                
            except KeyboardInterrupt:
                print("\n\n🛑 监控已停止")
                break
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                time.sleep(2)
                
    except KeyboardInterrupt:
        print("\n\n🛑 监控已停止")

def check_proxy_status():
    """检查代理状态"""
    print("🔍 检查代理服务状态...")
    
    import subprocess
    try:
        # 检查端口8080是否被占用
        result = subprocess.run(['netstat', '-an'], capture_output=True, text=True)
        if ':8080' in result.stdout:
            print("✅ 端口8080正在被使用（代理可能正在运行）")
        else:
            print("❌ 端口8080未被占用（代理可能未启动）")
    except Exception as e:
        print(f"⚠️ 无法检查端口状态: {e}")

if __name__ == "__main__":
    print("🚀 多账户虚拟化实时监控")
    print("=" * 40)
    
    # 检查代理状态
    check_proxy_status()
    print()
    
    # 开始监控
    monitor_logs()
