#!/usr/bin/env python3
"""
🧪 账户映射测试模块
测试AccountMapper核心功能、ID/名称双向转换、映射配置管理等

这是新增的测试模块，专门补充账户映射功能的测试盲点
覆盖smart_engine/utils/account_mapper.py的核心功能
"""

import os
import sys
import json
import unittest
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
try:
    from smart_engine.utils.account_mapper import AccountMapper
except ImportError:
    AccountMapper = None

try:
    from smart_engine.utils.multi_account_config_manager import MultiAccountConfigManager
except ImportError:
    MultiAccountConfigManager = None

class TestAccountMapping(unittest.TestCase):
    """账户映射测试套件"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试账户
        self.test_accounts = test_data_loader.get_test_accounts()
        
        # 创建测试用的账户映射配置
        self.test_mapping_config = {
            'account_mapping': {
                'real_account': {
                    'id': '****************',
                    'name': '希赢贸易'
                },
                'virtual_account': {
                    'id': '****************', 
                    'name': 'WH-测试公司'
                }
            }
        }
        
        # 创建另一个测试配置
        self.test_mapping_config2 = {
            'account_mapping': {
                'real_account': {
                    'id': '****************',
                    'name': '广州希赢贸易'
                },
                'virtual_account': {
                    'id': '****************',
                    'name': 'GZ-测试公司'
                }
            }
        }
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_account_mapper_initialization(self):
        """测试AccountMapper初始化"""
        print("\n🧪 测试AccountMapper初始化")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        try:
            # 测试正常初始化
            mapper = AccountMapper(self.test_mapping_config)
            self.assertIsNotNone(mapper)

            # 检查mapper是否有必要的属性
            self.assertTrue(hasattr(mapper, 'virtual_to_real'))
            self.assertTrue(hasattr(mapper, 'real_to_virtual'))

            print("✅ AccountMapper初始化测试通过")
        except Exception as e:
            print(f"❌ AccountMapper初始化失败: {e}")
            # 如果AccountMapper不可用，跳过这个测试
            self.skipTest(f"AccountMapper不可用: {e}")

    def test_id_conversion_accuracy(self):
        """测试ID双向转换准确性"""
        print("\n🧪 测试ID双向转换准确性")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        mapper = AccountMapper(self.test_mapping_config)
        
        # 获取测试ID
        real_id = self.test_mapping_config['account_mapping']['real_account']['id']
        virtual_id = self.test_mapping_config['account_mapping']['virtual_account']['id']
        
        # 测试虚拟ID → 真实ID转换
        converted_real_id = mapper.virtual_to_real['id'].get(virtual_id)
        self.assertEqual(converted_real_id, real_id)
        
        # 测试真实ID → 虚拟ID转换
        converted_virtual_id = mapper.real_to_virtual['id'].get(real_id)
        self.assertEqual(converted_virtual_id, virtual_id)
        
        # 测试双向转换的一致性
        round_trip_real = mapper.virtual_to_real['id'].get(
            mapper.real_to_virtual['id'].get(real_id)
        )
        self.assertEqual(round_trip_real, real_id)
        
        round_trip_virtual = mapper.real_to_virtual['id'].get(
            mapper.virtual_to_real['id'].get(virtual_id)
        )
        self.assertEqual(round_trip_virtual, virtual_id)
        
        print("✅ ID双向转换准确性测试通过")

    def test_name_conversion_accuracy(self):
        """测试名称双向转换准确性"""
        print("\n🧪 测试名称双向转换准确性")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        mapper = AccountMapper(self.test_mapping_config)
        
        # 获取测试名称
        real_name = self.test_mapping_config['account_mapping']['real_account']['name']
        virtual_name = self.test_mapping_config['account_mapping']['virtual_account']['name']
        
        # 测试虚拟名称 → 真实名称转换
        converted_real_name = mapper.virtual_to_real['name'].get(virtual_name)
        self.assertEqual(converted_real_name, real_name)
        
        # 测试真实名称 → 虚拟名称转换
        converted_virtual_name = mapper.real_to_virtual['name'].get(real_name)
        self.assertEqual(converted_virtual_name, virtual_name)
        
        # 测试双向转换的一致性
        round_trip_real = mapper.virtual_to_real['name'].get(
            mapper.real_to_virtual['name'].get(real_name)
        )
        self.assertEqual(round_trip_real, real_name)
        
        round_trip_virtual = mapper.real_to_virtual['name'].get(
            mapper.virtual_to_real['name'].get(virtual_name)
        )
        self.assertEqual(round_trip_virtual, virtual_name)
        
        print("✅ 名称双向转换准确性测试通过")

    def test_invalid_mapping_handling(self):
        """测试无效映射处理"""
        print("\n🧪 测试无效映射处理")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        mapper = AccountMapper(self.test_mapping_config)
        
        # 测试不存在的ID
        nonexistent_real = mapper.virtual_to_real['id'].get('nonexistent_id')
        self.assertIsNone(nonexistent_real)
        
        nonexistent_virtual = mapper.real_to_virtual['id'].get('nonexistent_id')
        self.assertIsNone(nonexistent_virtual)
        
        # 测试不存在的名称
        nonexistent_real_name = mapper.virtual_to_real['name'].get('不存在的名称')
        self.assertIsNone(nonexistent_real_name)
        
        nonexistent_virtual_name = mapper.real_to_virtual['name'].get('不存在的名称')
        self.assertIsNone(nonexistent_virtual_name)
        
        # 测试空值处理
        empty_result = mapper.virtual_to_real['id'].get('')
        self.assertIsNone(empty_result)
        
        none_result = mapper.virtual_to_real['id'].get(None)
        self.assertIsNone(none_result)
        
        print("✅ 无效映射处理测试通过")

    def test_mapping_config_validation(self):
        """测试映射配置验证"""
        print("\n🧪 测试映射配置验证")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        # 测试完整配置
        valid_config = self.test_mapping_config
        mapper = AccountMapper(valid_config)
        self.assertIsNotNone(mapper)
        
        # 测试缺少account_mapping的配置
        invalid_config1 = {'other_data': 'value'}
        try:
            mapper_invalid = AccountMapper(invalid_config1)
            # 如果没有抛出异常，检查是否有合理的默认处理
            self.assertIsNotNone(mapper_invalid)
        except Exception as e:
            # 如果抛出异常，验证异常类型合理
            self.assertIsInstance(e, (KeyError, ValueError, TypeError))
        
        # 测试不完整的映射配置
        invalid_config2 = {
            'account_mapping': {
                'real_account': {'id': '123'}
                # 缺少virtual_account
            }
        }
        try:
            mapper_invalid2 = AccountMapper(invalid_config2)
            self.assertIsNotNone(mapper_invalid2)
        except Exception as e:
            self.assertIsInstance(e, (KeyError, ValueError, TypeError))
        
        print("✅ 映射配置验证测试通过")

    def test_multiple_mappers_isolation(self):
        """测试多个映射器的隔离性"""
        print("\n🧪 测试多个映射器隔离性")

        if AccountMapper is None:
            self.skipTest("AccountMapper模块不可用")

        # 创建两个不同的映射器
        mapper1 = AccountMapper(self.test_mapping_config)
        mapper2 = AccountMapper(self.test_mapping_config2)
        
        # 获取各自的ID
        real_id1 = self.test_mapping_config['account_mapping']['real_account']['id']
        real_id2 = self.test_mapping_config2['account_mapping']['real_account']['id']
        
        # 验证映射器1只能转换自己的ID
        result1_from_mapper1 = mapper1.real_to_virtual['id'].get(real_id1)
        self.assertIsNotNone(result1_from_mapper1)
        
        result2_from_mapper1 = mapper1.real_to_virtual['id'].get(real_id2)
        self.assertIsNone(result2_from_mapper1)
        
        # 验证映射器2只能转换自己的ID
        result2_from_mapper2 = mapper2.real_to_virtual['id'].get(real_id2)
        self.assertIsNotNone(result2_from_mapper2)
        
        result1_from_mapper2 = mapper2.real_to_virtual['id'].get(real_id1)
        self.assertIsNone(result1_from_mapper2)
        
        print("✅ 多个映射器隔离性测试通过")



def run_account_mapping_tests():
    """运行账户映射测试"""
    print("🚀 开始账户映射测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestAccountMapping)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 账户映射测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有账户映射测试通过！")
    else:
        print("❌ 部分账户映射测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_account_mapping_tests()
