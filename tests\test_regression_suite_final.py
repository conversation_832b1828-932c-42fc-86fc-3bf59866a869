#!/usr/bin/env python3
"""
🧪 回归测试套件类 - 最终部分
从test_regression.py拆分出来的处理器测试，符合200行文件长度限制
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock, mock_open

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.engine.statistics_handler import StatisticsHandler
from smart_engine.engine.metrics_handler import MetricsHandler

class TestRegressionSuiteFinal(unittest.TestCase):
    """回归测试主套件 - 最终部分测试（测试7-8）"""
    
    def setUp(self):
        """测试前准备 - 使用数据驱动的测试数据"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 🆕 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json（保持向后兼容）
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 🆕 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_07_statistics_handler(self):
        """测试7：统计处理器 - 使用真实响应数据"""
        print("\n🧪 测试7：统计处理器（真实响应数据）")
        
        handler = StatisticsHandler(self.test_metrics)
        
        # 🆕 获取真实API响应数据
        api_responses = test_data_loader.get_api_responses('statistics_api')
        
        # 测试成功响应场景
        success_response = api_responses.get('success', {})
        if success_response:
            mock_flow = MagicMock()
            mock_flow.response.text = json.dumps(success_response)
            
            api_info = {"type": "statistics", "handler": "modify_statistics"}
            handler.modify_statistics(mock_flow, api_info)
            
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 真实响应场景测试通过")
        else:
            # 回退到原有测试格式
            mock_flow = MagicMock()
            mock_response_data = {
                "data": {
                    "StatsData": {
                        "Totals": {
                            "stat_cost": {"Value": 50.0, "ValueStr": "50.0"},
                            "show_cnt": {"Value": 1000, "ValueStr": "1,000"}
                        },
                        "Rows": [
                            {
                                "Metrics": {
                                    "stat_cost": {"Value": 25.0, "ValueStr": "25.0"},
                                    "show_cnt": {"Value": 500, "ValueStr": "500"}
                                }
                            }
                        ]
                    }
                }
            }
            mock_flow.response.text = json.dumps(mock_response_data)
            
            api_info = {"type": "statistics", "handler": "modify_statistics"}
            handler.modify_statistics(mock_flow, api_info)
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 默认测试场景通过")
        
        print("✅ 统计处理器测试通过")

    def test_08_metrics_handler(self):
        """测试8：指标处理器"""
        print("\n🧪 测试8：指标处理器")
        
        # 创建指标处理器需要的配置
        config = {
            "status_mappings": {
                "project": {
                    "fields": {"status": 1, "status_name": "投放中"}
                },
                "promotion": {
                    "fields": {"status": 1, "status_name": "启用中"}
                }
            },
            "metrics_template": {
                "basic": {
                    "show_cnt": "{Show Count}",
                    "stat_cost": "{Stat Cost}"
                }
            }
        }
        
        handler = MetricsHandler(config, self.test_metrics)
        
        # 测试指标数据构建
        metrics_data = handler.build_metrics_data()
        self.assertIn("show_cnt", metrics_data)
        self.assertIn("stat_cost", metrics_data)
        
        # 测试状态更新功能
        test_data = {"status": 0, "status_name": "暂停"}
        handler.update_status_fields(test_data, "project")
        self.assertEqual(test_data["status"], 1)
        
        print("✅ 指标处理器测试通过") 