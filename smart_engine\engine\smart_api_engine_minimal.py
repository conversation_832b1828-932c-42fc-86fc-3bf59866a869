import sys
import os
from mitmproxy import http

# 设置默认编码为UTF-8，避免Windows编码问题
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入核心引擎
from smart_engine.engine.core_engine import SmartAPIEngine

# ============================================================================
# mitmproxy 入口点 - 代理模式，确保系统正常工作
# ============================================================================

# 全局智能引擎实例
smart_engine = None

def load(loader):
    """mitmproxy加载时调用"""
    global smart_engine
    print("🚀 [智能引擎] 正在初始化...")
    smart_engine = SmartAPIEngine()
    print("✅ [智能引擎] 初始化完成")

def request(flow: http.HTTPFlow):
    """mitmproxy请求处理入口 - 新增：处理账户信息转换"""
    global smart_engine
    if smart_engine:
        try:
            # 调试：显示请求信息
            if 'oceanengine.com' in flow.request.pretty_host:
                print(f"🔍 [DEBUG] 处理请求: {flow.request.url}")
            
            # 预处理编码问题
            _fix_flow_encoding(flow)
            smart_engine.process_request_flow(flow)
        except UnicodeDecodeError as e:
            # 静默处理编码错误
            pass
        except Exception as e:
            # 只输出非编码相关的错误
            if 'codec' not in str(e).lower() and 'decode' not in str(e).lower():
                print(f"❌ [智能引擎] 处理请求出错: {e}")

def response(flow: http.HTTPFlow):
    """mitmproxy响应处理入口"""
    global smart_engine
    if smart_engine and hasattr(flow, 'response') and flow.response:
        try:
            # 预处理编码问题
            _fix_flow_encoding(flow)
            smart_engine.process_flow(flow)
        except UnicodeDecodeError as e:
            # 静默处理编码错误
            pass
        except Exception as e:
            # 只输出非编码相关的错误
            if 'codec' not in str(e).lower() and 'decode' not in str(e).lower():
                print(f"❌ [智能引擎] 处理响应出错: {e}")

def _fix_flow_encoding(flow: http.HTTPFlow):
    """修复flow对象的编码问题"""
    try:
        # 修复请求编码
        if hasattr(flow, 'request') and flow.request:
            _fix_message_encoding(flow.request)
        
        # 修复响应编码  
        if hasattr(flow, 'response') and flow.response:
            _fix_message_encoding(flow.response)
    except:
        # 静默处理所有编码错误
        pass

def _fix_message_encoding(message):
    """修复消息对象的编码问题"""
    try:
        # 如果有content但没有正确的text，尝试重新解码
        if hasattr(message, 'content') and message.content:
            if not hasattr(message, 'text') or not message.text:
                # 尝试多种编码
                for encoding in ['utf-8', 'gbk', 'latin-1']:
                    try:
                        decoded_text = message.content.decode(encoding)
                        # 成功解码后不设置text属性，让mitmproxy自己处理
                        break
                    except UnicodeDecodeError:
                        continue
    except:
        # 静默处理所有错误
        pass

# 兼容性：支持直接运行测试
if __name__ == "__main__":
    print("🧪 智能引擎测试模式")
    engine = SmartAPIEngine()
    print("✅ 智能引擎创建成功")