#!/usr/bin/env python3
"""
🎯 巨量引擎智能API管理系统 - 统一启动入口 v2.0
提供多种启动模式的统一选择界面
支持日志级别配置

🔧 v2.0 简化版特性：
- 直接整合所有启动器功能，减少调用层级
- 移除launchers/代理层，提升启动效率
- 保持100%向后兼容性
- 优化代码结构，便于AI维护
"""
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(__file__)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def show_launcher_menu():
    """显示启动模式选择菜单"""
    print("🚀 巨量引擎智能API管理系统")
    print("=" * 60)
    print("请选择启动模式:")
    print("1. 🔍 API监控模式 - 监控和学习新的API模式")
    print("2. 👥 多账户模式 - 支持多账户智能引擎")
    print("0. ❌ 退出")
    print()

def choose_log_level():
    """选择日志级别"""
    print("\n📊 日志级别选择:")
    print("=" * 40)
    print("1. QUIET级别 - 生产环境推荐 (只显示错误和重要信息)")
    print("   • 只显示ERROR、WARNING、SUCCESS信息")
    print("   • 适合生产环境，输出最少")
    print()
    print("2. NORMAL级别 - 调试环境推荐 (显示关键处理信息)")
    print("   • 显示ERROR、WARNING、INFO、SUCCESS信息")
    print("   • 适合日常调试，平衡详细度和可读性")
    print()
    print("3. VERBOSE级别 - 开发环境推荐 (显示所有详细信息)")
    print("   • 显示所有级别的日志信息")
    print("   • 适合深度调试和开发阶段")
    print()
    
    while True:
        choice = input("请选择日志级别 (1-3): ").strip()
        if choice == '1':
            return "QUIET"
        elif choice == '2':
            return "NORMAL"
        elif choice == '3':
            return "VERBOSE"
        else:
            print("❌ 无效选择，请输入1-3")

def set_global_log_level(level):
    """设置全局日志级别"""
    try:
        # 设置环境变量，确保子进程也能使用正确的日志级别
        os.environ['SMART_ENGINE_LOG_LEVEL'] = level

        from smart_engine.utils.debug_logger import set_log_level, clear_log_file
        set_log_level(level)  # 先设置级别
        clear_log_file()      # 再清空并重新初始化文件
        print(f"✅ 全局日志级别已设置为: {level}")
        print("✅ 调试日志已重新初始化")
    except Exception as e:
        print(f"⚠️ 日志设置失败: {e}")



def main():
    """主入口函数"""
    while True:
        show_launcher_menu()
        choice = input("请输入选择 (0-2): ").strip()

        try:
            if choice in ['1', '2']:
                # 选择日志级别
                log_level = choose_log_level()
                set_global_log_level(log_level)

                if choice == '1':
                    # 🔍 API监控模式 - 直接调用实现
                    from src.commands.quick_start_monitor import start_monitor
                    start_monitor()
                    break
                elif choice == '2':
                    # 👥 多账户模式 - 直接调用实现
                    from src.launchers.multi_account_launcher_core import start_multi_account_mode
                    start_multi_account_mode(log_level)
                    break

            elif choice == '0':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
                continue
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
            break
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main() 