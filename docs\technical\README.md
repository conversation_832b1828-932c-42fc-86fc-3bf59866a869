# 🏗️ 技术文档目录

本目录包含项目的技术实现文档，包括系统架构、API参考、配置说明等。

## 📋 文档列表

### 🏛️ 系统架构
- **[architecture.md](architecture.md)** - 系统整体架构设计
- **[implementation-summary.md](implementation-summary.md)** - 实现总结文档

### 🔗 多账户系统
- **[multi-account-architecture.md](multi-account-architecture.md)** - 多账户系统架构
- **[多账户数据预配置方案.md](多账户数据预配置方案.md)** - 预配置数据映射方案 🆕
- **[multi-account-config-conflict-bug.md](multi-account-config-conflict-bug.md)** - 配置冲突问题记录

### 📊 API文档
- **[api-reference.md](api-reference.md)** - API接口参考文档

### ⚙️ 配置管理
- **[configuration.md](configuration.md)** - 配置文件详解

## 🎯 重点文档

### 🆕 最新技术方案
- **[多账户数据预配置方案.md](多账户数据预配置方案.md)**
  - 基于配置文件驱动的多账户数据管理
  - 简化技术实现，提高系统可靠性
  - 充分利用现有系统能力

### 🏗️ 核心架构
- **[architecture.md](architecture.md)** - 了解系统整体设计
- **[multi-account-architecture.md](multi-account-architecture.md)** - 多账户功能架构

### 📊 API集成
- **[api-reference.md](api-reference.md)** - API接口使用说明

## 📚 文档用途

### 🔧 开发参考
- 提供技术实现细节
- 指导功能开发和扩展
- 记录重要的技术决策

### 🏗️ 架构指导
- 系统设计原则和模式
- 模块间的交互关系
- 技术选型和实现方案

### 📝 问题记录
- 技术问题和解决方案
- Bug修复记录
- 优化改进建议

## 📅 更新记录

- **2025-06-17**: 添加多账户数据预配置方案文档
- **持续更新中...**

---

**🏗️ 技术文档，构建稳固的系统基础！**
