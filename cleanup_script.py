#!/usr/bin/env python3
"""
🧹 项目清理脚本
自动化清理重复文件、备份文件和废弃代码

使用方法:
python cleanup_script.py --dry-run  # 预览清理操作
python cleanup_script.py --execute  # 执行清理操作
"""

import os
import shutil
import glob
import argparse
from datetime import datetime, timedelta
import json

class ProjectCleaner:
    def __init__(self, dry_run=True):
        self.dry_run = dry_run
        self.project_root = os.path.dirname(__file__)
        self.actions = []
        
    def log_action(self, action_type, description, path=None):
        """记录清理操作"""
        action = {
            'type': action_type,
            'description': description,
            'path': path,
            'timestamp': datetime.now().isoformat()
        }
        self.actions.append(action)
        
        if self.dry_run:
            print(f"[DRY RUN] {action_type}: {description}")
        else:
            print(f"[EXECUTE] {action_type}: {description}")
            
    def clean_duplicate_files(self):
        """清理重复文件"""
        print("\n🔍 检查重复文件...")
        
        # 检查ad_utils.py重复
        src_ad_utils = "src/core/ad_utils.py"
        smart_ad_utils = "smart_engine/utils/ad_utils.py"
        
        if os.path.exists(src_ad_utils) and os.path.exists(smart_ad_utils):
            self.log_action("DELETE", f"删除重复文件: {src_ad_utils}", src_ad_utils)
            if not self.dry_run:
                os.remove(src_ad_utils)
                
    def clean_backup_files(self, keep_days=7):
        """清理旧备份文件"""
        print(f"\n🗑️ 清理超过{keep_days}天的备份文件...")

        cutoff_date = datetime.now() - timedelta(days=keep_days)

        # 清理metrics.json备份
        backup_patterns = [
            "metrics.json.backup_*",
            "smart_engine/config/*.backup_*",
            "smart_engine/config/backup/*",
            "smart_engine/config/generated/*"
        ]

        for pattern in backup_patterns:
            for file_path in glob.glob(pattern):
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_date:
                        self.log_action("DELETE", f"删除旧备份文件: {file_path}", file_path)
                        if not self.dry_run:
                            os.remove(file_path)
                            
    def clean_pycache(self):
        """清理__pycache__目录"""
        print("\n🧹 清理__pycache__目录...")

        for root, dirs, _ in os.walk(self.project_root):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                self.log_action("DELETE", f"删除缓存目录: {pycache_path}", pycache_path)
                if not self.dry_run:
                    shutil.rmtree(pycache_path)

    def clean_temp_files(self):
        """清理临时文件"""
        print("\n🗑️ 清理临时文件...")

        temp_files = [
            "erro_response.txt",
            "test_a.json",
            "*.tmp",
            "*.temp"
        ]

        for pattern in temp_files:
            for file_path in glob.glob(pattern):
                if os.path.isfile(file_path):
                    self.log_action("DELETE", f"删除临时文件: {file_path}", file_path)
                    if not self.dry_run:
                        os.remove(file_path)
                    
    def check_unused_files(self):
        """检查可能未使用的文件"""
        print("\n🔍 检查可能未使用的文件...")
        
        # 检查tools目录下的脚本
        tools_scripts = [
            "tools/analyze_account_structure.py",
            "tools/analyze_api_logs.py", 
            "tools/api_config_analyzer.py",
            "tools/extract_multi_account_api.py"
        ]
        
        for script in tools_scripts:
            if os.path.exists(script):
                # 简单检查是否被其他文件引用
                is_used = self.check_file_usage(script)
                if not is_used:
                    self.log_action("WARN", f"可能未使用的文件: {script}", script)
                    
    def check_file_usage(self, file_path):
        """检查文件是否被其他文件引用"""
        file_name = os.path.basename(file_path).replace('.py', '')
        
        # 在所有Python文件中搜索引用
        for root, dirs, files in os.walk(self.project_root):
            # 跳过__pycache__和.git目录
            dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', '.venv']]
            
            for file in files:
                if file.endswith('.py') and file != os.path.basename(file_path):
                    full_path = os.path.join(root, file)
                    try:
                        with open(full_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if file_name in content:
                                return True
                    except:
                        continue
        return False
        
    def merge_test_files(self):
        """合并拆分的测试文件"""
        print("\n🔧 检查测试文件合并...")
        
        test_files = [
            "tests/test_regression_suite.py",
            "tests/test_regression_suite_middle.py", 
            "tests/test_regression_suite_final.py",
            "tests/test_regression_suite_extended.py"
        ]
        
        existing_files = [f for f in test_files if os.path.exists(f)]
        
        if len(existing_files) > 1:
            self.log_action("MERGE", f"建议合并测试文件: {', '.join(existing_files)}")
            
    def clean_launcher_proxies(self):
        """清理启动器代理文件"""
        print("\n🔧 检查启动器代理文件...")
        
        proxy_launchers = [
            "launchers/account_launcher.py",
            "launchers/demo_launcher.py",
            "launchers/main_launcher.py", 
            "launchers/monitor_launcher.py"
        ]
        
        for launcher in proxy_launchers:
            if os.path.exists(launcher):
                # 检查文件大小，如果很小可能是代理文件
                file_size = os.path.getsize(launcher)
                if file_size < 1000:  # 小于1KB
                    self.log_action("WARN", f"可能的代理文件: {launcher} ({file_size} bytes)", launcher)
                    
    def generate_report(self):
        """生成清理报告"""
        print("\n📊 生成清理报告...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': self.dry_run,
            'total_actions': len(self.actions),
            'actions': self.actions
        }
        
        report_file = f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        self.log_action("REPORT", f"清理报告已生成: {report_file}", report_file)
        
        # 打印统计信息
        action_types = {}
        for action in self.actions:
            action_type = action['type']
            action_types[action_type] = action_types.get(action_type, 0) + 1
            
        print(f"\n📈 清理统计:")
        for action_type, count in action_types.items():
            print(f"  {action_type}: {count}")
            
    def run_cleanup(self):
        """执行完整清理流程"""
        print("🚀 开始项目清理...")
        print(f"模式: {'预览模式' if self.dry_run else '执行模式'}")
        print("=" * 60)
        
        # 执行各种清理操作
        self.clean_duplicate_files()
        self.clean_backup_files()
        self.clean_pycache()
        self.clean_temp_files()
        self.check_unused_files()
        self.merge_test_files()
        self.clean_launcher_proxies()
        
        # 生成报告
        self.generate_report()
        
        print("\n✅ 清理完成!")
        if self.dry_run:
            print("💡 使用 --execute 参数执行实际清理操作")

def main():
    parser = argparse.ArgumentParser(description='项目清理脚本')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='预览模式，不执行实际操作（默认）')
    parser.add_argument('--execute', action='store_true',
                       help='执行模式，执行实际清理操作')
    
    args = parser.parse_args()
    
    # 如果指定了execute，则不是dry_run
    dry_run = not args.execute
    
    cleaner = ProjectCleaner(dry_run=dry_run)
    cleaner.run_cleanup()

if __name__ == "__main__":
    main()
