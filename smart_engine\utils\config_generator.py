"""
⚙️ 配置自动生成器
基于API监控数据自动生成智能引擎配置
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter

class ConfigGenerator:
    def __init__(self, log_dir='data/api_logs'):
        """初始化配置生成器"""
        self.log_dir = log_dir
        
    def analyze_session_logs(self, session_id: str) -> Optional[Dict]:
        """分析会话日志"""
        log_file = os.path.join(self.log_dir, f"{session_id}_apis.jsonl")
        
        if not os.path.exists(log_file):
            print(f"❌ 会话日志文件不存在: {log_file}")
            return None
            
        apis = []
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        api_info = json.loads(line.strip())
                        apis.append(api_info)
            
            print(f"📊 已加载 {len(apis)} 个API调用记录")
            return self._analyze_apis(apis)
            
        except Exception as e:
            print(f"❌ 分析会话日志失败: {e}")
            return None
    
    def _analyze_apis(self, apis: List[Dict]) -> Dict:
        """分析API数据"""
        analysis = {
            'total_apis': len(apis),
            'api_types': defaultdict(list),
            'path_patterns': defaultdict(Counter),
            'status_fields': defaultdict(set),
            'metrics_fields': defaultdict(set)
        }
        
        for api in apis:
            api_type = api.get('api_type', 'unknown')
            path = api.get('api_path', '')
            
            analysis['api_types'][api_type].append(api)
            analysis['path_patterns'][api_type][path] += 1
            
            # 分析响应结构
            if api.get('response') and api['response'].get('data_structure'):
                self._extract_field_info(api['response']['data_structure'], analysis, api_type)
        
        return analysis
    
    def _extract_field_info(self, structure: Dict, analysis: Dict, api_type: str):
        """提取字段信息"""
        if not isinstance(structure, dict):
            return
            
        if structure.get('type') == 'dict' and 'keys' in structure:
            for key_info in structure['keys']:
                key_name = key_info.get('name', '')
                
                # 识别状态字段
                if self._is_status_field(key_name):
                    analysis['status_fields'][api_type].add(key_name)
                
                # 识别指标字段  
                elif self._is_metrics_field(key_name):
                    analysis['metrics_fields'][api_type].add(key_name)
                
                # 递归处理嵌套结构
                if 'structure' in key_info:
                    self._extract_field_info(key_info['structure'], analysis, api_type)
    
    def _is_status_field(self, field_name: str) -> bool:
        """判断是否为状态字段"""
        status_keywords = ['status', 'state', 'opt_status']
        field_lower = field_name.lower()
        return any(keyword in field_lower for keyword in status_keywords)
    
    def _is_metrics_field(self, field_name: str) -> bool:
        """判断是否为指标字段"""
        metrics_keywords = ['cnt', 'count', 'cost', 'rate', 'ctr', 'cpm', 'cpc']
        field_lower = field_name.lower()
        return any(keyword in field_lower for keyword in metrics_keywords)
    
    def generate_api_config(self, analysis: Dict) -> Dict:
        """生成API配置"""
        config = {
            "api_patterns": {},
            "status_mappings": {},
            "metrics_template": {},
            "generated_at": datetime.now().isoformat()
        }
        
        # 生成API模式配置
        for api_type, apis in analysis['api_types'].items():
            if api_type == 'unknown':
                continue
                
            path_counter = analysis['path_patterns'][api_type]
            common_paths = [path for path, count in path_counter.most_common(5)]
            
            config["api_patterns"][api_type] = {
                "patterns": common_paths,
                "type": api_type,
                "handler": f"modify_{api_type}"
            }
        
        return config
    
    def save_generated_config(self, config: Dict) -> str:
        """保存生成的配置"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"generated_api_config_{timestamp}.json"
        config_path = os.path.join('smart_engine/config', filename)
        
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 配置已保存到: {config_path}")
            return config_path
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")
            return ""
