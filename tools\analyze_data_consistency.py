#!/usr/bin/env python3
"""
🔍 数据一致性分析工具
分析多账户列表页面和单账户页面的数据一致性问题
"""
import json
import os

def analyze_account_data():
    """分析各账户的虚拟数据"""
    print("🔍 多账户虚拟数据一致性分析")
    print("=" * 60)
    
    # 账户列表
    accounts = [
        '****************',  # 测试广告主A
        '****************',  # 测试广告主B
        '****************',  # 测试广告主C
        '****************'   # 测试广告主D
    ]
    
    total_cost = 0
    total_show = 0
    total_click = 0
    total_convert = 0
    
    print("📊 各账户虚拟数据详情:")
    print("-" * 40)
    
    for account_id in accounts:
        config_file = f'configs/accounts/metrics_{account_id}.json'
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cost = config.get('Stat Cost', 0)
            show = config.get('Show Count', 0)
            click = config.get('Click Count', 0)
            convert = config.get('Convert Count', 0)
            name = config.get('Account Name', f'账户{account_id}')
            
            print(f"\n🏢 {name}:")
            print(f"   💰 消耗: ¥{cost:,.2f}")
            print(f"   👁️ 展示: {show:,}")
            print(f"   👆 点击: {click:,}")
            print(f"   🎯 转化: {convert:,}")
            
            # 计算比率
            ctr = (click / show * 100) if show > 0 else 0
            cvr = (convert / click * 100) if click > 0 else 0
            cpc = (cost / click) if click > 0 else 0
            cpm = (cost / show * 1000) if show > 0 else 0
            
            print(f"   📊 CTR: {ctr:.2f}%")
            print(f"   📈 CVR: {cvr:.2f}%")
            print(f"   💵 CPC: ¥{cpc:.2f}")
            print(f"   📺 CPM: ¥{cpm:.2f}")
            
            total_cost += cost
            total_show += show
            total_click += click
            total_convert += convert
        else:
            print(f"❌ 配置文件不存在: {config_file}")
    
    print("\n" + "=" * 60)
    print("📈 多账户汇总数据:")
    print("-" * 30)
    print(f"💰 总消耗: ¥{total_cost:,.2f}")
    print(f"👁️ 总展示: {total_show:,}")
    print(f"👆 总点击: {total_click:,}")
    print(f"🎯 总转化: {total_convert:,}")
    
    # 计算汇总比率
    total_ctr = (total_click / total_show * 100) if total_show > 0 else 0
    total_cvr = (total_convert / total_click * 100) if total_click > 0 else 0
    total_cpc = (total_cost / total_click) if total_click > 0 else 0
    total_cpm = (total_cost / total_show * 1000) if total_show > 0 else 0
    
    print(f"📊 总CTR: {total_ctr:.2f}%")
    print(f"📈 总CVR: {total_cvr:.2f}%")
    print(f"💵 总CPC: ¥{total_cpc:.2f}")
    print(f"📺 总CPM: ¥{total_cpm:.2f}")
    
    return {
        'individual_accounts': accounts,
        'total_cost': total_cost,
        'total_show': total_show,
        'total_click': total_click,
        'total_convert': total_convert,
        'total_ctr': total_ctr,
        'total_cvr': total_cvr,
        'total_cpc': total_cpc,
        'total_cpm': total_cpm
    }

def identify_consistency_issue(data):
    """识别数据一致性问题"""
    print("\n" + "=" * 60)
    print("🔍 数据一致性问题分析:")
    print("-" * 40)
    
    print("❌ **发现的问题**:")
    print("   1. 单账户数据过大：每个单账户的消耗都在6000-10000元范围")
    print("   2. 多账户汇总数据：总消耗约56000元")
    print("   3. 逻辑不一致：单账户数据应该是多账户汇总的组成部分")
    print("   4. 用户反馈：单账户页面显示的数据远大于多账户列表概览数据")
    
    print("\n🎯 **问题根源**:")
    print("   当前配置中，每个账户都有独立的大数值虚拟数据")
    print("   但多账户列表页面显示的是这些数据的汇总")
    print("   这导致：多账户概览 = 单账户A + 单账户B + 单账户C + 单账户D")
    print("   而用户期望：单账户数据 < 多账户概览数据")
    
    print("\n💡 **解决方案**:")
    print("   方案1: 调整单账户数据，使其成为多账户数据的合理分配")
    print("   方案2: 修改多账户聚合逻辑，不是简单相加而是显示概览数据")
    print("   方案3: 重新设计数据关系，确保逻辑一致性")

def suggest_data_adjustment():
    """建议数据调整方案"""
    print("\n" + "=" * 60)
    print("🔧 数据调整建议:")
    print("-" * 40)
    
    # 建议的多账户概览数据（相对较大）
    overview_data = {
        'cost': 80000.0,  # 8万元总消耗
        'show': 500000,   # 50万展示
        'click': 25000,   # 2.5万点击
        'convert': 1250   # 1250转化
    }
    
    print("📊 建议的多账户概览数据:")
    print(f"   💰 总消耗: ¥{overview_data['cost']:,.2f}")
    print(f"   👁️ 总展示: {overview_data['show']:,}")
    print(f"   👆 总点击: {overview_data['click']:,}")
    print(f"   🎯 总转化: {overview_data['convert']:,}")
    
    print("\n📋 建议的单账户数据分配:")
    
    # 按比例分配给各账户
    accounts = [
        ('测试广告主A', 0.30),  # 30%
        ('测试广告主B', 0.25),  # 25%
        ('测试广告主C', 0.25),  # 25%
        ('测试广告主D', 0.20)   # 20%
    ]
    
    for name, ratio in accounts:
        account_cost = overview_data['cost'] * ratio
        account_show = int(overview_data['show'] * ratio)
        account_click = int(overview_data['click'] * ratio)
        account_convert = int(overview_data['convert'] * ratio)
        
        print(f"\n🏢 {name} ({ratio*100:.0f}%):")
        print(f"   💰 消耗: ¥{account_cost:,.2f}")
        print(f"   👁️ 展示: {account_show:,}")
        print(f"   👆 点击: {account_click:,}")
        print(f"   🎯 转化: {account_convert:,}")

def main():
    """主函数"""
    # 分析当前数据
    data = analyze_account_data()
    
    # 识别一致性问题
    identify_consistency_issue(data)
    
    # 建议数据调整
    suggest_data_adjustment()
    
    print("\n" + "=" * 60)
    print("🎯 **下一步行动**:")
    print("1. 确认数据关系设计：单账户数据应该如何与多账户概览数据关联")
    print("2. 调整配置文件：重新设计各账户的虚拟数据")
    print("3. 修改聚合逻辑：确保数据逻辑一致性")
    print("4. 测试验证：确保修改后的数据符合业务逻辑")

if __name__ == "__main__":
    main()
