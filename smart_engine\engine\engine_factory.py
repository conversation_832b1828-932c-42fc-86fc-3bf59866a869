"""
智能引擎工厂模块

负责处理器的创建、初始化和管理，减少主引擎文件的复杂度
"""

from smart_engine.utils.ad_utils import load_metrics_from_file
from smart_engine.utils.account_mapper import AccountMapper
from smart_engine.engine.statistics_handler import StatisticsHandler
from smart_engine.engine.balance_handler import BalanceHandler
from smart_engine.engine.metrics_handler import MetricsHandler
from smart_engine.engine.list_handler import <PERSON>Handler
from smart_engine.engine.detail_handler import DetailHandler
from smart_engine.engine.common_handler import CommonHandler
from smart_engine.engine.account_extractor import AccountExtractor
from smart_engine.engine.api_handlers import APIHandlers

class EngineFactory:
    """智能引擎组件工厂"""
    
    def __init__(self, config):
        """初始化工厂"""
        self.config = config
        self.metrics = {}
        self._handlers_cache = {}
    
    def create_account_mapper(self, metrics):
        """创建账户映射器"""
        if 'account_mapping' in metrics and metrics['account_mapping']:
            return AccountMapper(metrics)
        return None
    
    def create_account_extractor(self, metrics, account_mapper):
        """创建账户提取器"""
        return AccountExtractor(metrics, account_mapper)
    
    def create_statistics_handler(self, metrics):
        """创建统计处理器"""
        cache_key = f"stats_{id(metrics)}"
        if cache_key not in self._handlers_cache:
            self._handlers_cache[cache_key] = StatisticsHandler(metrics)
        return self._handlers_cache[cache_key]
    
    def create_balance_handler(self, metrics):
        """创建余额处理器"""
        cache_key = f"balance_{id(metrics)}"
        if cache_key not in self._handlers_cache:
            self._handlers_cache[cache_key] = BalanceHandler(metrics)
        return self._handlers_cache[cache_key]
    
    def create_metrics_handler(self, metrics):
        """创建指标处理器"""
        cache_key = f"metrics_{id(metrics)}"
        if cache_key not in self._handlers_cache:
            self._handlers_cache[cache_key] = MetricsHandler(self.config, metrics)
        return self._handlers_cache[cache_key]
    
    def create_list_handler(self, metrics_handler):
        """创建列表处理器"""
        return ListHandler(metrics_handler)
    
    def create_detail_handler(self, metrics_handler):
        """创建详情处理器"""
        return DetailHandler(metrics_handler)
    
    def create_common_handler(self, metrics_handler):
        """创建通用处理器"""
        return CommonHandler(metrics_handler)
    
    def create_api_handlers(self, statistics_handler, balance_handler, 
                           list_handler, detail_handler, common_handler):
        """创建API处理器集合"""
        return APIHandlers(
            statistics_handler,
            balance_handler,
            list_handler,
            detail_handler,
            common_handler
        )
    
    def create_all_handlers(self, metrics):
        """一次性创建所有处理器"""
        # 创建基础处理器
        statistics_handler = self.create_statistics_handler(metrics)
        balance_handler = self.create_balance_handler(metrics)
        metrics_handler = self.create_metrics_handler(metrics)
        
        # 创建高级处理器
        list_handler = self.create_list_handler(metrics_handler)
        detail_handler = self.create_detail_handler(metrics_handler)
        common_handler = self.create_common_handler(metrics_handler)
        
        # 创建API处理器集合
        api_handlers = self.create_api_handlers(
            statistics_handler, balance_handler,
            list_handler, detail_handler, common_handler
        )
        
        return {
            'statistics_handler': statistics_handler,
            'balance_handler': balance_handler,
            'metrics_handler': metrics_handler,
            'list_handler': list_handler,
            'detail_handler': detail_handler,
            'common_handler': common_handler,
            'api_handlers': api_handlers
        }
    
    def clear_cache(self):
        """清空处理器缓存"""
        self._handlers_cache.clear()

class EngineInitializer:
    """引擎初始化器"""
    
    def __init__(self, factory):
        self.factory = factory
    
    def startup_account_mapping(self, metrics):
        """启动时预提取账户映射配置"""
        print("🔄 [启动预提取] 正在检查账户映射配置...")
        
        # 如果已有映射配置，直接使用
        if 'account_mapping' in metrics and metrics['account_mapping']:
            mapping = metrics['account_mapping']
            print(f"✅ [启动预提取] 发现现有映射配置:")
            print(f"   🔑 真实账户: {mapping['real_account']['name']} ({mapping['real_account']['id']})")
            print(f"   🎭 虚拟账户: {mapping['virtual_account']['name']} ({mapping['virtual_account']['id']})")
            
            # 创建账户映射器
            account_mapper = self.factory.create_account_mapper(metrics)
            print(f"🔄 [启动预提取] 账户映射器已激活")
            return account_mapper
        
        # 如果没有映射配置，启动后台任务主动获取
        print("⚠️  [启动预提取] 未发现映射配置，将在首次访问ad.oceanengine.com时自动提取")
        print("💡 [启动预提取] 建议先访问广告后台主页以激活映射功能")
        return None
    
    def initialize_handlers_if_needed(self, current_handlers, metrics):
        """按需初始化处理器"""
        new_handlers = {}
        
        # 检查是否需要重新创建处理器
        if (current_handlers.get('statistics_handler') is None or 
            getattr(current_handlers.get('statistics_handler'), 'metrics', None) != metrics):
            
            # 重新创建所有处理器
            new_handlers = self.factory.create_all_handlers(metrics)
            print("🔄 [初始化器] 处理器已刷新")
        else:
            # 使用现有处理器
            new_handlers = current_handlers
        
        return new_handlers 