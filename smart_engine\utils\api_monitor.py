"""
🔍 API监控与学习模块
自动记录并分析巨量引擎API调用，生成智能引擎配置
"""
import json
import os
import time
import hashlib
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from typing import Dict, List, Any, Optional
from collections import defaultdict

class APIMonitor:
    def __init__(self, log_dir='data/api_logs'):
        """初始化API监控器"""
        self.log_dir = log_dir
        self.session_id = self._generate_session_id()
        self.api_patterns = defaultdict(list)
        self.api_statistics = defaultdict(int)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        print(f"🔍 API监控器已启动 - 会话ID: {self.session_id}")
    
    def _generate_session_id(self):
        """生成会话ID"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"session_{timestamp}"
    
    def record_api_call(self, flow) -> Optional[Dict]:
        """记录API调用"""
        try:
            # 只监控oceanengine.com的API
            if not flow.request.pretty_host.endswith('oceanengine.com'):
                return None

            # 🔥 增强调试：记录API监控的详细过程
            url = flow.request.url
            print(f"🔍 [API监控] 开始记录API调用: {url}")

            api_info = self._extract_api_info(flow)
            if api_info:
                # 🔥 增强调试：特别关注多账户相关API
                api_path = api_info.get('api_path', '')
                if 'account_list' in api_path or 'get_part_data' in api_path:
                    print(f"🎯 [API监控] 发现多账户相关API: {api_path}")

                    # 记录响应内容的关键信息
                    if 'response' in api_info and api_info['response']:
                        response_body = api_info['response'].get('body', '')
                        if response_body:
                            print(f"   📏 [API监控] 响应体长度: {len(response_body)} 字符")

                            # 检查是否包含账户名称
                            real_names = ["希赢贸易", "广州希赢贸易", "广州希赢", "初仔好得意"]
                            virtual_names = ["测试广告主A", "测试广告主B", "测试广告主C", "测试广告主D"]

                            contains_real = any(name in response_body for name in real_names)
                            contains_virtual = any(name in response_body for name in virtual_names)

                            print(f"   🔍 [API监控] 包含真实账户名: {contains_real}")
                            print(f"   🔍 [API监控] 包含虚拟账户名: {contains_virtual}")

                            if contains_real:
                                found_real = [name for name in real_names if name in response_body]
                                print(f"   📋 [API监控] 发现真实账户名: {found_real}")

                            if contains_virtual:
                                found_virtual = [name for name in virtual_names if name in response_body]
                                print(f"   📋 [API监控] 发现虚拟账户名: {found_virtual}")

                self._save_api_log(api_info)
                self._update_statistics(api_info)
                print(f"✅ [API监控] API调用记录完成: {api_path}")
                return api_info
            else:
                print(f"⚠️ [API监控] 无法提取API信息: {url}")

        except Exception as e:
            print(f"❌ [API监控] 记录API调用失败: {e}")
            import traceback
            print(f"详细错误: {traceback.format_exc()}")

        return None
    
    def _extract_api_info(self, flow) -> Optional[Dict]:
        """提取API信息"""
        try:
            url = flow.request.url
            parsed_url = urlparse(url)
            path = parsed_url.path
            
            # 过滤静态资源
            if self._is_static_resource(path):
                return None
            
            # 提取请求信息
            request_info = {
                'method': flow.request.method,
                'path': path,
                'query_params': dict(parse_qs(parsed_url.query)),
                'headers': dict(flow.request.headers),
                'body': self._safe_get_request_body(flow)
            }
            
            # 提取响应信息
            response_info = None
            if hasattr(flow, 'response') and flow.response:
                response_info = {
                    'status_code': flow.response.status_code,
                    'headers': dict(flow.response.headers),
                    'body': self._safe_get_response_body(flow),
                    'data_structure': self._analyze_response_structure(flow)
                }
            
            api_info = {
                'timestamp': datetime.now().isoformat(),
                'session_id': self.session_id,
                'url': url,
                'api_path': path,
                'request': request_info,
                'response': response_info,
                'api_type': self._classify_api_type(path, request_info, response_info)
            }
            
            return api_info
            
        except Exception as e:
            print(f"❌ 提取API信息失败: {e}")
            return None
    
    def _is_static_resource(self, path: str) -> bool:
        """判断是否为静态资源"""
        static_extensions = ['.js', '.css', '.png', '.jpg', '.ico', '.woff', '.ttf']
        return any(path.endswith(ext) for ext in static_extensions)
    
    def _safe_get_request_body(self, flow) -> Optional[str]:
        """安全获取请求体"""
        try:
            if hasattr(flow.request, 'text') and flow.request.text:
                body = flow.request.text
                # 限制body长度，避免过大的数据
                if len(body) > 10000:
                    return body[:10000] + '...[truncated]'
                return body
        except Exception:
            pass
        return None
    
    def _safe_get_response_body(self, flow) -> Optional[str]:
        """安全获取响应体"""
        try:
            if hasattr(flow.response, 'text') and flow.response.text:
                body = flow.response.text
                # 限制body长度
                if len(body) > 10000:
                    return body[:10000] + '...[truncated]'
                return body
        except Exception:
            pass
        return None
    
    def _analyze_response_structure(self, flow) -> Optional[Dict]:
        """分析响应数据结构"""
        try:
            if hasattr(flow.response, 'text') and flow.response.text:
                response_data = json.loads(flow.response.text)
                return self._extract_structure_info(response_data)
        except Exception:
            pass
        return None
    
    def _extract_structure_info(self, data, max_depth=3, current_depth=0) -> Dict:
        """递归提取数据结构信息"""
        if current_depth >= max_depth:
            return {"type": type(data).__name__, "truncated": True}
            
        if isinstance(data, dict):
            structure = {"type": "dict", "keys": []}
            for key, value in data.items():
                structure["keys"].append({
                    "name": key,
                    "structure": self._extract_structure_info(value, max_depth, current_depth + 1)
                })
            return structure
        elif isinstance(data, list):
            structure = {"type": "list", "length": len(data)}
            if data:  # 分析第一个元素的结构
                structure["item_structure"] = self._extract_structure_info(data[0], max_depth, current_depth + 1)
            return structure
        else:
            return {"type": type(data).__name__, "sample": str(data)[:100]}
    
    def _classify_api_type(self, path: str, request_info: Dict, response_info: Optional[Dict]) -> str:
        """分类API类型"""
        path_lower = path.lower()
        
        # 根据路径关键词分类
        if 'account' in path_lower or 'balance' in path_lower:
            return 'balance'
        elif 'project' in path_lower:
            return 'project'
        elif 'promotion' in path_lower or 'ad' in path_lower:
            return 'promotion'
        elif 'material' in path_lower:
            return 'material'
        elif 'statistics' in path_lower or 'stat' in path_lower:
            return 'statistics'
        elif 'dashboard' in path_lower:
            return 'dashboard'
        else:
            return 'unknown'
    
    def _save_api_log(self, api_info: Dict):
        """保存API日志"""
        try:
            log_file = os.path.join(self.log_dir, f"{self.session_id}_apis.jsonl")
            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(api_info, ensure_ascii=False) + '\n')
        except Exception as e:
            print(f"❌ 保存API日志失败: {e}")
    
    def _update_statistics(self, api_info: Dict):
        """更新统计信息"""
        api_type = api_info.get('api_type', 'unknown')
        self.api_statistics[api_type] += 1
        
        # 记录API模式
        path = api_info.get('api_path', '')
        self.api_patterns[api_type].append(path)
    
    def get_session_summary(self) -> Dict:
        """获取会话摘要"""
        summary = {
            'session_id': self.session_id,
            'total_apis': sum(self.api_statistics.values()),
            'api_types': dict(self.api_statistics),
            'timestamp': datetime.now().isoformat()
        }
        
        return summary
    
    def export_patterns_config(self) -> Dict:
        """导出API模式配置"""
        config = {
            "api_patterns": {},
            "generated_at": datetime.now().isoformat(),
            "session_id": self.session_id
        }
        
        for api_type, paths in self.api_patterns.items():
            # 去重并生成模式
            unique_paths = list(set(paths))
            config["api_patterns"][api_type] = {
                "patterns": unique_paths,
                "type": api_type,
                "handler": f"modify_{api_type}",
                "count": len(paths)
            }
        
        return config
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n🔍 API监控统计:")
        print("=" * 40)
        total = sum(self.api_statistics.values())
        print(f"总API调用数: {total}")
        
        for api_type, count in self.api_statistics.items():
            percentage = (count / total * 100) if total > 0 else 0
            print(f"{api_type}: {count} ({percentage:.1f}%)") 