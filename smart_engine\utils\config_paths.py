#!/usr/bin/env python3
"""
📁 配置路径管理器
简单的配置文件路径管理，消除硬编码路径
"""
import os
from typing import Optional

class ConfigPaths:
    """配置路径管理器"""
    
    # 基础目录
    SMART_ENGINE_CONFIG = "smart_engine/config"
    ACCOUNTS_CONFIG = "configs/accounts"
    SYSTEM_CONFIG = "config"
    
    @classmethod
    def api_rules_config(cls) -> str:
        """API规则配置文件路径"""
        return os.path.join(cls.SMART_ENGINE_CONFIG, "api_rules_config.json").replace("\\", "/")

    @classmethod
    def multi_account_mapping(cls) -> str:
        """多账户映射配置文件路径"""
        return os.path.join(cls.SMART_ENGINE_CONFIG, "multi_account_mapping.json").replace("\\", "/")

    @classmethod
    def account_metrics(cls, account_id: str) -> str:
        """账户专用配置文件路径"""
        return os.path.join(cls.ACCOUNTS_CONFIG, f"metrics_{account_id}.json").replace("\\", "/")
    
    @classmethod
    def system_config(cls) -> str:
        """系统配置文件路径"""
        return os.path.join(cls.SYSTEM_CONFIG, "system_config.json")
    
    @classmethod
    def config_version(cls) -> str:
        """配置版本文件路径"""
        return "config_version.json"
    
    @classmethod
    def ensure_directories(cls) -> None:
        """确保所有配置目录存在"""
        directories = [
            cls.SMART_ENGINE_CONFIG,
            cls.ACCOUNTS_CONFIG,
            cls.SYSTEM_CONFIG,
            "data/logs"  # 添加日志目录
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def exists(cls, path: str) -> bool:
        """检查配置文件是否存在"""
        return os.path.exists(path)
    
    @classmethod
    def list_account_configs(cls) -> list:
        """列出所有账户配置文件"""
        if not os.path.exists(cls.ACCOUNTS_CONFIG):
            return []
        
        configs = []
        for filename in os.listdir(cls.ACCOUNTS_CONFIG):
            if filename.startswith("metrics_") and filename.endswith(".json"):
                account_id = filename.replace("metrics_", "").replace(".json", "")
                configs.append({
                    'account_id': account_id,
                    'filename': filename,
                    'path': os.path.join(cls.ACCOUNTS_CONFIG, filename)
                })
        
        return configs


# 便捷函数
def get_api_rules_config_path() -> str:
    """获取API规则配置文件路径"""
    return ConfigPaths.api_rules_config()

def get_multi_account_mapping_path() -> str:
    """获取多账户映射配置文件路径"""
    return ConfigPaths.multi_account_mapping()

def get_account_metrics_path(account_id: str) -> str:
    """获取账户专用配置文件路径"""
    return ConfigPaths.account_metrics(account_id)

def get_system_config_path() -> str:
    """获取系统配置文件路径"""
    return ConfigPaths.system_config()

def ensure_config_directories() -> None:
    """确保所有配置目录存在"""
    ConfigPaths.ensure_directories()
