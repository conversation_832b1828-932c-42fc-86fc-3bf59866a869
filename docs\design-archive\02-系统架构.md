# 🏗️ 系统架构

## 🎯 整体架构概览

本系统采用**轻量级一体化架构**设计，避免过度设计，确保快速开发和简单维护。

### 🏗️ 架构决策：一体化 vs 分离式

#### ✅ 采用一体化架构的原因
1. **项目规模适中**：核心功能集中，预计300-500行代码，适合单体应用
2. **部署简单**：一个Flask应用包含所有功能，零配置部署
3. **维护成本低**：避免前后端分离的跨域、API版本管理等复杂性
4. **符合原则**：遵循"避免过度设计"的开发原则

#### 🏛️ 一体化架构设计

```
┌─────────────────────────────────────────────┐
│              双进程架构                      │
├─────────────────────────────────────────────┤
│  进程1: mitmproxy代理服务 (端口:8080)        │
│  🔄 HTTP拦截 → 🧠 API识别 → 🎨 响应修改      │
└─────────────────────────────────────────────┘
                       ↕ (SQLite通信)
┌─────────────────────────────────────────────┐
│  进程2: Flask一体化Web服务 (端口:5000)       │
│  📱 Jinja2模板 + Vue.js CDN + 🎨 Element Plus │
│  🚀 API接口 + 📊 静态文件 + 💾 数据管理       │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│                 数据层                      │
│  💾 SQLite数据库 (进程间通信)                │
└─────────────────────────────────────────────┘
```

### 🎨 前端实现方式

#### **技术组合：Jinja2 + Vue.js CDN**
```html
<!-- 示例模板结构 -->
<!DOCTYPE html>
<html>
<head>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <div id="app">
        <!-- Vue组件在Jinja2模板中 -->
        <el-config-panel :metrics="{{metrics|tojson}}"></el-config-panel>
    </div>
    <script>
        // Vue应用初始化
        const { createApp } = Vue
        createApp({
            data() {
                return {
                    metrics: {{metrics|tojson}}
                }
            }
        }).mount('#app')
    </script>
</body>
</html>
```

#### **数据交互方式**
- **页面渲染**：Jinja2模板传递服务器数据到前端
- **动态交互**：Vue.js通过Ajax调用Flask API接口
- **状态管理**：前端Vue组件 + 后端Session

### 🏛️ 架构层次

```
┌─────────────────────────────────────────────┐
│                用户界面层                     │
│  🖥️ 广告后台界面  📱 管理Web界面               │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│                 代理层                      │
│       🔄 mitmproxy (端口:8080)              │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│                业务处理层                    │
│  🧠 API识别引擎  🎯 规则引擎  🎨 响应生成器     │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│                 服务层                      │
│    🚀 Flask Web服务 (端口:5000)             │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│                 数据层                      │
│  💾 SQLite数据库  📄 配置文件  📋 日志文件     │
└─────────────────────────────────────────────┘
```

## 🔧 核心模块设计

### 1. **🧠 智能API识别引擎**

#### 设计目标
- 自动识别API类型（项目、广告、统计等）
- 支持90%以上的API自动识别
- 处理速度快，内存占用低

#### 核心组件

##### **URL模式匹配器**
- **功能**：基于URL路径进行初步分类
- **匹配规则**：
  - 项目API：`/project/*`, `/promote/project/*`
  - 广告API：`/promotion/*`, `/ads/*`
  - 统计API：`/statistics/*`, `/metrics/*`
- **优先级**：高（第一优先级识别）

##### **内容分析器**
- **功能**：分析响应内容中的关键字段
- **识别字段**：
  - 状态字段：`*_status_*`, `*_name`
  - 指标字段：`metrics`, `*_cnt`, `*_cost`
- **触发条件**：URL匹配失败时启用

##### **规则匹配器**
- **功能**：应用自定义匹配规则
- **配置方式**：JSON文件配置
- **扩展性**：支持运行时添加新规则

#### 处理流程

```
HTTP请求 → URL分析 → 内容分析 → 规则匹配 → API类型确定
    ↓         ↓         ↓         ↓         ↓
  获取URL → 模式匹配 → 字段扫描 → 自定义规则 → 分类结果
```

### 2. **🎯 智能规则引擎**

#### 设计原则
- **配置驱动**：通过JSON配置定义处理规则
- **热更新**：支持不重启的规则更新
- **优先级管理**：支持规则优先级和冲突解决

#### 规则类型

##### **字段映射规则**
```json
{
  "field_mappings": {
    "promotion_status_first_name": {
      "target_value": "启用中",
      "condition": "always"
    },
    "project_status_name": {
      "target_value": "投放中", 
      "condition": "if_not_empty"
    }
  }
}
```

##### **数据注入规则**
```json
{
  "data_injection": {
    "metrics": {
      "source": "database",
      "table": "metrics_config",
      "mapping": {
        "show_cnt": "show_count",
        "click_cnt": "click_count"
      }
    }
  }
}
```

##### **条件处理规则**
```json
{
  "conditional_rules": [
    {
      "condition": "url_contains('/project/')",
      "actions": ["apply_project_status", "inject_project_metrics"]
    },
    {
      "condition": "response_contains('promotion_status')",
      "actions": ["apply_promotion_status", "clear_secondary_status"]
    }
  ]
}
```

### 3. **🎨 响应生成引擎**

#### 设计目标
- 确保生成的响应符合巨量引擎API格式
- 支持复杂嵌套结构的数据处理
- 保持原始响应的完整性

#### 核心机制

##### **模板系统**
- **JSON模板**：预定义的响应结构模板
- **变量替换**：支持动态数据注入
- **条件渲染**：根据条件决定是否渲染某些字段

##### **数据合并策略**
- **原始数据优先**：保留原始响应的基础结构
- **配置数据覆盖**：用配置的数据覆盖特定字段
- **智能合并**：自动处理数据类型转换

##### **完整性验证**
- **结构验证**：确保响应结构完整
- **类型验证**：验证数据类型正确性
- **业务验证**：验证业务逻辑合理性

## 🔄 数据流架构

### 📊 请求处理流程

```mermaid
sequenceDiagram
    participant User as 👤用户浏览器
    participant Proxy as 🔄mitmproxy
    participant Engine as 🧠识别引擎
    participant Rules as 🎯规则引擎
    participant Generator as 🎨生成引擎
    participant DB as 💾数据库
    participant Server as 🌐原始服务器

    User->>Proxy: HTTP请求
    Proxy->>Server: 转发请求
    Server->>Proxy: 原始响应
    
    Proxy->>Engine: 分析请求
    Engine->>Engine: URL模式匹配
    Engine->>Engine: 内容分析
    Engine->>Rules: API类型识别结果
    
    Rules->>DB: 查询配置规则
    DB->>Rules: 返回规则配置
    Rules->>Rules: 应用处理规则
    
    Rules->>Generator: 处理指令
    Generator->>DB: 获取数据配置
    DB->>Generator: 返回配置数据
    Generator->>Generator: 生成新响应
    
    Generator->>Proxy: 修改后响应
    Proxy->>User: 返回用户
```

### 🎛️ 配置管理流程

```mermaid
sequenceDiagram
    participant Admin as 👨‍💼管理员
    participant WebUI as 🎨Web界面
    participant Flask as 🚀Flask服务
    participant DB as 💾数据库
    participant Proxy as 🔄代理服务

    Admin->>WebUI: 修改配置
    WebUI->>Flask: 提交变更
    Flask->>DB: 更新数据
    DB->>Flask: 确认更新
    Flask->>WebUI: 返回结果
    WebUI->>Admin: 显示成功

    Flask->>Proxy: 通知配置更新
    Proxy->>Proxy: 热重载配置
    Proxy->>Flask: 确认更新
```

## 🏢 模块间通信架构

### 📡 通信方式

#### **同步通信**
- **Web界面 ↔ Flask服务**：HTTP REST API
- **代理服务 ↔ 规则引擎**：函数调用
- **规则引擎 ↔ 数据库**：SQL查询

#### **异步通信**
- **配置更新通知**：事件驱动
- **日志记录**：异步写入
- **监控数据收集**：定时任务

### 🔌 接口设计

#### **API识别引擎接口**
```python
class APIDetector:
    def detect_api_type(url: str, content: dict) -> str
    def get_confidence_score() -> float
    def add_custom_rule(pattern: str, api_type: str) -> bool
```

#### **规则引擎接口**
```python
class RuleEngine:
    def apply_rules(api_type: str, response: dict) -> dict
    def load_rules_from_db() -> bool
    def validate_rule(rule: dict) -> bool
```

#### **响应生成器接口**
```python
class ResponseGenerator:
    def generate_response(template: str, data: dict) -> dict
    def merge_data(original: dict, override: dict) -> dict
    def validate_response(response: dict) -> bool
```

## 🗄️ 数据架构设计

### 📊 数据库设计

#### **核心数据表**

##### **metrics_config（指标配置表）**
```sql
CREATE TABLE metrics_config (
    id INTEGER PRIMARY KEY,
    show_count INTEGER NOT NULL,
    click_count INTEGER NOT NULL, 
    convert_count INTEGER NOT NULL,
    stat_cost REAL NOT NULL,
    ctr REAL COMPUTED,
    conversion_rate REAL COMPUTED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **api_rules（API规则表）**
```sql
CREATE TABLE api_rules (
    id INTEGER PRIMARY KEY,
    rule_name TEXT NOT NULL,
    url_pattern TEXT NOT NULL,
    api_type TEXT NOT NULL,
    field_mappings TEXT NOT NULL, -- JSON格式
    conditions TEXT, -- JSON格式
    priority INTEGER DEFAULT 100,
    is_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

##### **processing_logs（处理日志表）**
```sql
CREATE TABLE processing_logs (
    id INTEGER PRIMARY KEY,
    url TEXT NOT NULL,
    api_type TEXT,
    processing_time_ms INTEGER,
    success BOOLEAN,
    error_message TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 📄 配置文件结构

#### **主配置文件 (config/main.json)**
```json
{
  "system": {
    "proxy_port": 8080,
    "web_port": 5000,
    "log_level": "INFO"
  },
  "detection": {
    "url_patterns": {
      "project": ["/project", "/promote/project"],
      "promotion": ["/promotion", "/ads"],
      "statistics": ["/statistics", "/metrics"]
    },
    "content_keywords": {
      "status_fields": ["status_name", "status_first_name"],
      "metrics_fields": ["show_cnt", "click_cnt", "metrics"]
    }
  }
}
```

#### **规则配置文件 (config/rules.json)**
```json
{
  "global_rules": {
    "status_normalization": {
      "已暂停": "启用中",
      "未投放": "启用中", 
      "审核中": "投放中"
    }
  },
  "api_specific_rules": {
    "project": {
      "clear_fields": ["project_status_second", "project_status_second_name"],
      "set_fields": {
        "project_status_first_name": "启用中",
        "project_status_name": "投放中"
      }
    }
  }
}
```

## 🔧 系统集成架构

### 🔗 与现有系统集成

#### **mitmproxy集成**
- **集成方式**：插件模式
- **数据传递**：HTTP流对象
- **性能考虑**：异步处理，避免阻塞

#### **现有工具函数复用**
- **ad_utils.py**：计算函数直接复用
- **配置数据**：平滑迁移到数据库
- **处理逻辑**：重构为规则配置

### ⚡ 性能优化架构

#### **缓存策略**
- **规则缓存**：内存缓存热点规则
- **数据缓存**：缓存常用配置数据
- **响应缓存**：缓存相同请求的响应

#### **并发处理**
- **多线程处理**：并发处理多个HTTP请求
- **异步操作**：数据库操作异步化
- **连接池**：数据库连接池管理

## 🚀 扩展架构设计

### 🔮 未来扩展考虑

#### **多平台支持**
- **抽象层设计**：平台无关的核心引擎
- **插件架构**：每个平台一个插件
- **配置隔离**：不同平台独立配置

#### **分布式架构**
- **服务拆分**：核心服务可独立部署
- **负载均衡**：支持多实例部署
- **数据同步**：多实例间配置同步

#### **云原生支持**
- **容器化**：Docker容器部署
- **配置中心**：外部配置管理
- **监控集成**：Prometheus监控指标

## 📊 架构质量保证

### ✅ 可维护性

- **分层清晰**：每层职责单一，耦合度低
- **接口标准**：统一的接口设计规范
- **文档完整**：每个模块都有详细文档

### ✅ 可扩展性

- **插件架构**：支持功能插件化扩展
- **配置驱动**：通过配置实现功能变更
- **版本兼容**：向后兼容的接口设计

### ✅ 可测试性

- **单元测试**：每个模块独立可测试
- **集成测试**：端到端流程验证
- **性能测试**：关键路径性能验证

### ✅ 可监控性

- **日志记录**：完整的操作日志
- **指标收集**：关键性能指标监控
- **告警机制**：异常情况及时告警

这个架构设计确保了系统的**高性能、高可用、易维护**，为项目的长期发展奠定了坚实的技术基础。 