import json
from mitmproxy import http

class CommonHandler:
    """通用处理器"""
    
    def __init__(self, metrics_handler):
        """初始化通用处理器
        
        Args:
            metrics_handler: Metrics处理器实例
        """
        self.metrics_handler = metrics_handler
    
    def generic_handler(self, flow: http.HTTPFlow, api_info: dict):
        """通用处理器 - 处理未实现特定处理器的API"""
        api_type = api_info.get('type')
        print(f"🔄 [新引擎] 使用通用处理器处理 {api_type} 类型API")
        
        try:
            response_data = json.loads(flow.response.text)
            
            # 递归更新状态字段
            self.metrics_handler.update_status_fields(response_data, api_type)
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 通用处理完成")
            
        except Exception as e:
            print(f"❌ [新引擎] 通用处理出错: {e}")
            raise e
    
    def handle_ad_promotion_list(self, flow: http.HTTPFlow, api_info: dict, promotion_handler):
        """修改广告计划列表API"""
        # 复用广告列表的处理逻辑
        promotion_handler(flow, api_info)
    
    def handle_ad_promotion_attribute(self, flow: http.HTTPFlow, api_info: dict, promotion_handler):
        """修改广告计划属性API"""
        # 复用广告列表的处理逻辑  
        promotion_handler(flow, api_info)
    
    def handle_ad_materials(self, flow: http.HTTPFlow, api_info: dict, material_handler):
        """修改广告素材API"""
        # 复用素材列表的处理逻辑
        material_handler(flow, api_info)
    
    def log_error(self, flow: http.HTTPFlow, error: Exception):
        """记录错误日志"""
        with open('erro_response.txt', 'a', encoding='utf-8') as file:
            file.write(f"\n[新引擎错误]\n")
            file.write(f"URL: {flow.request.url}\n")
            file.write(f"Error: {str(error)}\n")
            file.write(f"Response: {flow.response.text[:200]}...\n")
            file.write('----------------------------\n') 