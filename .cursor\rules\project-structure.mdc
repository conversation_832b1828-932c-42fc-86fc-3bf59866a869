---
description: 
globs: *.py
alwaysApply: false
---
# 大屏智能引擎项目结构指南

## 项目概览
这是一个智能引擎项目，主要用于数据处理和分析。

## 核心入口点
- **主启动器**: [launcher.py](mdc:launcher.py) - 项目的主要入口点
- **调试工具**: [debug_account_mapping.py](mdc:debug_account_mapping.py) - 账户映射调试脚本
- **依赖管理**: [requirements.txt](mdc:requirements.txt) - Python依赖包列表

## 主要目录结构

### 核心引擎 (`smart_engine/`)
- `engine/` - 核心引擎逻辑
- `config/` - 引擎配置文件
  - `backup/` - 配置备份
  - `generated/` - 生成的配置文件
- `utils/` - 引擎工具函数

### 源代码 (`src/`)
- `commands/` - 命令行指令实现
- `core/` - 核心业务逻辑
- `generators/` - 代码生成器

### 数据存储 (`data/`)
- `api_logs/` - API调用日志
- `logs/` - 系统日志文件

### 测试 (`tests/`)
- `test_data/` - 测试数据
  - `accounts/` - 账户测试数据
  - `api_responses/` - API响应测试数据
  - `metrics/` - 指标测试数据

### 文档 (`docs/`)
- `api/` - API文档
- `design-archive/` - 设计归档
- `technical/` - 技术文档
- `user-guide/` - 用户指南

### 其他目录
- `config/` - 项目配置文件
- `launchers/` - 启动脚本
- `script/` - 辅助脚本
- `tools/` - 开发工具

## 开发规范

### 文件长度限制
- 每个代码文件不得超过200行
- 超过限制时需要拆分为多个模块
- 特殊情况需要在代码注释中说明

### 架构原则
- 避免过度设计
- 模块化开发
- 保持代码简洁可读

## 开发工作流
1. 使用 [launcher.py](mdc:launcher.py) 启动主程序
2. 配置文件位于 `smart_engine/config/` 和 `config/` 目录
3. 日志文件输出到 `data/logs/` 目录
4. 测试数据和用例位于 `tests/` 目录

## 关键文件说明
- 备份文件：`metrics.json.backup_20250612_231816` - 指标数据备份
- 项目说明：[README.md](mdc:README.md) - 项目整体说明文档

