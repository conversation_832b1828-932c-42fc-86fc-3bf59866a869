#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据一致性验证脚本
验证多账户列表页面和单账户页面数据是否一致
"""

import json
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(__file__)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_data_consistency():
    """测试数据一致性"""
    print("🔍 开始数据一致性验证...")
    
    # 1. 检查全局配置文件
    print("\n📋 1. 检查全局配置文件 (metrics.json)")
    try:
        with open('metrics.json', 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        print(f"   ✅ 全局配置: {global_config.get('Account Name', '未知')}")
        print(f"   📊 展示: {global_config.get('Show Count', 0):,}")
        print(f"   💰 消耗: ¥{global_config.get('Stat Cost', 0)}")
    except Exception as e:
        print(f"   ❌ 全局配置读取失败: {e}")
        return False
    
    # 2. 检查专用配置文件
    print("\n📋 2. 检查专用配置文件 (configs/accounts/)")
    accounts_dir = 'configs/accounts'
    if not os.path.exists(accounts_dir):
        print(f"   ❌ 专用配置目录不存在: {accounts_dir}")
        return False
    
    account_configs = {}
    total_cost = 0.0
    total_show = 0
    total_click = 0
    total_convert = 0
    
    for filename in os.listdir(accounts_dir):
        if filename.startswith('metrics_') and filename.endswith('.json'):
            account_id = filename.replace('metrics_', '').replace('.json', '')
            config_path = os.path.join(accounts_dir, filename)
            
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                account_configs[account_id] = config
                cost = float(config.get('Stat Cost', 0))
                show = int(config.get('Show Count', 0))
                click = int(config.get('Click Count', 0))
                convert = int(config.get('Convert Count', 0))
                
                total_cost += cost
                total_show += show
                total_click += click
                total_convert += convert
                
                print(f"   ✅ 账户 {account_id}: {config.get('Account Name', '未知')}")
                print(f"      📊 展示: {show:,}, 💰 消耗: ¥{cost:.2f}")
                
            except Exception as e:
                print(f"   ❌ 账户 {account_id} 配置读取失败: {e}")
    
    # 3. 数据一致性分析
    print("\n🔍 3. 数据一致性分析")
    print(f"   📈 汇总数据:")
    print(f"      总展示: {total_show:,}")
    print(f"      总消耗: ¥{total_cost:.2f}")
    print(f"      总点击: {total_click:,}")
    print(f"      总转化: {total_convert}")
    
    # 4. 测试多账户数据处理器
    print("\n🧪 4. 测试多账户数据处理器")
    try:
        from smart_engine.engine.multi_account_data_processor import MultiAccountDataProcessor
        from smart_engine.config.multi_account_mapping import load_multi_account_mappings
        
        mappings = load_multi_account_mappings()
        processor = MultiAccountDataProcessor(mappings)
        
        # 测试概览数据生成
        overview_data = processor.get_virtual_overview_data('feed')
        if overview_data:
            print(f"   ✅ 概览数据生成成功:")
            print(f"      展示: {overview_data.get('show_cnt', 0):,}")
            print(f"      消耗: ¥{overview_data.get('stat_cost', 0):.2f}")
            print(f"      点击: {overview_data.get('click_cnt', 0):,}")
            print(f"      转化: {overview_data.get('convert_cnt', 0)}")
        else:
            print(f"   ❌ 概览数据生成失败")
            
        # 测试账户数据聚合
        aggregated_data = processor.aggregate_virtual_performance_data()
        if aggregated_data:
            print(f"   ✅ 账户数据聚合成功: {len(aggregated_data)} 个账户")
        else:
            print(f"   ❌ 账户数据聚合失败")
            
    except Exception as e:
        print(f"   ❌ 多账户数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. 数据源冲突检查
    print("\n⚠️ 5. 数据源冲突检查")
    
    # 检查全局配置与专用配置的差异
    global_show = global_config.get('Show Count', 0)
    global_cost = global_config.get('Stat Cost', 0)
    
    if total_show != global_show:
        print(f"   🔴 展示数据不一致: 全局({global_show:,}) vs 汇总({total_show:,})")
    else:
        print(f"   ✅ 展示数据一致: {total_show:,}")
        
    if abs(total_cost - global_cost) > 0.01:
        print(f"   🔴 消耗数据不一致: 全局(¥{global_cost}) vs 汇总(¥{total_cost:.2f})")
    else:
        print(f"   ✅ 消耗数据一致: ¥{total_cost:.2f}")
    
    print("\n🎯 数据一致性验证完成!")
    return True

if __name__ == "__main__":
    test_data_consistency()
