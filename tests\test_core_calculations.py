#!/usr/bin/env python3
"""
🧪 核心计算逻辑测试模块
测试广告指标计算、工具函数、涨幅数据生成等核心算法

从原有拆分文件迁移的测试：
- test_regression_suite.py: test_02_ad_metrics_calculation
- test_regression_suite.py: test_03_utility_functions  
- test_regression_suite.py: test_04_ratio_generation
"""

import os
import sys
import json
import unittest
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.utils import ad_utils

class TestCoreCalculations(unittest.TestCase):
    """核心计算逻辑测试套件"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_ad_metrics_calculation_data_driven(self):
        """测试广告指标计算功能 - 数据驱动版本"""
        print("\n🧪 测试广告指标计算（数据驱动）")
        
        # 获取所有测试用例
        test_cases = test_data_loader.get_test_metrics()
        
        for test_case in test_cases:
            case_name = test_case.get('name', 'unknown')
            input_data = test_case.get('input', {})
            expected = test_case.get('expected', {})
            
            print(f"  🔍 测试场景: {case_name}")
            
            # 执行计算
            metrics = ad_utils.calculate_ad_metrics(
                input_data.get('show_cnt', 0),
                input_data.get('click_cnt', 0), 
                input_data.get('convert_cnt', 0),
                input_data.get('stat_cost', 0.0)
            )
            
            # 验证计算结果
            for key, expected_value in expected.items():
                self.assertEqual(metrics[key], expected_value, 
                    f"场景 {case_name}: {key} 期望 {expected_value}, 实际 {metrics[key]}")
            
            # 验证比较数据生成（非零值情况）
            if input_data.get('show_cnt', 0) > 0:
                self.assertIn('Comparison Data', metrics)
                self.assertIn('CTR', metrics['Comparison Data'])
        
        print("✅ 广告指标计算测试通过（所有数据驱动场景）")

    def test_ad_metrics_calculation_edge_cases(self):
        """测试广告指标计算的边界情况"""
        print("\n🧪 测试广告指标计算边界情况")
        
        # 测试零值情况
        zero_metrics = ad_utils.calculate_ad_metrics(0, 0, 0, 0.0)
        self.assertEqual(zero_metrics['CTR (%)'], 0.0)
        self.assertEqual(zero_metrics['Conversion Rate (%)'], 0.0)
        self.assertEqual(zero_metrics['CPM'], 0.0)
        self.assertEqual(zero_metrics['CPC'], 0.0)
        
        # 测试除零保护
        division_test = ad_utils.calculate_ad_metrics(0, 100, 50, 100.0)
        self.assertEqual(division_test['CTR (%)'], 0.0)  # 0展现时CTR应为0
        
        # 测试大数值
        large_metrics = ad_utils.calculate_ad_metrics(1000000, 50000, 5000, 10000.0)
        self.assertEqual(large_metrics['CTR (%)'], 5.0)
        self.assertEqual(large_metrics['Conversion Rate (%)'], 10.0)
        
        print("✅ 边界情况测试通过")

    def test_utility_functions(self):
        """测试工具函数"""
        print("\n🧪 测试工具函数")
        
        # 测试嵌套字典查找
        nested_dict = {
            'level1': {
                'level2': {
                    'target_key': 'found'
                }
            },
            'other_key': 'value'
        }
        
        self.assertTrue(ad_utils.check_nested_dict_for_key(nested_dict, 'target_key'))
        self.assertTrue(ad_utils.check_nested_dict_for_key(nested_dict, 'level2'))
        self.assertFalse(ad_utils.check_nested_dict_for_key(nested_dict, 'nonexistent'))
        
        # 测试空字典
        self.assertFalse(ad_utils.check_nested_dict_for_key({}, 'any_key'))
        
        # 测试数字格式化
        formatted_int = ad_utils.format_number_with_commas_int("12345")
        self.assertEqual(formatted_int, "12,345")
        
        formatted_float = ad_utils.format_number_with_commas("12345.67")
        self.assertEqual(formatted_float, "12,345.67")
        
        # 测试无效输入
        invalid_result = ad_utils.format_number_with_commas("invalid")
        self.assertEqual(invalid_result, "Invalid input")
        
        # 测试边界值
        zero_result = ad_utils.format_number_with_commas("0")
        self.assertIn(zero_result, ["0", "0.00"])  # 允许两种格式
        
        print("✅ 工具函数测试通过")

    def test_ratio_generation(self):
        """测试涨幅数据生成"""
        print("\n🧪 测试涨幅数据生成")
        
        # 测试不同指标的涨幅生成
        ctr_ratio = ad_utils.generate_ratio_and_ratio_str('CTR (%)', 5.0)
        self.assertIn('Value', ctr_ratio)
        self.assertIn('Ratio', ctr_ratio)
        self.assertIn('RatioStr', ctr_ratio)
        self.assertTrue(ctr_ratio['Ratio'] > 0)  # CTR应该是正向增长
        
        # 测试成本类指标（应该是负向，即降低）
        cost_ratio = ad_utils.generate_ratio_and_ratio_str('Conversion Cost', 2.5)
        self.assertTrue(cost_ratio['Ratio'] < 0)  # 转化成本应该降低
        
        # 测试CPC类指标
        cpc_ratio = ad_utils.generate_ratio_and_ratio_str('CPC', 0.5)
        self.assertTrue(cpc_ratio['Ratio'] < 0)  # CPC应该降低
        
        # 测试消耗类指标
        cost_total_ratio = ad_utils.generate_ratio_and_ratio_str('Stat Cost', 100.0)
        self.assertTrue(cost_total_ratio['Ratio'] > 0)  # 总消耗应该增长
        
        # 测试转化率指标
        conversion_ratio = ad_utils.generate_ratio_and_ratio_str('Conversion Rate (%)', 20.0)
        self.assertTrue(conversion_ratio['Ratio'] > 0)  # 转化率应该增长
        
        print("✅ 涨幅数据生成测试通过")

    def test_ratio_generation_edge_cases(self):
        """测试涨幅数据生成的边界情况"""
        print("\n🧪 测试涨幅数据生成边界情况")

        # 测试零值
        zero_ratio = ad_utils.generate_ratio_and_ratio_str('CTR (%)', 0.0)
        # 由于有随机比例，Value可能不是0.0，但应该是合理的数值
        self.assertIsInstance(zero_ratio['Value'], (int, float))
        self.assertIsInstance(zero_ratio['Ratio'], (int, float))

        # 测试负值（虽然实际业务中不太可能）
        negative_ratio = ad_utils.generate_ratio_and_ratio_str('CTR (%)', -1.0)
        # 由于有随机比例调整，Value会变化，但应该是数值类型
        self.assertIsInstance(negative_ratio['Value'], (int, float))
        self.assertIsInstance(negative_ratio['Ratio'], (int, float))

        # 测试未知指标类型 - 应该按默认规则处理（正向增长）
        unknown_ratio = ad_utils.generate_ratio_and_ratio_str('Unknown Metric', 10.0)
        self.assertIsInstance(unknown_ratio['Value'], (int, float))
        self.assertIsInstance(unknown_ratio['Ratio'], (int, float))
        self.assertTrue(unknown_ratio['Ratio'] > 0)  # 未知指标应该是正向增长

        print("✅ 涨幅数据生成边界情况测试通过")

def run_core_calculation_tests():
    """运行核心计算逻辑测试"""
    print("🚀 开始核心计算逻辑测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestCoreCalculations)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 核心计算测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有核心计算测试通过！")
    else:
        print("❌ 部分核心计算测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_core_calculation_tests()
