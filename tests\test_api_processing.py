#!/usr/bin/env python3
"""
🧪 API处理测试模块
专门测试API识别器、各种处理器、Handler路由验证等API处理功能
文件长度限制：200行以内

从原有拆分文件迁移的测试：
- test_regression_suite_middle.py: test_05_api_identifier
- test_regression_suite_middle.py: test_06_balance_handler
- test_regression_suite_final.py: test_07_statistics_handler
- test_regression_suite_final.py: test_08_metrics_handler
- test_handler_routing.py: 所有Handler路由测试
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.engine.api_identifier import APIIdentifier
from smart_engine.engine.balance_handler import BalanceHandler
from smart_engine.engine.statistics_handler import StatisticsHandler
from smart_engine.engine.metrics_handler import MetricsHandler

class TestAPIProcessing(unittest.TestCase):
    """API处理测试套件"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_api_identifier_basic(self):
        """测试API识别器基础功能"""
        print("\n🧪 测试API识别器基础功能")
        
        # 创建正确的配置格式
        default_config = {
            "api_patterns": {
                "balance_api": {
                    "type": "balance",
                    "handler": "modify_balance",
                    "patterns": ["/advertiser/info", "/fund/daily_stat"]
                },
                "statistics_api": {
                    "type": "statistics", 
                    "handler": "modify_statistics",
                    "patterns": ["/report/advertiser/get", "/report/custom"]
                }
            }
        }
        
        identifier = APIIdentifier(default_config)
        
        # 模拟HTTP流对象
        mock_flow = MagicMock()
        mock_flow.request.url = "https://ad.oceanengine.com/openapi/2/advertiser/info/"
        mock_flow.request.text = ""
        
        # 测试API识别
        api_info = identifier.identify_api(mock_flow)
        self.assertIsNotNone(api_info)
        self.assertEqual(api_info['type'], 'balance')
        self.assertEqual(api_info['handler'], 'modify_balance')
        
        print("✅ API识别器基础功能测试通过")

    def test_api_identifier_pattern_matching(self):
        """测试API识别器模式匹配"""
        print("\n🧪 测试API识别器模式匹配")
        
        config = {
            "api_patterns": {
                "test_api": {
                    "type": "test",
                    "handler": "modify_test",
                    "patterns": ["/test/pattern", "/another/pattern"]
                }
            }
        }
        
        identifier = APIIdentifier(config)
        mock_flow = MagicMock()
        
        # 测试匹配的URL
        mock_flow.request.url = "https://example.com/test/pattern"
        api_info = identifier.identify_api(mock_flow)
        self.assertIsNotNone(api_info)
        self.assertEqual(api_info['type'], 'test')
        
        # 测试不匹配的URL
        mock_flow.request.url = "https://example.com/unknown/api"
        api_info_unknown = identifier.identify_api(mock_flow)
        self.assertIsNone(api_info_unknown)
        
        print("✅ API识别器模式匹配测试通过")

    def test_balance_handler_basic(self):
        """测试余额处理器基础功能"""
        print("\n🧪 测试余额处理器基础功能")
        
        handler = BalanceHandler(self.test_metrics)
        
        # 获取真实API响应数据  
        api_responses = test_data_loader.get_api_responses('balance_api')
        
        # 测试成功响应场景
        success_response = api_responses.get('success', {})
        if success_response:
            mock_flow = MagicMock()
            mock_flow.response.text = json.dumps(success_response)
            
            api_info = {"type": "balance", "handler": "modify_balance"}
            handler.modify_balance(mock_flow, api_info)
            
            # 验证处理器被调用
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 真实响应场景测试通过")
        else:
            # 回退到原有测试
            mock_flow = MagicMock()
            mock_response_data = {
                "data": {
                    "advertiser_name": "原始账户名", 
                    "advertiser_id": "原始ID",
                    "balance": 1000.0
                }
            }
            mock_flow.response.text = json.dumps(mock_response_data)
            
            api_info = {"type": "balance", "handler": "modify_balance"}
            handler.modify_balance(mock_flow, api_info)
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 默认测试场景通过")
        
        print("✅ 余额处理器基础功能测试通过")

    def test_statistics_handler_basic(self):
        """测试统计处理器基础功能"""
        print("\n🧪 测试统计处理器基础功能")
        
        handler = StatisticsHandler(self.test_metrics)
        
        # 获取真实API响应数据
        api_responses = test_data_loader.get_api_responses('statistics_api')
        
        # 测试成功响应场景
        success_response = api_responses.get('success', {})
        if success_response:
            mock_flow = MagicMock()
            mock_flow.response.text = json.dumps(success_response)
            
            api_info = {"type": "statistics", "handler": "modify_statistics"}
            handler.modify_statistics(mock_flow, api_info)
            
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 真实响应场景测试通过")
        else:
            # 回退到原有测试格式
            mock_flow = MagicMock()
            mock_response_data = {
                "data": {
                    "StatsData": {
                        "Totals": {
                            "stat_cost": {"Value": 50.0, "ValueStr": "50.0"},
                            "show_cnt": {"Value": 1000, "ValueStr": "1,000"}
                        },
                        "Rows": [
                            {
                                "Metrics": {
                                    "stat_cost": {"Value": 25.0, "ValueStr": "25.0"},
                                    "show_cnt": {"Value": 500, "ValueStr": "500"}
                                }
                            }
                        ]
                    }
                }
            }
            mock_flow.response.text = json.dumps(mock_response_data)
            
            api_info = {"type": "statistics", "handler": "modify_statistics"}
            handler.modify_statistics(mock_flow, api_info)
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 默认测试场景通过")
        
        print("✅ 统计处理器基础功能测试通过")

    def test_metrics_handler_basic(self):
        """测试指标处理器基础功能"""
        print("\n🧪 测试指标处理器基础功能")
        
        # 创建指标处理器需要的配置
        config = {
            "status_mappings": {
                "project": {
                    "fields": {"status": 1, "status_name": "投放中"}
                },
                "promotion": {
                    "fields": {"status": 1, "status_name": "启用中"}
                }
            },
            "metrics_template": {
                "basic": {
                    "show_cnt": "{Show Count}",
                    "stat_cost": "{Stat Cost}"
                }
            }
        }
        
        handler = MetricsHandler(config, self.test_metrics)
        
        # 测试指标数据构建
        metrics_data = handler.build_metrics_data()
        self.assertIn("show_cnt", metrics_data)
        self.assertIn("stat_cost", metrics_data)
        
        # 测试状态更新功能
        test_data = {"status": 0, "status_name": "暂停"}
        handler.update_status_fields(test_data, "project")
        self.assertEqual(test_data["status"], 1)
        self.assertEqual(test_data["status_name"], "投放中")
        
        print("✅ 指标处理器基础功能测试通过")

def run_api_processing_tests():
    """运行API处理测试"""
    print("🚀 开始API处理测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestAPIProcessing)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 API处理测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有API处理测试通过！")
    else:
        print("❌ 部分API处理测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_api_processing_tests()
