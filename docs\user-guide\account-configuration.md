# 🏢 账户信息配置功能说明

## 🎯 功能概述

巨量引擎智能API管理系统现已支持**自定义账户昵称和账户ID**功能，用户可以在启动时配置个性化的账户信息，系统会自动在广告后台页面显示用户自定义的账户信息。

## ✨ 新增功能

### 🔧 **用户输入增强**
- ✅ 新增账户昵称输入
- ✅ 新增账户ID输入  
- ✅ 输入验证和默认值处理
- ✅ 实时预览功能

### 🎛️ **API处理器增强**
- ✅ 新增 `modify_unknown` 处理器 - 处理用户会话API
- ✅ 增强 `modify_balance` 处理器 - 支持自定义账户信息
- ✅ 智能账户信息注入

### 📊 **支持的API端点**
| API类型 | API端点 | 修改内容 |
|---------|---------|----------|
| 用户会话 | `/copilot/api/v1/agw/session/human/latest` | 用户名、用户ID、广告主信息 |
| 账户余额 | `/platform/api/v1/statistics/dashboard/account_info/` | 广告主名称、广告主ID |
| 账户信息 | `/ad/api/account/info` | 账户基础信息 |
| 账户配置 | `/ad/api/account/conf` | 账户配置信息 |

## 🚀 使用方法

### 🔹 **方法一：主程序启动**

```bash
python mitmPro_AD.py
```

启动后会依次提示：

1. **账户信息配置**
   ```
   🏢 请输入账户信息：
   ========================================
   请输入自定义账户昵称 (例如: WH-我的广告公司): 
   请输入自定义账户ID (例如: ****************): 
   ```

2. **广告数据配置**
   ```
   🎯 请输入广告数据：
   ========================================
   请输入广告展示数: 
   请输入广告点击数: 
   请输入转化数: 
   请输入广告活动的总成本(消耗): 
   ```

### 🔹 **方法二：多账户模式**

```bash
python launcher.py
# 选择 2. 👥 多账户模式
```

多账户模式提供：
- 智能账户检测
- 动态配置切换
- 多账户数据隔离

## 📋 配置示例

### 🏢 **账户信息示例**

| 账户类型 | 账户昵称 | 账户ID |
|----------|----------|--------|
| 科技公司 | `WH-北京科技有限公司` | `****************` |
| 创新企业 | `WH-上海创新科技` | `****************` |
| 营销中心 | `WH-深圳数字营销` | `****************` |

### 📊 **完整配置文件结构 (metrics.json)**

```json
{
  "CTR (%)": 8.0,
  "Conversion Rate (%)": 6.25,
  "CPM": 150.0,
  "CPC": 1.88,
  "Conversion Cost": 30.0,
  "Stat Cost": 1500.0,
  "Show Count": 10000,
  "Click Count": 800,
  "Convert Count": 50,
  "Account Name": "WH-我的广告公司",
  "Account ID": "****************",
  "Comparison Data": {
    // 环比数据...
  }
}
```

## 🎯 显示效果

### 🔹 **用户会话API效果**
访问巨量引擎后台时，用户会话信息将显示：
```json
{
  "data": {
    "user_name": "WH-我的广告公司",
    "user_id": "****************", 
    "advertiser_name": "WH-我的广告公司",
    "advertiser_id": "****************",
    "account_name": "WH-我的广告公司",
    "account_id": "****************"
  }
}
```

### 🔹 **账户余额API效果**
仪表板账户信息将显示：
```json
{
  "data": {
    "advertiser_name": "WH-我的广告公司",
    "advertiser_id": "****************",
    "account_name": "WH-我的广告公司", 
    "account_id": "****************",
    "user_name": "WH-我的广告公司",
    "user_id": "****************",
    "valid_balance": 25000.00,
    "today_cost": 1500.0
  }
}
```

## 🛠️ 技术实现

### 📋 **修改的核心文件**

1. **`mitmPro_AD.py`** - 主程序入口
   - 新增 `get_account_data()` 函数
   - 增强 `start_smart_engine_with_input()` 函数

2. **`smart_engine/engine/smart_api_engine_minimal.py`** - 智能引擎
   - 新增 `_modify_unknown()` 处理器
   - 增强 `_modify_balance()` 处理器

3. **配置文件支持**
   - `smart_engine/config/api_rules_config.json` - API规则配置
   - `metrics.json` - 用户数据配置

### 🔧 **关键处理逻辑**

```python
def _modify_unknown(self, flow, api_info):
    """处理用户会话API"""
    if '/session/human/latest' in flow.request.url:
        # 获取用户自定义账户信息
        account_name = self.metrics.get('Account Name', '默认账户')
        account_id = self.metrics.get('Account ID', '默认ID')
        
        # 注入账户信息到响应
        response_data['data']['user_name'] = account_name
        response_data['data']['advertiser_id'] = account_id
```

## 🔍 功能验证

### 🔹 **测试步骤**

1. **启动多账户模式**
   ```bash
   python launcher.py
   # 选择 2. 👥 多账户模式
   ```

2. **配置浏览器代理**
   - 代理地址：`127.0.0.1:8080`
   - 安装证书：访问 [mitm.it](http://mitm.it)

3. **配置浏览器代理**
   - 代理地址: `127.0.0.1:8080`

4. **访问巨量引擎后台**
   - URL: `https://ad.oceanengine.com/`

5. **验证显示效果**
   - 检查页面顶部账户信息
   - 查看仪表板账户名称
   - 验证账户ID显示

### 🔹 **预期结果**

- ✅ 页面显示自定义的账户昵称
- ✅ 账户ID更改为用户输入的值
- ✅ 用户会话信息正确更新
- ✅ 所有相关API都使用新的账户信息

## 📈 覆盖的API类型

| API类型 | 数量 | 覆盖率 | 处理器 |
|---------|------|--------|--------|
| 用户会话 | 3个 | 100% | `modify_unknown` |
| 账户信息 | 22个 | 100% | `modify_balance` |
| 账户余额 | 1个 | 100% | `modify_balance` |
| 总计 | **26个** | **100%** | - |

## 💡 使用技巧

### 🔹 **账户昵称建议**
- 使用有意义的名称，便于识别
- 建议格式：`WH-{公司/部门名称}`
- 避免使用特殊字符

### 🔹 **账户ID建议** 
- 使用纯数字ID
- 长度建议13-16位
- 避免与真实账户ID冲突

### 🔹 **配置管理**
- 使用演示脚本快速切换账户
- 定期备份 `metrics.json` 配置
- 可创建多个配置文件用于不同场景

## 🚀 高级用法

### 🔹 **批量账户配置**
创建多个配置文件：
```bash
cp metrics.json metrics_account1.json
cp metrics.json metrics_account2.json
```

切换账户配置：
```bash
cp metrics_account1.json metrics.json
python mitmPro_AD.py
```

### 🔹 **自动化脚本**
创建批处理脚本实现一键切换：
```python
# 账户切换脚本示例
import json, shutil

accounts = {
    'account1': {'name': 'WH-公司A', 'id': '*********'},
    'account2': {'name': 'WH-公司B', 'id': '*********'}
}

def switch_account(account_key):
    # 实现账户切换逻辑
    pass
```

## 🎯 总结

通过新增的账户信息配置功能，用户现在可以：

- 🎨 **个性化账户显示** - 自定义账户昵称和ID
- 🔄 **实时生效** - 无需重启，即时修改API响应
- 🛡️ **安全可靠** - 本地代理处理，不影响真实账户
- 📊 **完整覆盖** - 支持所有相关的账户API

这个功能让巨量引擎智能API管理系统更加灵活和实用！🚀 