#!/usr/bin/env python3
"""
🔍 实时API监控工具
专门用于监控多账户页面的API调用，找出真正的多账户列表API
"""

import json
import time
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def monitor_api_logs():
    """实时监控API日志"""
    print("🔍 开始实时监控API调用...")
    print("=" * 60)
    print("请在浏览器中访问多账户页面，观察API调用")
    print("=" * 60)
    
    # 获取最新的日志文件
    log_dir = "data/api_logs"
    if not os.path.exists(log_dir):
        print(f"❌ 日志目录不存在: {log_dir}")
        return
    
    # 找到最新的日志文件
    log_files = [f for f in os.listdir(log_dir) if f.endswith('.jsonl')]
    if not log_files:
        print(f"❌ 没有找到日志文件")
        return
    
    latest_log = max(log_files, key=lambda f: os.path.getctime(os.path.join(log_dir, f)))
    log_path = os.path.join(log_dir, latest_log)
    
    print(f"📋 监控日志文件: {latest_log}")
    print("🎯 重点关注以下API模式:")
    print("   - /nbs/api/bm/promotion/ad/get_account_list")
    print("   - /bp/api/*/account*")
    print("   - 包含 'data_list' 或 'total_metrics' 的响应")
    print()
    
    # 记录已处理的行数
    processed_lines = 0
    if os.path.exists(log_path):
        with open(log_path, 'r', encoding='utf-8') as f:
            processed_lines = sum(1 for _ in f)
    
    print(f"📊 当前日志行数: {processed_lines}")
    print("⏰ 开始实时监控...")
    print()
    
    try:
        while True:
            if os.path.exists(log_path):
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    
                    # 处理新增的行
                    new_lines = lines[processed_lines:]
                    for line in new_lines:
                        try:
                            api_data = json.loads(line.strip())
                            analyze_api_call(api_data)
                        except json.JSONDecodeError:
                            continue
                    
                    processed_lines = len(lines)
            
            time.sleep(1)  # 每秒检查一次
            
    except KeyboardInterrupt:
        print("\n🛑 监控已停止")

def analyze_api_call(api_data):
    """分析API调用"""
    url = api_data.get('url', '')
    api_path = api_data.get('api_path', '')
    timestamp = api_data.get('timestamp', '')
    
    # 🎯 重点关注的API模式
    important_patterns = [
        'get_account_list',
        'account_manage',
        'multi_account',
        'org_with_account',
        'promotion/ad',
        'bm/promotion',
        'bp/api'
    ]
    
    # 检查是否是重要的API
    is_important = any(pattern in url.lower() for pattern in important_patterns)
    
    if is_important:
        print(f"🎯 [{timestamp}] 发现重要API:")
        print(f"   URL: {url}")
        print(f"   路径: {api_path}")
        
        # 分析响应数据
        response = api_data.get('response', {})
        if response and 'body' in response:
            try:
                response_body = json.loads(response['body'])
                analyze_response_structure(response_body, url)
            except:
                print(f"   响应: 无法解析JSON")
        
        print()

def analyze_response_structure(response_data, url):
    """分析响应数据结构"""
    if not isinstance(response_data, dict):
        return
    
    data = response_data.get('data', {})
    if not isinstance(data, dict):
        return
    
    # 🔥 关键：检查是否包含多账户列表结构
    if 'data_list' in data:
        print(f"   ✅ 发现 data_list!")
        data_list = data['data_list']
        if isinstance(data_list, list) and data_list:
            print(f"   📊 账户数量: {len(data_list)}")
            first_account = data_list[0]
            if isinstance(first_account, dict):
                print(f"   🔑 账户字段: {list(first_account.keys())}")
                if 'advertiser_name' in first_account:
                    print(f"   👤 第一个账户: {first_account.get('advertiser_name', 'N/A')}")
    
    if 'total_metrics' in data:
        print(f"   ✅ 发现 total_metrics!")
        total_metrics = data['total_metrics']
        if isinstance(total_metrics, dict):
            print(f"   📈 概览字段: {list(total_metrics.keys())}")
            if 'stat_cost' in total_metrics:
                print(f"   💰 概览消耗: {total_metrics.get('stat_cost', 'N/A')}")
    
    # 检查其他可能的多账户结构
    if 'org_list' in data:
        print(f"   ✅ 发现 org_list!")
        
    # 检查是否是数字键的结构（另一种多账户格式）
    if isinstance(data, dict) and all(key.isdigit() for key in data.keys() if key):
        print(f"   ✅ 发现数字键结构（可能是多账户数据）!")
        print(f"   🔑 账户ID: {list(data.keys())}")

def main():
    """主函数"""
    print("🔍 实时API监控工具")
    print("专门用于发现多账户页面的真实API调用")
    print()
    
    try:
        monitor_api_logs()
    except Exception as e:
        print(f"❌ 监控失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
