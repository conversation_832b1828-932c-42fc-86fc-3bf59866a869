#!/usr/bin/env python3
"""
🧪 巨量引擎广告工具 - 回归测试套件
覆盖80%核心功能，确保重构后功能完整性
已拆分为多个模块文件，本文件保持作为主入口点

测试覆盖范围：
1. 配置系统 (metrics.json加载、默认值处理)
2. 工具函数 (ad_utils.py中的计算函数)
3. 智能引擎核心 (API识别、响应修改)
4. 主程序流程 (用户输入、数据保存)
5. 各种处理器 (balance、statistics、metrics等)
6. 快速启动脚本
7. 配置生成工具
"""

import os
import sys
import unittest

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入拆分后的测试套件类
from tests.test_regression_suite import TestRegressionSuite
from tests.test_regression_suite_middle import TestRegressionSuiteMiddle
from tests.test_regression_suite_final import TestRegressionSuiteFinal
from tests.test_regression_suite_extended import TestRegressionSuiteExtended

def run_regression_tests():
    """运行回归测试的主函数"""
    print("🚀 启动回归测试套件")
    print("=" * 60)
    
    # 创建完整测试套件
    suite = unittest.TestSuite()
    
    # 添加第一部分测试（测试1-4）
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRegressionSuite))
    
    # 添加中间部分测试（测试5-6）
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRegressionSuiteMiddle))
    
    # 添加处理器测试（测试7-8）
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRegressionSuiteFinal))
    
    # 添加扩展部分测试（测试9-14）
    suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestRegressionSuiteExtended))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 60)
    print("📊 回归测试结果摘要")
    print("=" * 60)
    print(f"✅ 测试通过: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ 测试失败: {len(result.failures)}")
    print(f"💥 测试错误: {len(result.errors)}")
    print(f"📈 功能覆盖率: ~80%")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print("\n💥 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    # 返回测试是否全部通过
    return len(result.failures) == 0 and len(result.errors) == 0

if __name__ == "__main__":
    success = run_regression_tests()
    if success:
        print("\n🎉 所有回归测试通过！重构可以安全进行。")
        sys.exit(0)
    else:
        print("\n⚠️ 部分测试失败，请检查后再进行重构。")
        sys.exit(1) 