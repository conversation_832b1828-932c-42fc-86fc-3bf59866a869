#!/usr/bin/env python3
"""
⚙️ 简化的多账户配置处理器
直接使用文件操作，避免复杂的缓存机制
"""

import json
import os
from typing import Dict, Any, Optional
from smart_engine.utils.debug_logger import info_log, debug_log, error_log, warning_log, success_log
from smart_engine.utils.ad_utils import save_metrics_to_file, load_metrics_from_file, calculate_ad_metrics
from smart_engine.utils.config_paths import ConfigPaths, get_account_metrics_path


class MultiAccountConfigHandler:
    """简化的多账户配置处理器"""

    def __init__(self, mappings: Dict[str, Dict[str, Any]]):
        """
        初始化配置处理器

        Args:
            mappings: 账户映射配置
        """
        self.mappings = mappings
        self.accounts_dir = ConfigPaths.ACCOUNTS_CONFIG

        # 确保目录存在
        ConfigPaths.ensure_directories()

    def configure_account(self, account_id: str) -> bool:
        """配置指定账户 - 简化版本"""
        try:
            if account_id not in self.mappings:
                warning_log(f"⚠️ [配置处理器] 账户 {account_id} 未找到映射配置")
                return False

            mapping_info = self.mappings[account_id]
            real_name = mapping_info['real_name']
            virtual_name = mapping_info['virtual_name']

            debug_log(f"🔧 [配置处理器] 为账户 {virtual_name} 配置...")

            # 生成配置数据
            current_metrics = self._generate_account_config(account_id, real_name, virtual_name)

            # 保存到专用文件
            config_file = get_account_metrics_path(account_id)
            if save_metrics_to_file(current_metrics, config_file):
                success_log(f"✅ [配置处理器] 账户配置已完成: {virtual_name} → {real_name}")
                debug_log(f"   📁 配置文件: {config_file}")
                debug_log(f"   🔑 真实账户: {real_name} ({account_id})")
                debug_log(f"   🎭 虚拟账户: {virtual_name}")
                debug_log(f"   🚫 不覆盖全局配置，避免账户混乱")
                return True
            else:
                error_log(f"❌ [配置处理器] 配置保存失败: {account_id}")
                return False

        except Exception as e:
            error_log(f"❌ [配置处理器] 账户配置失败: {e}")
            return False

    def _generate_account_config(self, account_id: str, real_name: str, virtual_name: str) -> Dict[str, Any]:
        """生成账户配置数据 - 统一使用配置文件中的标准虚拟ID"""
        from smart_engine.utils.debug_logger import debug_log, warning_log, error_log

        # 🔧 修复：使用统一的虚拟ID管理器获取标准虚拟ID
        from smart_engine.utils.virtual_id_manager import VirtualIdManager
        standard_virtual_id = VirtualIdManager.get_standard_virtual_id(account_id, virtual_name, self.mappings)

        # 🔥 修复：优先从现有专用配置文件读取数据
        config_file = get_account_metrics_path(account_id)
        if os.path.exists(config_file):
            try:
                existing_config = load_metrics_from_file(config_file)
                if existing_config:
                    # 更新账户映射信息，保留现有的指标数据，使用标准虚拟ID
                    existing_config.update({
                        'Account Name': virtual_name,
                        'Account ID': standard_virtual_id,  # 使用标准虚拟ID
                        'account_mapping': {
                            'real_account': {
                                'name': real_name,
                                'id': account_id
                            },
                            'virtual_account': {
                                'name': virtual_name,
                                'id': standard_virtual_id  # 使用标准虚拟ID
                            }
                        }
                    })
                    debug_log(f"📊 [配置处理器] 使用现有配置数据: 展示{existing_config.get('Show Count', 0):,} 消耗¥{existing_config.get('Stat Cost', 0)}")
                    debug_log(f"🔧 [配置处理器] 使用标准虚拟ID: {standard_virtual_id}")
                    return existing_config
            except Exception as e:
                error_log(f"❌ [配置处理器] 读取现有配置失败: {e}")

        # 🔥 修复：如果没有现有配置，返回基础配置，使用标准虚拟ID
        warning_log(f"⚠️ [配置处理器] 账户 {account_id} 没有现有配置文件，返回基础配置")
        debug_log(f"🔧 [配置处理器] 使用标准虚拟ID: {standard_virtual_id}")
        return {
            'Account Name': virtual_name,
            'Account ID': standard_virtual_id,  # 使用标准虚拟ID
            'account_mapping': {
                'real_account': {
                    'name': real_name,
                    'id': account_id
                },
                'virtual_account': {
                    'name': virtual_name,
                    'id': standard_virtual_id  # 使用标准虚拟ID
                }
            }
        }

    def get_account_config(self, account_id: str) -> Optional[Dict[str, Any]]:
        """获取账户配置"""
        config_file = get_account_metrics_path(account_id)
        if os.path.exists(config_file):
            return load_metrics_from_file(config_file)
        return None

