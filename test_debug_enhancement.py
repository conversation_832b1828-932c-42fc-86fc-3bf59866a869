#!/usr/bin/env python3
"""
测试增强调试功能
验证新增的调试日志是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_debug_logs():
    """测试调试日志功能"""
    print("🔍 [测试] 开始测试增强调试功能...")
    
    try:
        # 测试调试日志系统
        from smart_engine.utils.debug_logger import debug_log, info_log, success_log, warning_log, error_log
        
        print("✅ [测试] 调试日志系统导入成功")
        
        # 测试各种日志级别
        debug_log("🔍 [测试] 这是一条调试日志")
        info_log("📋 [测试] 这是一条信息日志")
        success_log("✅ [测试] 这是一条成功日志")
        warning_log("⚠️ [测试] 这是一条警告日志")
        error_log("❌ [测试] 这是一条错误日志")
        
        print("✅ [测试] 所有日志级别测试完成")
        
    except Exception as e:
        print(f"❌ [测试] 调试日志系统测试失败: {e}")
        return False
    
    try:
        # 测试入口代理
        from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
        
        print("✅ [测试] 入口代理导入成功")
        
        # 创建入口代理实例
        entry_proxy = MultiAccountEntryProxy()
        print("✅ [测试] 入口代理实例创建成功")
        
    except Exception as e:
        print(f"❌ [测试] 入口代理测试失败: {e}")
        return False
    
    try:
        # 测试API监控器
        from smart_engine.utils.api_monitor import APIMonitor
        
        print("✅ [测试] API监控器导入成功")
        
        # 创建API监控器实例
        api_monitor = APIMonitor()
        print("✅ [测试] API监控器实例创建成功")
        
    except Exception as e:
        print(f"❌ [测试] API监控器测试失败: {e}")
        return False
    
    print("🎉 [测试] 所有增强调试功能测试通过！")
    return True

def check_debug_log_file():
    """检查调试日志文件"""
    log_file = "data/logs/account_mapping_debug.log"
    
    if os.path.exists(log_file):
        print(f"✅ [测试] 调试日志文件存在: {log_file}")
        
        # 检查文件大小
        file_size = os.path.getsize(log_file)
        print(f"📏 [测试] 日志文件大小: {file_size} 字节")
        
        # 读取最后几行
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"📋 [测试] 日志文件总行数: {len(lines)}")
                    print(f"📋 [测试] 最后一行: {lines[-1].strip()}")
                else:
                    print("⚠️ [测试] 日志文件为空")
        except Exception as e:
            print(f"❌ [测试] 读取日志文件失败: {e}")
    else:
        print(f"⚠️ [测试] 调试日志文件不存在: {log_file}")

def main():
    """主函数"""
    print("🚀 [测试] 开始测试增强调试功能...")
    print("=" * 50)
    
    # 测试调试功能
    if test_debug_logs():
        print("=" * 50)
        print("✅ [测试] 增强调试功能测试成功！")
        
        # 检查日志文件
        print("=" * 50)
        check_debug_log_file()
        
        print("=" * 50)
        print("📋 [测试] 测试完成，现在可以重新启动代理进行实际测试")
        print("📋 [测试] 新增的调试信息将在以下关键点记录：")
        print("   🔍 API匹配过程详情")
        print("   📏 响应修改前后的长度对比")
        print("   📋 账户名称变化详情")
        print("   ✅ 响应内容修改验证")
        print("   🎯 最终响应状态检查")
        
    else:
        print("=" * 50)
        print("❌ [测试] 增强调试功能测试失败！")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
