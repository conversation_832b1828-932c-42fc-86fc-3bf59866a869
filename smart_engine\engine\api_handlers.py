from mitmproxy import http


class APIHandlers:
    """API处理器集合 - 统一管理所有_modify_*方法"""
    
    def __init__(self, statistics_handler, balance_handler, list_handler, 
                 detail_handler, common_handler):
        """初始化API处理器集合"""
        self.statistics_handler = statistics_handler
        self.balance_handler = balance_handler
        self.list_handler = list_handler
        self.detail_handler = detail_handler
        self.common_handler = common_handler
    
    def modify_balance(self, flow: http.HTTPFlow, api_info: dict):
        """修改余额API - 委托给余额处理器"""
        try:
            self.balance_handler.modify_balance(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改余额API出错: {e}")
            self._log_error(flow, e)
    
    def modify_project_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目列表API - 委托给列表处理器"""
        try:
            self.list_handler.modify_project_list(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改项目列表API出错: {e}")
            self._log_error(flow, e)
    
    def modify_promotion_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告列表API - 委托给列表处理器"""
        try:
            self.list_handler.modify_promotion_list(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改广告列表API出错: {e}")
            self._log_error(flow, e)
    
    def modify_material_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改素材列表API - 委托给列表处理器"""
        try:
            self.list_handler.modify_material_list(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改素材列表API出错: {e}")
            self._log_error(flow, e)
    
    def modify_statistics(self, flow: http.HTTPFlow, api_info: dict):
        """修改统计数据API - 委托给统计处理器"""
        try:
            self.statistics_handler.modify_statistics(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改统计数据API出错: {e}")
            self._log_error(flow, e)
    
    def generic_handler(self, flow: http.HTTPFlow, api_info: dict):
        """通用处理器 - 委托给通用处理器"""
        try:
            self.common_handler.generic_handler(flow, api_info)
        except Exception as e:
            self._log_error(flow, e)
    
    def modify_project_detail(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目详情API - 委托给详情处理器"""
        try:
            self.detail_handler.modify_project_detail(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改项目详情API出错: {e}")
            self._log_error(flow, e)
    
    def modify_project_home(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目首页API - 委托给详情处理器"""
        try:
            self.detail_handler.modify_project_home(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改项目首页API出错: {e}")
            self._log_error(flow, e)
    
    def modify_promotion_detail(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告详情API - 委托给详情处理器"""
        try:
            self.detail_handler.modify_promotion_detail(flow, api_info)
        except Exception as e:
            print(f"❌ [新引擎] 修改广告详情API出错: {e}")
            self._log_error(flow, e)
    
    def modify_ad_promotion_list(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告计划列表API - 委托给通用处理器"""
        self.common_handler.handle_ad_promotion_list(flow, api_info, self.modify_promotion_list)
    
    def modify_ad_promotion_attribute(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告计划属性API - 委托给通用处理器"""
        self.common_handler.handle_ad_promotion_attribute(flow, api_info, self.modify_promotion_list)
    
    def modify_ad_materials(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告素材API - 委托给通用处理器"""
        self.common_handler.handle_ad_materials(flow, api_info, self.modify_material_list)
    
    def modify_unknown(self, flow: http.HTTPFlow, api_info: dict, metrics=None):
        """修改未知类型API - 委托给详情处理器"""
        try:
            # 如果没有传入metrics，尝试从detail_handler获取
            if metrics is None and hasattr(self.detail_handler, 'metrics_handler') and hasattr(self.detail_handler.metrics_handler, 'metrics'):
                metrics = self.detail_handler.metrics_handler.metrics
            self.detail_handler.modify_unknown(flow, api_info, metrics)
        except Exception as e:
            print(f"❌ [新引擎] 修改未知类型API出错: {e}")
            self._log_error(flow, e)
    
    def _log_error(self, flow: http.HTTPFlow, error: Exception):
        """记录错误日志 - 委托给通用处理器"""
        self.common_handler.log_error(flow, error) 