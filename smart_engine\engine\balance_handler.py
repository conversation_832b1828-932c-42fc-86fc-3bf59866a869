import json
import random
from mitmproxy import http

# 使用绝对导入
from smart_engine.utils.ad_utils import check_nested_dict_for_key

class BalanceHandler:
    """余额数据处理器"""
    
    def __init__(self, metrics_data):
        """初始化余额处理器
        
        Args:
            metrics_data: 指标数据字典
        """
        self.metrics = metrics_data
    
    def modify_balance(self, flow: http.HTTPFlow, api_info: dict):
        """修改余额API - 增强版：支持自定义账户信息和账户详情API"""
        try:
            response_data = json.loads(flow.response.text)
            if not check_nested_dict_for_key(response_data, "data"):
                return
                
            # 获取用户自定义的账户信息
            account_name = self.metrics.get('Account Name', 'WH-我的广告公司')
            account_id = self.metrics.get('Account ID', '****************')
            stat_cost = self.metrics['Stat Cost']
            
            # 🔧 智能识别API类型并相应处理
            url = flow.request.pretty_url
            
            if '/ad/api/account/info' in url:
                # 🎯 专门处理账户详情API
                response_data["data"] = {
                    "core_user": {
                        "user_id": account_id,
                        "name": account_name,
                        "screen_name": account_name  # 关键：页面显示的昵称
                    },
                    "advertiser": {
                        "id": account_id,
                        "name": account_name  # 关键：公司名称显示
                    },
                    # 添加其他可能的账户信息字段
                    "account_info": {
                        "account_id": account_id,
                        "account_name": account_name,
                        "company_name": account_name,
                        "display_name": account_name
                    }
                }
                flow.response.set_text(json.dumps(response_data))
                print(f"🎯 [新引擎] 账户详情API修改完成 - 昵称: {account_name} (ID: {account_id})")
                
            else:
                # 🏦 处理其他余额相关API - 保留原有账户信息，只更新余额相关数据
                yue = round(random.uniform(1000, 50000), 2)
                
                # 保留原有数据结构，只更新余额相关字段
                if "data" not in response_data:
                    response_data["data"] = {}
                
                # 🔥 修复：使用配置中的预算值，不再硬编码
                budget_value = self.metrics.get('Budget', 10000.0)  # 从配置读取预算，默认10000
                budget_str = f"{budget_value:.2f}"

                # 更新余额相关字段，保留原有的账户信息
                response_data["data"].update({
                    "budget_mode": 0,
                    "bid_cost": stat_cost,
                    "grant": 0.0,
                    "valid_balance": yue,
                    "cash": yue,
                    "budget_task_info": None,
                    "budget": budget_str,  # 🔥 修复：使用配置中的预算值
                    "today_cost_total_v2": stat_cost,
                    "today_cost_total_v1": "0.0",
                    "valid_grant": 0.0,
                    "today_cost": stat_cost,
                    "is_branding_advertiser": False,
                    "brand_cost_v2": "0.0",
                    "brand_cost_v1": "0.0",
                    "brand_cost": "0.0",
                    "balance": yue,
                    "is_share_wallet": False,
                    "bid_cost_v2": stat_cost,
                    "bid_cost_v1": "0.0"
                })
                
                # 🔍 保留已转换的账户信息，不强制覆盖
                # 如果原响应中没有账户信息，才使用配置中的信息
                if "advertiser_name" not in response_data["data"]:
                    response_data["data"]["advertiser_name"] = account_name
                if "advertiser_id" not in response_data["data"]:
                    response_data["data"]["advertiser_id"] = account_id
                if "account_name" not in response_data["data"]:
                    response_data["data"]["account_name"] = account_name
                if "account_id" not in response_data["data"]:
                    response_data["data"]["account_id"] = account_id
                if "user_name" not in response_data["data"]:
                    response_data["data"]["user_name"] = account_name
                if "user_id" not in response_data["data"]:
                    response_data["data"]["user_id"] = account_id
                
                flow.response.set_text(json.dumps(response_data))
                print(f"✅ [新引擎] 余额API修改完成 - 账户: {account_name} ({account_id})")
                
        except Exception as e:
            print(f"❌ [新引擎] 修改余额API出错: {e}")
            raise e 