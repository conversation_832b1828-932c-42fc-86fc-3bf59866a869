# ⚡ 开发计划

## 🎯 项目开发概览

### 📊 项目特点
- **架构类型**：轻量级一体化架构
- **开发时间**：3天完成核心功能
- **技术栈**：Flask + Jinja2 + Vue.js CDN + SQLite
- **部署方式**：双进程 + 单数据库通信

### 🏗️ 架构实施方案

#### **双进程架构**
```
进程1: mitmproxy代理服务 (proxy_server.py)
├── 端口: 8080
├── 功能: HTTP请求拦截和响应修改
└── 通信: SQLite数据库读写

进程2: Flask Web服务 (app.py)  
├── 端口: 5000
├── 功能: Web界面 + API接口 + 数据管理
└── 通信: SQLite数据库读写
```

#### **一体化Web服务结构**
```
Flask应用 (app.py)
├── 路由处理
│   ├── @app.route('/') -> 首页模板
│   ├── @app.route('/config') -> 配置页面模板
│   └── @app.route('/monitor') -> 监控页面模板
├── API接口
│   ├── @app.route('/api/config', methods=['POST'])
│   ├── @app.route('/api/rules', methods=['GET', 'POST'])
│   └── @app.route('/api/logs', methods=['GET'])
├── 模板渲染
│   ├── render_template() + 数据注入
│   └── Jinja2 + Vue.js CDN集成
└── 静态文件服务
    ├── /static/css/ -> 样式文件
    ├── /static/js/ -> JavaScript文件
    └── /static/images/ -> 图片资源
```

## 📅 详细开发计划

### 🚀 第一天：后端核心功能 (6-8小时)

#### **上午 (4小时)：基础架构搭建**

##### ⏰ 09:00-10:30 | 项目结构初始化
```bash
# 创建项目结构
mkdir templates static/css static/js static/images api data config
touch app.py proxy_server.py requirements.txt
touch templates/base.html templates/index.html
touch static/css/main.css static/js/app.js
```

**输出物**：
- ✅ 完整的项目目录结构
- ✅ 基础文件框架
- ✅ requirements.txt依赖清单

##### ⏰ 10:30-12:00 | 数据库设计和初始化
```python
# 创建 database.py
import sqlite3
import json
from datetime import datetime

def init_database():
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    
    # 创建核心数据表
    cursor.execute('''CREATE TABLE metrics_config...''')
    cursor.execute('''CREATE TABLE api_rules...''')
    cursor.execute('''CREATE TABLE processing_logs...''')
    cursor.execute('''CREATE TABLE system_config...''')
    
    # 插入默认数据
    cursor.execute('''INSERT INTO metrics_config...''')
    
    conn.commit()
    conn.close()
```

**输出物**：
- ✅ SQLite数据库文件
- ✅ 所有数据表结构
- ✅ 初始化数据和配置

#### **下午 (4小时)：Flask应用开发**

##### ⏰ 13:00-15:00 | Flask核心应用
```python
# app.py 主要结构
from flask import Flask, render_template, jsonify, request
import sqlite3
import json

app = Flask(__name__)

# 数据库操作类
class DatabaseManager:
    def get_metrics_config(self):
        # 获取指标配置
        pass
    
    def update_metrics_config(self, config):
        # 更新指标配置
        pass

# 页面路由
@app.route('/')
def index():
    db = DatabaseManager()
    metrics = db.get_metrics_config()
    return render_template('index.html', 
                         title='数据管理平台',
                         metrics=metrics)

@app.route('/config')
def config_page():
    # 配置页面
    pass

# API路由
@app.route('/api/config', methods=['POST'])
def update_config():
    # 配置更新API
    pass

if __name__ == '__main__':
    app.run(debug=True, port=5000)
```

**输出物**：
- ✅ Flask应用主程序
- ✅ 数据库操作类
- ✅ 基础路由和API接口

##### ⏰ 15:00-17:00 | 代理服务集成
```python
# proxy_server.py
from mitmproxy import http, ctx
import sqlite3
import json
import time

class APIModifier:
    def __init__(self):
        self.db_path = 'data/app.db'
        
    def load_metrics_config(self):
        # 从数据库加载配置
        conn = sqlite3.connect(self.db_path)
        # ...查询逻辑
        conn.close()
        
    def detect_api_type(self, url):
        # API类型识别
        if '/project/' in url:
            return 'project'
        elif '/promotion/' in url:
            return 'promotion'
        # ...
        
    def modify_response(self, flow: http.HTTPFlow):
        # 响应修改逻辑
        api_type = self.detect_api_type(flow.request.pretty_url)
        if api_type:
            # 应用修改规则
            pass

def response(flow: http.HTTPFlow):
    modifier = APIModifier()
    modifier.modify_response(flow)

# 启动命令：mitmdump -s proxy_server.py -p 8080
```

**输出物**：
- ✅ mitmproxy代理服务
- ✅ API识别和修改逻辑
- ✅ 数据库通信机制

### 🎨 第二天：前端界面开发 (6-8小时)

#### **上午 (4小时)：模板系统开发**

##### ⏰ 09:00-10:30 | 基础模板架构
```html
<!-- templates/base.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    
    <!-- Element Plus CDN -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
</head>
<body>
    <div id="app">
        <!-- 导航栏 -->
        <el-container>
            <el-header>
                <el-menu mode="horizontal" default-active="{{active_menu}}">
                    <el-menu-item index="/">首页</el-menu-item>
                    <el-menu-item index="/config">数据配置</el-menu-item>
                    <el-menu-item index="/monitor">监控日志</el-menu-item>
                </el-menu>
            </el-header>
            
            <el-main>
                {% block content %}{% endblock %}
            </el-main>
        </el-container>
    </div>
    
    <script>
        const { createApp } = Vue
        const app = createApp({
            data() {
                return {
                    {% block app_data %}{% endblock %}
                }
            },
            methods: {
                // 通用API调用方法
                async callApi(url, data, method = 'POST') {
                    try {
                        const response = await fetch(url, {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: method !== 'GET' ? JSON.stringify(data) : undefined
                        })
                        return await response.json()
                    } catch (error) {
                        this.$message.error('请求失败: ' + error.message)
                        return null
                    }
                },
                
                // 通用成功提示
                showSuccess(message) {
                    this.$message.success(message)
                },
                
                // 通用错误提示
                showError(message) {
                    this.$message.error(message)
                }
            }
        })
        
        app.use(ElementPlus)
        app.mount('#app')
    </script>
    
    {% block custom_scripts %}{% endblock %}
</body>
</html>
```

**输出物**：
- ✅ 基础模板架构
- ✅ Vue.js + Element Plus集成
- ✅ 通用方法和样式

##### ⏰ 10:30-12:00 | 首页仪表板开发
```html
<!-- templates/index.html -->
{% extends "base.html" %}

{% block content %}
<div>
    <el-row :gutter="20">
        <!-- 系统状态卡片 -->
        <el-col :span="6">
            <el-card>
                <div slot="header">
                    <span>系统状态</span>
                </div>
                <div>
                    <p>代理服务: <el-tag :type="proxyStatus ? 'success' : 'danger'">
                        {{ proxyStatus ? '运行中' : '已停止' }}
                    </el-tag></p>
                    <p>处理请求: {{totalRequests}}</p>
                    <p>成功率: {{successRate}}%</p>
                </div>
            </el-card>
        </el-col>
        
        <!-- 数据概览卡片 -->
        <el-col :span="18">
            <el-card>
                <div slot="header">
                    <span>当前数据配置</span>
                    <el-button style="float: right;" @click="openConfigDialog">编辑</el-button>
                </div>
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="展示量">{{metrics.show_count}}</el-descriptions-item>
                    <el-descriptions-item label="点击量">{{metrics.click_count}}</el-descriptions-item>
                    <el-descriptions-item label="转化量">{{metrics.convert_count}}</el-descriptions-item>
                    <el-descriptions-item label="消耗金额">¥{{metrics.stat_cost}}</el-descriptions-item>
                    <el-descriptions-item label="点击率">{{ctr}}%</el-descriptions-item>
                    <el-descriptions-item label="转化率">{{conversionRate}}%</el-descriptions-item>
                </el-descriptions>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 快速配置对话框 -->
    <el-dialog title="快速配置数据" v-model="configDialogVisible" width="600px">
        <el-form :model="editMetrics" label-width="100px">
            <el-form-item label="展示量">
                <el-input-number v-model="editMetrics.show_count" :min="0"></el-input-number>
            </el-form-item>
            <el-form-item label="点击量">
                <el-input-number v-model="editMetrics.click_count" :min="0"></el-input-number>
            </el-form-item>
            <el-form-item label="转化量">
                <el-input-number v-model="editMetrics.convert_count" :min="0"></el-input-number>
            </el-form-item>
            <el-form-item label="消耗金额">
                <el-input-number v-model="editMetrics.stat_cost" :min="0" :precision="2"></el-input-number>
            </el-form-item>
        </el-form>
        
        <template #footer>
            <el-button @click="configDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="saveQuickConfig">保存</el-button>
        </template>
    </el-dialog>
</div>
{% endblock %}

{% block app_data %}
// 服务器数据注入
metrics: {{metrics|tojson}},
proxyStatus: true,
totalRequests: {{stats.total_requests}},
successRate: {{stats.success_rate}},

// 本地状态
configDialogVisible: false,
editMetrics: {}
{% endblock %}

{% block custom_scripts %}
<script>
// 页面特定方法
app.computed = {
    ctr() {
        return this.metrics.click_count > 0 ? 
            (this.metrics.click_count / this.metrics.show_count * 100).toFixed(2) : 0
    },
    conversionRate() {
        return this.metrics.click_count > 0 ? 
            (this.metrics.convert_count / this.metrics.click_count * 100).toFixed(2) : 0
    }
}

app.methods.openConfigDialog = function() {
    this.editMetrics = { ...this.metrics }
    this.configDialogVisible = true
}

app.methods.saveQuickConfig = async function() {
    const result = await this.callApi('/api/config', this.editMetrics)
    if (result && result.status === 'success') {
        this.metrics = { ...this.editMetrics }
        this.configDialogVisible = false
        this.showSuccess('配置保存成功')
    }
}

// 定时刷新状态
setInterval(() => {
    app.callApi('/api/status', {}, 'GET').then(data => {
        if (data) {
            app.totalRequests = data.total_requests
            app.successRate = data.success_rate
        }
    })
}, 5000)
</script>
{% endblock %}
```

**输出物**：
- ✅ 仪表板页面
- ✅ 实时状态显示
- ✅ 快速配置功能

#### **下午 (4小时)：详细配置页面**

##### ⏰ 13:00-15:00 | 数据配置页面
```html
<!-- templates/config.html -->
{% extends "base.html" %}

{% block content %}
<div>
    <el-row :gutter="20">
        <!-- 指标配置 -->
        <el-col :span="12">
            <el-card>
                <div slot="header">
                    <span>📊 广告指标配置</span>
                </div>
                
                <el-form :model="metricsForm" label-width="120px" @submit.prevent="saveMetrics">
                    <el-form-item label="广告展示量">
                        <el-input-number 
                            v-model="metricsForm.show_count" 
                            :min="0" 
                            :max="10000000"
                            placeholder="请输入展示量">
                        </el-input-number>
                        <small>建议范围：10,000 - 1,000,000</small>
                    </el-form-item>
                    
                    <el-form-item label="广告点击量">
                        <el-input-number 
                            v-model="metricsForm.click_count" 
                            :min="0" 
                            :max="metricsForm.show_count"
                            placeholder="请输入点击量">
                        </el-input-number>
                        <small>点击率将自动计算：{{calculatedCtr}}%</small>
                    </el-form-item>
                    
                    <el-form-item label="转化量">
                        <el-input-number 
                            v-model="metricsForm.convert_count" 
                            :min="0" 
                            :max="metricsForm.click_count"
                            placeholder="请输入转化量">
                        </el-input-number>
                        <small>转化率将自动计算：{{calculatedConversionRate}}%</small>
                    </el-form-item>
                    
                    <el-form-item label="广告消耗">
                        <el-input-number 
                            v-model="metricsForm.stat_cost" 
                            :min="0" 
                            :precision="2"
                            placeholder="请输入消耗金额">
                        </el-input-number>
                        <small>单位：人民币元</small>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="saveMetrics">💾 保存指标配置</el-button>
                        <el-button @click="resetMetrics">🔄 重置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </el-col>
        
        <!-- 状态配置 -->
        <el-col :span="12">
            <el-card>
                <div slot="header">
                    <span>🏷️ 状态显示配置</span>
                </div>
                
                <el-form :model="statusForm" label-width="120px">
                    <el-form-item label="项目状态">
                        <el-input v-model="statusForm.project_status" placeholder="如：启用中"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="广告状态">
                        <el-input v-model="statusForm.promotion_status" placeholder="如：投放中"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="素材状态">
                        <el-input v-model="statusForm.material_status" placeholder="如：正常"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="账户余额">
                        <el-input-number 
                            v-model="statusForm.account_balance" 
                            :min="0" 
                            :precision="2"
                            placeholder="账户余额">
                        </el-input-number>
                    </el-form-item>
                    
                    <el-form-item>
                        <el-button type="primary" @click="saveStatus">💾 保存状态配置</el-button>
                    </el-form-item>
                </el-form>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 实时预览 -->
    <el-row style="margin-top: 20px;">
        <el-col :span="24">
            <el-card>
                <div slot="header">
                    <span>👀 效果预览</span>
                </div>
                
                <el-descriptions :column="3" border>
                    <el-descriptions-item label="展示量">{{metricsForm.show_count | numberFormat}}</el-descriptions-item>
                    <el-descriptions-item label="点击量">{{metricsForm.click_count | numberFormat}}</el-descriptions-item>
                    <el-descriptions-item label="转化量">{{metricsForm.convert_count | numberFormat}}</el-descriptions-item>
                    <el-descriptions-item label="消耗金额">¥{{metricsForm.stat_cost}}</el-descriptions-item>
                    <el-descriptions-item label="点击率">{{calculatedCtr}}%</el-descriptions-item>
                    <el-descriptions-item label="转化率">{{calculatedConversionRate}}%</el-descriptions-item>
                </el-descriptions>
                
                <div style="margin-top: 15px;">
                    <el-tag type="success">项目状态：{{statusForm.project_status}}</el-tag>
                    <el-tag type="primary" style="margin-left: 10px;">广告状态：{{statusForm.promotion_status}}</el-tag>
                    <el-tag style="margin-left: 10px;">素材状态：{{statusForm.material_status}}</el-tag>
                </div>
            </el-card>
        </el-col>
    </el-row>
</div>
{% endblock %}

{% block app_data %}
metricsForm: {{metrics|tojson}},
statusForm: {{status_config|tojson}}
{% endblock %}

{% block custom_scripts %}
<script>
// 计算属性
app.computed = {
    calculatedCtr() {
        return this.metricsForm.show_count > 0 ? 
            (this.metricsForm.click_count / this.metricsForm.show_count * 100).toFixed(2) : 0
    },
    calculatedConversionRate() {
        return this.metricsForm.click_count > 0 ? 
            (this.metricsForm.convert_count / this.metricsForm.click_count * 100).toFixed(2) : 0
    }
}

// 过滤器
app.config.globalProperties.$filters = {
    numberFormat(value) {
        return new Intl.NumberFormat('zh-CN').format(value)
    }
}

// 方法
app.methods.saveMetrics = async function() {
    const result = await this.callApi('/api/config', this.metricsForm)
    if (result && result.status === 'success') {
        this.showSuccess('指标配置保存成功')
    }
}

app.methods.saveStatus = async function() {
    const result = await this.callApi('/api/status-config', this.statusForm)
    if (result && result.status === 'success') {
        this.showSuccess('状态配置保存成功')
    }
}

app.methods.resetMetrics = function() {
    this.metricsForm = {
        show_count: 50000,
        click_count: 2500,
        convert_count: 500,
        stat_cost: 5000.0
    }
}
</script>
{% endblock %}
```

**输出物**：
- ✅ 详细配置页面
- ✅ 实时预览功能
- ✅ 表单验证和提示

##### ⏰ 15:00-17:00 | 监控日志页面
```html
<!-- templates/monitor.html -->
{% extends "base.html" %}

{% block content %}
<div>
    <!-- 过滤器 -->
    <el-card style="margin-bottom: 20px;">
        <el-form :inline="true" :model="filters">
            <el-form-item label="时间范围">
                <el-date-picker
                    v-model="filters.dateRange"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="API类型">
                <el-select v-model="filters.apiType" placeholder="请选择">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="项目API" value="project"></el-option>
                    <el-option label="广告API" value="promotion"></el-option>
                    <el-option label="统计API" value="statistics"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="处理结果">
                <el-select v-model="filters.result" placeholder="请选择">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="成功" value="success"></el-option>
                    <el-option label="失败" value="failed"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="loadLogs">🔍 查询</el-button>
                <el-button @click="resetFilters">🔄 重置</el-button>
            </el-form-item>
        </el-form>
    </el-card>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
        <el-col :span="6">
            <el-card>
                <div class="metric-card">
                    <div class="metric-value">{{stats.total}}</div>
                    <div class="metric-label">总请求数</div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card>
                <div class="metric-card">
                    <div class="metric-value success">{{stats.success}}</div>
                    <div class="metric-label">成功处理</div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card>
                <div class="metric-card">
                    <div class="metric-value warning">{{stats.failed}}</div>
                    <div class="metric-label">处理失败</div>
                </div>
            </el-card>
        </el-col>
        <el-col :span="6">
            <el-card>
                <div class="metric-card">
                    <div class="metric-value">{{stats.avgTime}}ms</div>
                    <div class="metric-label">平均耗时</div>
                </div>
            </el-card>
        </el-col>
    </el-row>
    
    <!-- 日志表格 -->
    <el-card>
        <div slot="header">
            <span>📋 处理日志</span>
            <el-button style="float: right;" @click="loadLogs">🔄 刷新</el-button>
        </div>
        
        <el-table :data="logs" style="width: 100%" v-loading="loading">
            <el-table-column prop="timestamp" label="时间" width="160">
                <template #default="scope">
                    {{formatTime(scope.row.timestamp)}}
                </template>
            </el-table-column>
            
            <el-table-column prop="url" label="API地址" width="300" show-overflow-tooltip></el-table-column>
            
            <el-table-column prop="api_type" label="类型" width="100">
                <template #default="scope">
                    <el-tag :type="getApiTypeColor(scope.row.api_type)">
                        {{getApiTypeName(scope.row.api_type)}}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="processing_result" label="结果" width="100">
                <template #default="scope">
                    <el-tag :type="scope.row.processing_result === 'success' ? 'success' : 'danger'">
                        {{scope.row.processing_result === 'success' ? '成功' : '失败'}}
                    </el-tag>
                </template>
            </el-table-column>
            
            <el-table-column prop="processing_time_ms" label="耗时" width="100">
                <template #default="scope">
                    {{scope.row.processing_time_ms}}ms
                </template>
            </el-table-column>
            
            <el-table-column prop="error_message" label="错误信息" show-overflow-tooltip></el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pagination.page"
            :page-sizes="[20, 50, 100]"
            :page-size="pagination.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            style="margin-top: 20px; text-align: right;">
        </el-pagination>
    </el-card>
</div>
{% endblock %}

{% block app_data %}
logs: [],
loading: false,
stats: {{stats|tojson}},
filters: {
    dateRange: [],
    apiType: '',
    result: ''
},
pagination: {
    page: 1,
    size: 20,
    total: 0
}
{% endblock %}

{% block custom_scripts %}
<script>
app.methods.loadLogs = async function() {
    this.loading = true
    try {
        const params = {
            page: this.pagination.page,
            size: this.pagination.size,
            ...this.filters
        }
        const result = await this.callApi('/api/logs', params, 'GET')
        if (result) {
            this.logs = result.logs
            this.pagination.total = result.total
            this.stats = result.stats
        }
    } finally {
        this.loading = false
    }
}

app.methods.formatTime = function(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN')
}

app.methods.getApiTypeColor = function(type) {
    const colors = {
        'project': 'primary',
        'promotion': 'success', 
        'statistics': 'warning'
    }
    return colors[type] || 'info'
}

app.methods.getApiTypeName = function(type) {
    const names = {
        'project': '项目',
        'promotion': '广告',
        'statistics': '统计'
    }
    return names[type] || type
}

app.methods.handleSizeChange = function(size) {
    this.pagination.size = size
    this.loadLogs()
}

app.methods.handleCurrentChange = function(page) {
    this.pagination.page = page
    this.loadLogs()
}

app.methods.resetFilters = function() {
    this.filters = {
        dateRange: [],
        apiType: '',
        result: ''
    }
    this.pagination.page = 1
    this.loadLogs()
}

// 页面加载时获取日志
app.mounted = function() {
    this.loadLogs()
}
</script>

<style>
.metric-card {
    text-align: center;
}
.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
}
.metric-value.success {
    color: #67C23A;
}
.metric-value.warning {
    color: #E6A23C;
}
.metric-label {
    color: #909399;
    margin-top: 8px;
}
</style>
{% endblock %}
```

**输出物**：
- ✅ 监控日志页面
- ✅ 数据过滤和分页
- ✅ 实时统计显示

### 🔧 第三天：集成测试优化 (4-6小时)

#### **上午 (3小时)：系统集成和测试**

##### ⏰ 09:00-10:00 | 双进程通信测试
```python
# 测试脚本 test_integration.py
import requests
import sqlite3
import time
import threading

def test_database_communication():
    """测试数据库通信"""
    # 测试配置更新
    config_data = {
        'show_count': 100000,
        'click_count': 5000,
        'convert_count': 1000,
        'stat_cost': 8000.0
    }
    
    # 通过Web API更新配置
    response = requests.post('http://localhost:5000/api/config', json=config_data)
    assert response.status_code == 200
    
    # 验证数据库中的数据
    conn = sqlite3.connect('data/app.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM metrics_config WHERE is_active = TRUE')
    result = cursor.fetchone()
    conn.close()
    
    assert result[2] == 100000  # show_count
    print("✅ 数据库通信测试通过")

def test_proxy_integration():
    """测试代理服务集成"""
    # 模拟HTTP请求到代理服务
    # 验证响应修改是否生效
    pass

if __name__ == '__main__':
    test_database_communication()
    test_proxy_integration()
```

##### ⏰ 10:00-11:00 | 性能优化
```python
# 数据库连接池优化
class DatabasePool:
    def __init__(self, db_path, max_connections=10):
        self.db_path = db_path
        self.pool = []
        self.max_connections = max_connections
        self._lock = threading.Lock()
    
    def get_connection(self):
        with self._lock:
            if self.pool:
                return self.pool.pop()
            else:
                return sqlite3.connect(self.db_path)
    
    def return_connection(self, conn):
        with self._lock:
            if len(self.pool) < self.max_connections:
                self.pool.append(conn)
            else:
                conn.close()

# API响应缓存
from functools import lru_cache

@lru_cache(maxsize=100)
def get_cached_metrics():
    return load_metrics_from_database()
```

##### ⏰ 11:00-12:00 | 错误处理和日志
```python
# 完善错误处理
import logging
import traceback

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)

def handle_api_error(func):
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(f"API错误: {func.__name__} - {str(e)}")
            logging.error(traceback.format_exc())
            return jsonify({'status': 'error', 'message': str(e)}), 500
    return wrapper

@app.route('/api/config', methods=['POST'])
@handle_api_error
def update_config():
    # API实现
    pass
```

#### **下午 (2-3小时)：文档完善和部署准备**

##### ⏰ 13:00-14:00 | 使用文档编写
```markdown
# 🚀 快速启动指南

## 📋 环境要求
- Python 3.7+
- 现代浏览器 (Chrome/Firefox/Safari)

## ⚡ 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **初始化数据库**  
```bash
python -c "from database import init_database; init_database()"
```

3. **启动服务**
```bash
# 启动Web服务
python app.py

# 启动代理服务 (另开终端)
mitmdump -s proxy_server.py -p 8080
```

4. **配置浏览器代理**
- 设置HTTP代理：127.0.0.1:8080
- 访问 http://mitm.it 安装证书

5. **访问管理界面**
- 打开 http://localhost:5000
- 配置广告数据
- 开始使用

## 🔧 常见问题
Q: 代理服务无法启动？
A: 检查端口8080是否被占用

Q: 浏览器无法访问HTTPS网站？
A: 确保已安装mitmproxy证书
```

##### ⏰ 14:00-15:00 | 部署脚本编写
```bash
#!/bin/bash
# deploy.sh - 一键部署脚本

echo "🚀 开始部署广告数据管理平台..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 未安装，请先安装Python 3.7+"
    exit 1
fi

# 创建虚拟环境
echo "📦 创建虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 安装依赖
echo "📥 安装依赖包..."
pip install -r requirements.txt

# 初始化数据库
echo "💾 初始化数据库..."
python -c "from database import init_database; init_database()"

# 创建日志目录
mkdir -p logs

# 生成启动脚本
cat > start.sh << 'EOF'
#!/bin/bash
source venv/bin/activate

echo "🚀 启动Web服务..."
python app.py &
WEB_PID=$!

echo "🔄 启动代理服务..."
mitmdump -s proxy_server.py -p 8080 &
PROXY_PID=$!

echo "✅ 服务启动完成!"
echo "📱 Web界面: http://localhost:5000"
echo "🔄 代理端口: 8080"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "kill $WEB_PID $PROXY_PID; exit" INT
wait
EOF

chmod +x start.sh

echo "✅ 部署完成!"
echo "🎯 运行 ./start.sh 启动服务"
```

##### ⏰ 15:00-16:00 | 最终测试和优化
```python
# 性能测试脚本
import time
import requests
import concurrent.futures

def performance_test():
    """性能测试"""
    urls = [
        'http://localhost:5000/',
        'http://localhost:5000/config',
        'http://localhost:5000/monitor',
        'http://localhost:5000/api/status'
    ]
    
    def test_url(url):
        start_time = time.time()
        try:
            response = requests.get(url, timeout=5)
            end_time = time.time()
            return {
                'url': url,
                'status': response.status_code,
                'time': (end_time - start_time) * 1000,
                'success': response.status_code == 200
            }
        except Exception as e:
            return {
                'url': url,
                'status': 0,
                'time': 0,
                'success': False,
                'error': str(e)
            }
    
    # 并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(test_url, url) for url in urls * 10]
        results = [future.result() for future in futures]
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    avg_time = sum(r['time'] for r in results if r['success']) / success_count
    
    print(f"✅ 性能测试结果:")
    print(f"   总请求数: {len(results)}")
    print(f"   成功请求: {success_count}")
    print(f"   成功率: {success_count/len(results)*100:.1f}%")
    print(f"   平均响应时间: {avg_time:.1f}ms")

if __name__ == '__main__':
    performance_test()
```

## 📊 开发计划总结

### ✅ 预期成果

#### **第一天结束**：
- ✅ Flask Web应用基础框架
- ✅ SQLite数据库和核心数据表
- ✅ mitmproxy代理服务集成
- ✅ 基础API接口实现

#### **第二天结束**：
- ✅ 完整的Web管理界面
- ✅ Vue.js + Element Plus组件集成
- ✅ 数据配置和实时预览功能
- ✅ 监控日志查看界面

#### **第三天结束**：
- ✅ 完整的一体化系统
- ✅ 性能优化和错误处理
- ✅ 部署脚本和使用文档
- ✅ 完整的测试验证

### 🎯 技术特色

1. **🚀 极简架构**：双进程 + 单数据库，避免复杂的微服务架构
2. **🔧 零构建部署**：CDN + 模板，无需前端构建工具
3. **📱 现代界面**：Vue.js + Element Plus，用户体验优秀
4. **⚡ 高性能**：数据库连接池 + 缓存策略，响应速度快
5. **🛡️ 易维护**：300行核心代码，结构清晰，便于维护

这个开发计划确保在3天内完成一个**功能完整、性能优秀、易于维护**的广告数据管理平台！ 