#!/usr/bin/env python3
"""
🔧 统一虚拟ID管理器
解决系统中多套虚拟ID生成机制冲突的问题
统一使用配置文件中的标准虚拟ID格式
"""

from typing import Dict, Any, Optional, List


class VirtualIdManager:
    """统一的虚拟ID管理器"""
    
    @staticmethod
    def get_standard_virtual_id(account_id: str, virtual_name: str, mappings: Dict[str, Any]) -> str:
        """
        获取标准虚拟ID - 统一入口
        
        Args:
            account_id: 真实账户ID
            virtual_name: 虚拟账户名称
            mappings: 账户映射配置
            
        Returns:
            str: 标准虚拟ID
        """
        mapping_info = mappings.get(account_id, {})
        account_config = mapping_info.get('account_mapper_config', {}).get('account_mapping', {})
        virtual_account = account_config.get('virtual_account', {})
        
        # 优先使用配置文件中的ID
        if 'id' in virtual_account:
            return virtual_account['id']
        
        # 回退到标准生成规则（与配置文件保持一致）
        return f'VIRTUAL_{virtual_name.replace("测试广告主", "")}'
    
    @staticmethod
    def get_all_standard_mappings(mappings: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
        """
        获取所有标准虚拟ID映射
        
        Args:
            mappings: 原始账户映射配置
            
        Returns:
            Dict: 标准化的虚拟ID映射
        """
        all_mappings = {}
        for account_id, mapping in mappings.items():
            virtual_name = mapping.get('virtual_name', '')
            virtual_id = VirtualIdManager.get_standard_virtual_id(account_id, virtual_name, mappings)
            
            all_mappings[virtual_id] = {
                'virtual_id': virtual_id,
                'real_id': account_id,
                'virtual_name': virtual_name,
                'real_name': mapping.get('real_name', '')
            }
        return all_mappings
    
    @staticmethod
    def validate_virtual_id_consistency(mappings: Dict[str, Any]) -> List[str]:
        """
        验证虚拟ID一致性，返回问题列表
        
        Args:
            mappings: 账户映射配置
            
        Returns:
            List[str]: 发现的问题列表
        """
        issues = []
        
        for account_id, mapping in mappings.items():
            virtual_name = mapping.get('virtual_name', '')
            
            # 检查配置文件中的虚拟ID
            account_config = mapping.get('account_mapper_config', {}).get('account_mapping', {})
            virtual_account = account_config.get('virtual_account', {})
            config_virtual_id = virtual_account.get('id', '')
            
            # 检查标准生成的虚拟ID
            standard_virtual_id = f'VIRTUAL_{virtual_name.replace("测试广告主", "")}'
            
            if config_virtual_id and config_virtual_id != standard_virtual_id:
                # 这是正常的，配置文件中的ID优先
                continue
            elif not config_virtual_id:
                issues.append(f"账户 {account_id} ({virtual_name}) 缺少配置文件中的虚拟ID")
        
        return issues
    
    @staticmethod
    def get_virtual_id_from_config_file(account_id: str, virtual_name: str, mappings: Dict[str, Any]) -> str:
        """
        从配置文件获取虚拟ID（推荐方法）
        
        Args:
            account_id: 真实账户ID
            virtual_name: 虚拟账户名称
            mappings: 账户映射配置
            
        Returns:
            str: 虚拟ID
        """
        return VirtualIdManager.get_standard_virtual_id(account_id, virtual_name, mappings)


def replace_dynamic_virtual_id_generation():
    """
    🚨 废弃函数警告
    
    以下虚拟ID生成方式已被废弃，请使用 VirtualIdManager.get_standard_virtual_id():
    
    ❌ f"VIRTUAL_{account_id[-4:]}"           # 基于账户ID后4位
    ❌ f"VIRTUAL_{virtual_name[-1]}_ID"       # 基于虚拟名称最后字符
    ❌ mapping.get('virtual_id', ...)         # 动态生成回退
    
    ✅ VirtualIdManager.get_standard_virtual_id(account_id, virtual_name, mappings)
    """
    raise DeprecationWarning(
        "动态虚拟ID生成已废弃，请使用 VirtualIdManager.get_standard_virtual_id()"
    )
