# 🚀 巨量引擎账户配置 - 使用指南

## ⚡ 一键启动 (推荐)

**只需要一个命令：**
```bash
python launcher.py
# 选择 2. 👥 多账户模式
```

## 🎯 错误修复

刚才您看到的错误：
```
❌ [新引擎] 修改未知类型API出错: 'list' object has no attribute 'items'
```

**已经修复！** ✅ 这是代码中处理某些特殊API格式的小bug，不影响核心功能。

## 📊 实际效果

从您的日志可以看到，**系统实际上工作正常**：

✅ **成功部分：**
- `✅ [新引擎] 用户会话信息已更新: WH-北京科技有限公司`
- `✅ [新引擎] 余额API修改完成 - 账户: WH-北京科技有限公司`
- `✅ [新引擎] 广告列表API修改完成`
- 代理服务正常运行在端口8080

## 🎯 使用方式

### 最简单的使用：
```bash
python launcher.py
# 输入: 2 (多账户模式)
# 输入: y (确认启动)
```

### API监控模式：
```bash
python launcher.py
# 输入: 1 (API监控模式)
# 用于监控和学习新的API模式
```

### 查看日志级别：
```bash
python launcher.py
# 可选择 QUIET/NORMAL/VERBOSE 日志级别
```

## 🎉 总结

- ✅ **一键启动脚本工作正常**
- ✅ **账户信息修改功能正常** 
- ✅ **刚才的错误已修复**
- ✅ **您可以直接使用，功能完全正常**

现在访问 `https://ad.oceanengine.com/` 就能看到您自定义的账户信息了！🚀 