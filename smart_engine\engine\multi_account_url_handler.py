#!/usr/bin/env python3
"""
🔗 多账户URL处理器
从 multi_account_entry_proxy.py 拆分出来的URL处理逻辑
保持文件长度在200行以内
"""

import re
from typing import Optional
from urllib.parse import urlparse, parse_qs
from smart_engine.utils.debug_logger import warning_log, debug_log


class MultiAccountUrlHandler:
    """多账户URL处理器"""
    
    def __init__(self, mappings: dict):
        """
        初始化URL处理器
        
        Args:
            mappings: 账户映射配置
        """
        self.mappings = mappings
    
    def is_account_switch_url(self, url: str) -> bool:
        """检测是否是真正的账户切换URL"""
        # 账户跳转的典型URL模式
        account_switch_patterns = [
            '/platform/api/v1/bp/multi_accounts/get_advertiser_list',  # 账户列表相关
            '/superior/api/dashboard',  # 账户首页
            '/account/',  # 账户路径
            '/ads/audience_package',  # 🔥 新增：定向包页面
            '/ads/campaign',  # 🔥 新增：广告组页面
            '/ads/creative',  # 🔥 新增：创意页面
            '/promotion/promote-manage',  # 🔥 新增：投放管理页面
        ]

        # 排除的普通API模式
        exclude_patterns = [
            '/monitor_browser/',  # 监控API
            '/mcs.',  # 统计API
            '/list',  # 列表API
            '/collect/',  # 收集API
            '/api/agw/',  # 🔥 新增：排除AGW API
            '/api/v1/agw/',  # 🔥 新增：排除AGW API
            '/copilot/api/',  # 🔥 新增：排除Copilot API
            '/nbs/api/',  # 🔥 新增：排除NBS API
        ]

        # 先排除不相关的请求
        for pattern in exclude_patterns:
            if pattern in url:
                return False

        # 再检查是否匹配账户切换模式
        for pattern in account_switch_patterns:
            if pattern in url:
                return True

        return False
    
    def extract_account_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取账户ID"""
        try:
            # 方法1: 检查aadvid参数
            aadvid_match = re.search(r'[?&]aadvid=(\d+)', url)
            if aadvid_match:
                account_id = aadvid_match.group(1)
                if account_id in self.mappings:
                    return account_id
            
            # 方法2: 检查URL路径中的账户ID
            parsed_url = urlparse(url)
            path_match = re.search(r'/(\d{15,20})(?:/|$)', parsed_url.path)
            if path_match:
                account_id = path_match.group(1)
                if account_id in self.mappings:
                    return account_id
            
            # 方法3: 检查查询参数
            query_params = parse_qs(parsed_url.query)
            for param_name in ['aadvid', 'advertiser_id', 'account_id']:
                if param_name in query_params:
                    account_id = query_params[param_name][0]
                    if account_id in self.mappings:
                        return account_id
                        
        except Exception as e:
            warning_log(f"⚠️ [URL处理器] 账户ID提取失败: {e}")
            
        return None
    
    def get_api_patterns(self) -> list:
        """获取多账户API模式列表"""
        return [
            '/platform/api/v1/bp/multi_accounts/org_with_account_list/',
            '/nbs/api/bm/promotion/ad/get_account_list',
            '/nbs/api/bm/promotion/get_part_data',
            '/bp/api/promotion/promotion_common/get_overview_data',
            '/nbs/api/bm/msg_center/notification/list_new_notifications'
        ]
    
    def match_api_pattern(self, url: str) -> Optional[str]:
        """匹配API模式"""
        api_patterns = self.get_api_patterns()
        
        for pattern in api_patterns:
            if pattern in url:
                return pattern
        
        return None
    
    def check_potential_multi_account_api(self, url: str) -> list:
        """检查是否是潜在的多账户API"""
        potential_keywords = ['multi_account', 'account_list', 'org_with_account', 'get_account', 'account_manage']
        found_keywords = [keyword for keyword in potential_keywords if keyword in url]
        return found_keywords
    
    def is_oceanengine_api(self, url: str) -> bool:
        """检查是否是oceanengine相关API"""
        return 'oceanengine.com' in url
    
    def is_business_oceanengine_api(self, url: str) -> bool:
        """检查是否是business.oceanengine.com API"""
        return 'business.oceanengine.com' in url
    
    def get_oceanengine_keywords(self) -> list:
        """获取oceanengine关键词列表"""
        return ['multi_account', 'account_list', 'org_with_account', 'get_account_list', 'advertiser', 'bp', 'promotion', 'bm']
    
    def find_oceanengine_keywords(self, url: str) -> list:
        """在URL中查找oceanengine关键词"""
        keywords = self.get_oceanengine_keywords()
        return [kw for kw in keywords if kw in url]
