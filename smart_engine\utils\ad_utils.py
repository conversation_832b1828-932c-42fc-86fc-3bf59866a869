import json
import random

metrics = {}  # 存储广告各项指标，例如消耗、转化数等

# 保存metrics到文件
def save_metrics_to_file(metrics, filename='metrics.json'):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(metrics, f, ensure_ascii=False, indent=2)

# 从文件中读取metrics
def load_metrics_from_file(filename='metrics.json'):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}
    except UnicodeDecodeError as e:
        print(f"❌ 配置文件编码错误: {e}")
        print(f"💡 尝试修复文件编码...")
        return _fix_metrics_file_encoding(filename)


# 判断嵌套的字典中是否包含某个key
def check_nested_dict_for_key(d, target_key):
    if target_key in d:
        return True
    for value in d.values():
        if isinstance(value, dict):
            if check_nested_dict_for_key(value, target_key):
                return True
    return False

# 计算各个广告指标
def calculate_ad_metrics(show_cnt, click_cnt, convert_cnt, stat_cost):
    ctr = (click_cnt / show_cnt) * 100 if show_cnt else 0  # 点击率
    conversion_rate = (convert_cnt / click_cnt) * 100 if click_cnt else 0  # 转化率
    cpm = (stat_cost / show_cnt * 1000) if show_cnt else 0  # 每千次展示成本
    cpc = (stat_cost / click_cnt) if click_cnt else 0  # 每次点击成本
    conversion_cost = (stat_cost / convert_cnt) if convert_cnt else 0  # 每次转化成本

    metrics = {
        'CTR (%)': round(ctr, 2),
        'Conversion Rate (%)': round(conversion_rate, 2),
        'CPM': round(cpm, 2),
        'CPC': round(cpc, 2),
        'Conversion Cost': round(conversion_cost, 2),
        'Stat Cost': stat_cost,
        'Show Count': show_cnt,
        'Click Count': click_cnt,
        'Convert Count': convert_cnt
    }
    
    # 为每个指标预先生成涨幅数据，保证所有API调用使用相同的涨幅数据
    metrics['Comparison Data'] = {
        'CTR': generate_ratio_and_ratio_str('CTR', metrics['CTR (%)']),
        'Conversion Rate': generate_ratio_and_ratio_str('Conversion Rate', metrics['Conversion Rate (%)']),
        'CPM': generate_ratio_and_ratio_str('CPM', metrics['CPM']),
        'CPC': generate_ratio_and_ratio_str('CPC', metrics['CPC']),
        'Conversion Cost': generate_ratio_and_ratio_str('Conversion Cost', metrics['Conversion Cost']),
        'Stat Cost': generate_ratio_and_ratio_str('Stat Cost', metrics['Stat Cost']),
        'Show Count': generate_ratio_and_ratio_str('Show Count', metrics['Show Count']),
        'Click Count': generate_ratio_and_ratio_str('Click Count', metrics['Click Count']),
        'Convert Count': generate_ratio_and_ratio_str('Convert Count', metrics['Convert Count'])
    }

    return metrics

def format_number_with_commas_int(number_str):
    """
    将数字字符串转换为带逗号的格式。

    参数:
    number_str (str): 要转换的数字字符串。

    返回:
    str: 转换后的带逗号的数字字符串。
    """
    try:
        number = int(number_str)
        formatted_number = "{:,}".format(number)
        return formatted_number
    except ValueError:
        return "Invalid input"



def format_number_with_commas(number_str):
    """
    将数字字符串转换为带逗号的格式。

    参数:
    number_str (str): 要转换的数字字符串。

    返回:
    str: 转换后的带逗号的数字字符串。
    """
    try:
        # 将字符串转换为浮点数
        number = float(number_str)
        # 将浮点数格式化为带逗号的字符串，并保留小数部分
        formatted_number = "{:,.2f}".format(number)
        return formatted_number
    except ValueError:
        return "Invalid input"




def generate_ratio_and_ratio_str(indicator, base_value):
    """
    为给定的广告投放指标随机生成Value, ValueStr, Ratio和RatioStr的值，确保正向效果。

    参数:
    indicator (str): 指标名称，例如'CTR (%)'。
    base_value (float): 基础值，用于计算Value和ValueStr。

    返回:
    dict: 包含随机生成的'Value', 'ValueStr', 'Ratio'和'RatioStr'的字典。
    """
    if 'Stat Cost' in indicator:
        #总消耗应该提升
        ratio = random.uniform(0.01, 5.0)
    elif 'Cost' in indicator or 'CPC' in indicator:
        # 转化成本、平均转化成本和每次点击成本应该降低
        ratio = random.uniform(-0.6, -0.01)
    else:
        # 其他指标应该提升
        ratio = random.uniform(0.01, 5.0)

    # 将Ratio保留14位小数
    ratio = round(ratio, 14)

    # 计算新的Value，并将其精确到小数点后两位
    value = round(base_value * (1 + ratio), 2)
    value_str = f"{value:.2f}"

    # 将Ratio转换为字符串表示形式，乘以100并加上百分号
    ratio_str = f"{ratio * 100:.2f}%"

    return {
        'Indicator': indicator,
        'Value': value,
        'ValueStr': value_str,
        'Ratio': ratio,
        'RatioStr': ratio_str
    }

def _fix_metrics_file_encoding(filename='metrics.json'):
    """修复配置文件编码问题"""
    import os
    import shutil
    
    try:
        # 创建备份
        backup_file = f"{filename}.backup"
        shutil.copy2(filename, backup_file)
        print(f"📋 已创建备份文件: {backup_file}")
        
        # 尝试用不同编码读取文件
        content = None
        for encoding in ['gbk', 'gb2312', 'latin-1']:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"✅ 使用 {encoding} 编码成功读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("❌ 无法读取配置文件，返回空配置")
            return {}
        
        # 解析JSON
        import json
        data = json.loads(content)
        
        # 用UTF-8重新保存
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("✅ 配置文件编码已修复为UTF-8")
        return data
        
    except Exception as e:
        print(f"❌ 修复配置文件失败: {e}")
        return {} 