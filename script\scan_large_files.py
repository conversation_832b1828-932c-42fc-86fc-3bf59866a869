#!/usr/bin/env python3
import os
import sys
from pathlib import Path

def check_file_encoding(file_path):
    # 尝试不同的编码
    encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'utf-16', 'ascii']
    
    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                # 读取前几行来测试编码
                f.readline()
                return encoding
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"读取文件失败 {file_path}: {str(e)}")
            return None
    
    return None

def scan_directory(directory, line_threshold=400):
    large_files = []
    encoding_issues = []
    
    # 要忽略的目录
    ignore_dirs = {'.git', 'node_modules', 'dist', 'build', '__pycache__', 'venv', '.venv'}
    
    # 要检查的文件扩展名
    check_extensions = {'.js', '.jsx', '.ts', '.tsx', '.py', '.java', '.cpp', '.c', '.h', '.hpp', '.css', '.less', '.scss', '.html', '.xml', '.json'}
    
    for root, dirs, files in os.walk(directory):
        # 忽略特定目录
        dirs[:] = [d for d in dirs if d not in ignore_dirs]
        
        for file in files:
            file_path = os.path.join(root, file)
            
            # 检查文件扩展名
            if Path(file).suffix.lower() in check_extensions:
                # 检查编码
                encoding = check_file_encoding(file_path)
                if encoding and encoding != 'utf-8':
                    rel_path = os.path.relpath(file_path, directory)
                    encoding_issues.append((rel_path, encoding))
                
                # 检查行数
                if encoding:
                    with open(file_path, 'r', encoding=encoding) as f:
                        line_count = len(f.readlines())
                        if line_count > line_threshold:
                            rel_path = os.path.relpath(file_path, directory)
                            large_files.append((rel_path, line_count))
    
    return large_files, encoding_issues

def main():
    # 获取工作目录
    work_dir = os.getcwd()
    
    print("开始扫描文件...")
    large_files, encoding_issues = scan_directory(work_dir)
    
    if encoding_issues:
        print("\n发现以下非UTF-8编码的文件:")
        print("-" * 80)
        print(f"{'文件路径':<60} {'编码':>10}")
        print("-" * 80)
        for file_path, encoding in encoding_issues:
            print(f"{file_path:<60} {encoding:>10}")
    
    if large_files:
        print("\n发现以下大文件(超过400行):")
        print("-" * 80)
        print(f"{'文件路径':<60} {'行数':>10}")
        print("-" * 80)
        for file_path, line_count in large_files:
            print(f"{file_path:<60} {line_count:>10}")
    
    if not large_files and not encoding_issues:
        print("\n未发现任何问题。")

if __name__ == "__main__":
    main() 