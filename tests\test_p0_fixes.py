#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0级别修复验证测试
验证多套逻辑和数据源问题的关键修复
"""

import os
import sys
import json
import shutil
import tempfile
import unittest
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from smart_engine.utils import ad_utils
from smart_engine.engine.multi_account_config_handler import MultiAccountConfigHandler


class TestP0Fixes(unittest.TestCase):
    """P0级别修复验证测试"""
    
    def setUp(self):
        """测试前准备"""
        self.old_cwd = os.getcwd()
        self.test_dir = tempfile.mkdtemp()
        os.chdir(self.test_dir)
        
        # 创建测试配置目录
        os.makedirs('configs/accounts', exist_ok=True)
        
        # 创建测试用的全局配置
        self.global_config = {
            'Account Name': 'WH-测试公司',
            'Account ID': '****************',
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Stat Cost': 5000.0,
            'CTR (%)': 5.0,
            'CVR (%)': 20.0,
            'CPC': 2.0,
            'CPA': 10.0,
            'account_mapping': {
                'real_account': {'name': '真实测试账户', 'id': '****************'},
                'virtual_account': {'name': 'WH-测试公司', 'id': '****************'}
            }
        }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.global_config, f, ensure_ascii=False, indent=2)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_01_global_config_not_overwritten(self):
        """测试1：验证全局配置不再被多账户覆盖"""
        print("\n🧪 测试1：全局配置保护")
        
        # 记录原始全局配置
        original_config = ad_utils.load_metrics_from_file('metrics.json')
        original_account_name = original_config['Account Name']
        
        # 创建多账户配置处理器
        mappings = {
            '****************': {
                'real_name': '测试广告主A',
                'virtual_name': '虚拟广告主A'
            }
        }
        config_handler = MultiAccountConfigHandler(mappings)
        
        # 模拟账户切换
        test_account_id = '****************'
        
        # 执行账户配置
        result = config_handler.configure_account(test_account_id)
        self.assertTrue(result, "账户配置应该成功")
        
        # 验证全局配置未被修改
        current_global_config = ad_utils.load_metrics_from_file('metrics.json')
        self.assertEqual(
            current_global_config['Account Name'], 
            original_account_name,
            "全局配置的账户名不应该被修改"
        )
        
        # 验证专用配置文件已创建
        account_config_file = f'configs/accounts/metrics_{test_account_id}.json'
        self.assertTrue(
            os.path.exists(account_config_file),
            "应该创建专用的账户配置文件"
        )
        
        # 验证专用配置内容正确
        account_config = ad_utils.load_metrics_from_file(account_config_file)
        self.assertEqual(
            account_config['Account Name'],
            '虚拟广告主A',
            "专用配置应该包含正确的虚拟账户名"
        )
        
        print("✅ 全局配置保护测试通过")
    
    def test_02_multiple_account_isolation(self):
        """测试2：验证多账户配置隔离"""
        print("\n🧪 测试2：多账户配置隔离")
        
        config_handler = MultiAccountConfigHandler()
        
        # 设置多个账户映射
        config_handler.mappings = {
            '****************': {
                'real_name': '测试广告主A',
                'virtual_name': '虚拟广告主A'
            },
            '****************': {
                'real_name': '测试广告主B', 
                'virtual_name': '虚拟广告主B'
            }
        }
        
        # 配置账户A
        result_a = config_handler.configure_account('****************')
        self.assertTrue(result_a, "账户A配置应该成功")
        
        # 配置账户B
        result_b = config_handler.configure_account('****************')
        self.assertTrue(result_b, "账户B配置应该成功")
        
        # 验证两个账户的配置文件都存在且独立
        config_a = ad_utils.load_metrics_from_file('configs/accounts/metrics_****************.json')
        config_b = ad_utils.load_metrics_from_file('configs/accounts/metrics_****************.json')
        
        self.assertEqual(config_a['Account Name'], '虚拟广告主A')
        self.assertEqual(config_b['Account Name'], '虚拟广告主B')
        
        # 验证配置互不影响
        self.assertNotEqual(config_a['Account Name'], config_b['Account Name'])
        
        # 验证全局配置仍然保持原样
        global_config = ad_utils.load_metrics_from_file('metrics.json')
        self.assertEqual(global_config['Account Name'], 'WH-测试公司')
        
        print("✅ 多账户配置隔离测试通过")
    

    def test_03_no_global_file_modification(self):
        """测试3：验证不再修改全局文件"""
        print("\n🧪 测试3：全局文件保护")

        # 记录全局文件的修改时间
        global_file_stat = os.stat('metrics.json')
        original_mtime = global_file_stat.st_mtime

        # 等待一小段时间确保时间戳不同
        import time
        time.sleep(0.1)

        # 执行多账户配置操作
        mappings = {
            '****************': {
                'real_name': '测试广告主A',
                'virtual_name': '虚拟广告主A'
            }
        }
        config_handler = MultiAccountConfigHandler(mappings)

        result = config_handler.configure_account('****************')
        self.assertTrue(result, "账户配置应该成功")

        # 检查全局文件是否被修改
        new_file_stat = os.stat('metrics.json')
        new_mtime = new_file_stat.st_mtime

        self.assertEqual(
            original_mtime,
            new_mtime,
            "全局 metrics.json 文件不应该被修改"
        )

        print("✅ 全局文件保护测试通过")


if __name__ == '__main__':
    print("🚀 开始P0级别修复验证测试")
    print("=" * 50)
    
    # 运行测试
    unittest.main(verbosity=2)
