#!/usr/bin/env python3
"""
🔍 账户数据处理器 (增强版)
专门处理复杂的递归数据转换逻辑

🎯 包含功能：
- 递归请求数据转换
- 递归响应数据转换
- 请求验证逻辑
- 复杂数据结构处理 (详细日志记录)
"""

import json
from typing import Dict, Any, Optional, List


class AccountDataProcessor:
    """账户数据处理器"""
    
    def __init__(self, transform_utils, request_transform_fields: List[str], 
                 response_whitelist: List[str]):
        """初始化数据处理器
        
        Args:
            transform_utils: 账户转换工具实例
            request_transform_fields: 请求转换字段列表
            response_whitelist: 响应白名单字段列表
        """
        self.transform_utils = transform_utils
        self.request_transform_fields = request_transform_fields
        self.response_whitelist = response_whitelist
        
        # 统计计数器
        self.current_session_stats = {
            "requests_skipped": 0,
            "transformations_applied": 0,
            "fields_processed": 0
        }
    
    def should_transform_request(self, flow) -> bool:
        """判断是否需要转换请求
        
        🎯 修复说明：根据git提交记录976bd8c，所有向巨量ad服务器发送的请求都应该使用真实账户ID
        不应该有过于严格的判断逻辑导致某些页面请求被跳过
        """
        # 只处理oceanengine.com的请求
        if not flow.request.pretty_host.endswith('oceanengine.com'):
            return False
            
        # 🔧 修复：对所有oceanengine.com的请求都进行账户参数检查和转换
        # 这样确保页面跳转（如audience_package）也能正确处理账户映射
        return True
    
    def transform_request_body(self, flow, url: str = None) -> bool:
        """转换请求体"""
        try:
            # 安全获取请求体内容
            request_text = self.transform_utils.encoding_utils.safe_get_request_text(flow.request)
            if not request_text:
                return False
                
            # 尝试解析JSON
            request_data = json.loads(request_text)
            if self.transform_request_data_recursive(request_data, url):
                # 安全设置请求体
                new_text = json.dumps(request_data, ensure_ascii=False)
                self.transform_utils.encoding_utils.safe_set_request_text(flow.request, new_text)
                return True
        except json.JSONDecodeError:
            # 处理非JSON请求体（如form data）
            return self.transform_utils.encoding_utils.transform_form_data(flow)
        except Exception as e:
            # 忽略编码错误
            pass
            
        return False
    
    def transform_request_data_recursive(self, data: Any, url: str = None) -> bool:
        """递归转换请求数据"""
        if isinstance(data, dict):
            transformed = False
            for key, value in data.items():
                # 检查键名是否是账户相关字段（请求中全转换）
                if self.transform_utils.is_request_account_field(key, self.request_transform_fields):
                    new_value = self.transform_utils.transform_value_virtual_to_real(value)
                    if new_value != value:
                        data[key] = new_value
                        transformed = True
                        # 使用新的映射日志功能
                        from smart_engine.utils.debug_logger import mapping_log
                        mapping_log("请求体字段转换", key, value, new_value, url, "request")
                        self.current_session_stats["transformations_applied"] += 1
                elif isinstance(value, (dict, list)):
                    if self.transform_request_data_recursive(value, url):
                        transformed = True
            return transformed
        elif isinstance(data, list):
            transformed = False
            for i, item in enumerate(data):
                if self.transform_request_data_recursive(item, url):
                    transformed = True
            return transformed
        return False
    
    def transform_response_recursive(self, data: Any, url: str = None, depth: int = 0) -> bool:
        """递归转换响应数据：真实 → 虚拟（白名单模式，详细日志）"""
        from smart_engine.utils.debug_logger import transform_log, debug_log, warning_log, mapping_log
        
        # 限制递归深度和日志输出
        max_depth = 5
        if depth > max_depth:
            return False
        
        if isinstance(data, dict):
            transformed = False
            
            # 只在顶层记录处理信息
            if depth == 0:
                transform_log(f"开始处理响应数据，包含{len(data)}个字段", {"url": url})
            
            # 统计不同类型字段的数量
            whitelist_transformations = []
            potential_fields = []
            
            for key, value in data.items():
                self.current_session_stats["fields_processed"] += 1
                
                # 🎯 关键修改：只转换白名单中的字段
                if self.transform_utils.is_response_display_field(key, self.response_whitelist):
                    new_value = self.transform_utils.transform_value_real_to_virtual(value)
                    if new_value != value:
                        data[key] = new_value
                        transformed = True
                        whitelist_transformations.append((key, value, new_value))
                        self.current_session_stats["transformations_applied"] += 1
                        # 使用新的映射日志功能
                        mapping_log("响应字段转换", key, value, new_value, url, "response")
                elif self.transform_utils.is_request_account_field(key, self.request_transform_fields):
                    # 账户字段但不在白名单中，跳过转换（保护功能性字段）
                    debug_log(f"跳过功能性字段: {key}={value} (保护字段)")
                elif self.transform_utils.is_potential_account_field(key, value):
                    # 🔍 发现可能的账户字段但不在白名单中
                    potential_fields.append((key, value))
                elif isinstance(value, (dict, list)):
                    # 递归处理嵌套结构
                    if self.transform_response_recursive(value, url, depth + 1):
                        transformed = True
            
            # 批量记录转换结果（仅在顶层）
            if depth == 0:
                if whitelist_transformations:
                    transform_log(f"✅ 白名单字段转换完成({len(whitelist_transformations)}个)")
                
                if potential_fields:
                    potential_summary = "; ".join([f"{k}={v}" for k, v in potential_fields[:2]])
                    if len(potential_fields) > 2:
                        potential_summary += f"... (+{len(potential_fields)-2}个)"
                    warning_log(f"⚠️ 发现潜在账户字段: {potential_summary} | URL: {url[:60] if url else 'N/A'}...")
                    
            return transformed
            
        elif isinstance(data, list):
            transformed = False
            if depth == 0:
                transform_log(f"处理响应列表，包含{len(data)}个元素", {"url": url})
            
            for i, item in enumerate(data):
                if self.transform_response_recursive(item, url, depth + 1):
                    transformed = True
                    
            return transformed
        return False
    
    def transform_request_headers(self, flow) -> bool:
        """转换请求头"""
        # 一般情况下请求头不包含账户信息，但保留接口
        return False
    
    def get_session_stats(self) -> Dict[str, int]:
        """获取当前会话统计"""
        return self.current_session_stats.copy()
    
    def reset_session_stats(self):
        """重置会话统计"""
        self.current_session_stats = {
            "requests_skipped": 0,
            "transformations_applied": 0,
            "fields_processed": 0
        } 