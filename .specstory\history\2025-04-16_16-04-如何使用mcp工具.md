<!-- Generated by SpecStory -->

# 如何使用MCP工具 (2025-04-17 00:04:29)

_**User**_

browser-tools mcp工具怎么使用

---

_**Assistant**_



---

browser-tools mcp工具是一组用于网页自动化、测试和分析的工具，主要分为两类：

1. **Playwright浏览器操作工具**
   - 基本导航：
     - `mcp_playwright_mcp_browser_navigate`：导航到指定URL
     - `mcp_playwright_mcp_browser_navigate_back`：返回上一页
     - `mcp_playwright_mcp_browser_navigate_forward`：前进到下一页
   
   - 页面交互：
     - `mcp_playwright_mcp_browser_click`：点击元素
     - `mcp_playwright_mcp_browser_type`：在输入框中输入文本
     - `mcp_playwright_mcp_browser_hover`：悬停在元素上
     - `mcp_playwright_mcp_browser_select_option`：选择下拉菜单选项
   
   - 标签页管理：
     - `mcp_playwright_mcp_browser_tab_list`：列出所有标签页
     - `mcp_playwright_mcp_browser_tab_new`：打开新标签页
     - `mcp_playwright_mcp_browser_tab_select`：选择标签页
     - `mcp_playwright_mcp_browser_tab_close`：关闭标签页

2. **浏览器调试和审计工具**
   - 日志和错误：
     - `mcp_browser_tools_getConsoleLogs`：获取控制台日志
     - `mcp_browser_tools_getConsoleErrors`：获取控制台错误
     - `mcp_browser_tools_getNetworkErrors`：获取网络错误
     - `mcp_browser_tools_getNetworkLogs`：获取所有网络日志
   
   - 审计工具：
     - `mcp_browser_tools_runAccessibilityAudit`：可访问性审计
     - `mcp_browser_tools_runPerformanceAudit`：性能审计
     - `mcp_browser_tools_runSEOAudit`：SEO审计
     - `mcp_browser_tools_runBestPracticesAudit`：最佳实践审计

要使用这些工具，只需调用相应的函数并提供所需参数。例如，导航到百度：

---



---

我已经打开了百度首页进行演示。下面展示一些常用操作：

1. 在搜索框中输入文本：

---



---

2. 点击搜索按钮：

---



---

3. 运行控制台日志查看：

---



---

现在我来详细介绍browser-tools mcp工具的主要功能和用法：

## browser-tools mcp工具概述

Browser-tools mcp是一组强大的浏览器自动化工具，主要用于网页测试、爬取和交互。主要分为以下几类：

### 1. 浏览器基础操作
- **导航与页面控制**
  - 打开网页：`mcp_playwright_mcp_browser_navigate`
  - 前进后退：`mcp_playwright_mcp_browser_navigate_back/forward`
  - 等待加载：`mcp_playwright_mcp_browser_wait`
  - 截图：`mcp_playwright_mcp_browser_take_screenshot`
  - 关闭页面：`mcp_playwright_mcp_browser_close`

### 2. 页面交互
- **元素操作**
  - 点击元素：`mcp_playwright_mcp_browser_click`
  - 输入文本：`mcp_playwright_mcp_browser_type`
  - 悬停：`mcp_playwright_mcp_browser_hover`
  - 拖拽：`mcp_playwright_mcp_browser_drag`
  - 选择下拉选项：`mcp_playwright_mcp_browser_select_option`
  - 按键：`mcp_playwright_mcp_browser_press_key`

### 3. 标签页管理
- 列出标签页：`mcp_playwright_mcp_browser_tab_list`
- 新建标签页：`mcp_playwright_mcp_browser_tab_new`
- 选择标签页：`mcp_playwright_mcp_browser_tab_select`
- 关闭标签页：`mcp_playwright_mcp_browser_tab_close`

### 4. 调试工具
- **日志与错误**
  - 控制台日志：`mcp_browser_tools_getConsoleLogs`
  - 控制台错误：`mcp_browser_tools_getConsoleErrors`
  - 网络日志：`mcp_browser_tools_getNetworkLogs`
  - 网络错误：`mcp_browser_tools_getNetworkErrors`

- **性能与审计**
  - 访问性审计：`mcp_browser_tools_runAccessibilityAudit`
  - 性能审计：`mcp_browser_tools_runPerformanceAudit`
  - SEO审计：`mcp_browser_tools_runSEOAudit`
  - 最佳实践审计：`mcp_browser_tools_runBestPracticesAudit`
  - Next.js审计：`mcp_browser_tools_runNextJSAudit`

### 5. 高级功能
- 调试模式：`mcp_browser_tools_runDebuggerMode`
- 审计模式：`mcp_browser_tools_runAuditMode`
- 文件上传：`mcp_playwright_mcp_browser_file_upload`
- PDF保存：`mcp_playwright_mcp_browser_pdf_save`

## 使用流程

1. **启动浏览器**：首先使用`mcp_playwright_mcp_browser_navigate`打开目标网页

2. **获取页面信息**：使用`mcp_playwright_mcp_browser_snapshot`获取页面元素信息，每个元素都有一个唯一的引用ID (ref)

3. **交互操作**：使用点击、输入等功能与页面交互，需提供元素描述和ref值

4. **调试分析**：使用日志和审计工具进行网页问题诊断

## 实际应用例子

1. **自动化表单填写**：
```
1. 导航到表单页面
2. 获取页面快照
3. 在输入框中输入文本
4. 点击提交按钮
```

2. **网页测试**：
```
1. 导航到目标网站
2. 执行一系列点击、输入操作
3. 收集日志和错误信息
4. 运行性能和可访问性审计
```

3. **数据抓取**：
```
1. 导航到数据源网页
2. 获取页面快照和DOM结构
3. 提取需要的数据元素
4. 导航到下一页继续
```

这些工具通常需要搭配使用才能发挥最大效果，例如先用snapshot获取页面结构，再用click和type进行交互。

您有什么特定的应用场景需要了解详情吗？

---

_**User**_

需要在node环境先启动服务吧，命令是什么来着

---

_**Assistant**_



---

在Node环境中使用browser-tools mcp工具，确实需要先启动浏览器连接器服务。启动命令通常是：

```bash
npx playwright install
npx playwright run-server
```

或者如果您已经在项目中安装了Playwright，可以在package.json中添加脚本：

```json
"scripts": {
  "start-browser-server": "playwright run-server"
}
```

然后通过以下命令启动：

```bash
npm run start-browser-server
```

如果是特定的MCP实现，可能还需要：

```bash
node path/to/mcp-browser-connector.js
```

启动服务后，您就可以使用那些browser-tools mcp工具进行浏览器自动化操作了。服务启动后会在特定端口监听连接请求，浏览器工具会自动连接到这个服务。

如果您遇到连接问题，可能需要检查端口设置或防火墙配置。

---

_**User**_

你是什么模型

---

_**Assistant**_



---

我是Claude 3.7 Sonnet，一个由Anthropic开发的大型语言模型。

---

_**User**_

什么原因

---

_**Assistant**_



---

您遇到的问题是browser-tools-mcp工具无法找到正在运行的浏览器连接器服务。从您的终端输出可以看到，工具尝试在本地（127.0.0.1和localhost）的多个端口（3025-3035）上寻找服务，但都连接失败了。

这个问题的原因是：

1. 您需要先启动单独的浏览器连接器服务，然后再运行browser-tools-mcp工具

正确的使用顺序应该是：

```bash
# 第一步：安装必要的包
npm install @agentdeskai/browser-connector

# 第二步：先启动浏览器连接器服务
npx @agentdeskai/browser-connector

# 第三步：在另一个终端窗口运行browser-tools-mcp
npx @agentdeskai/browser-tools-mcp
```

您需要在一个终端窗口中保持浏览器连接器服务运行，然后在另一个终端窗口使用browser-tools-mcp工具。连接器服务会控制实际的浏览器实例，而mcp工具则是用来发送指令给连接器。

如果仍然无法连接，可能需要检查：
- 防火墙是否阻止了这些端口
- 是否有其他安全软件干扰
- 浏览器连接器服务是否成功启动（应该有明确的成功提示）

---



---

