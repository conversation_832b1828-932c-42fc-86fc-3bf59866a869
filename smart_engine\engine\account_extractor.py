#!/usr/bin/env python3
"""
🔍 账户数据提取和转换器
处理响应数据中的账户信息提取和转换
"""

import json
import re
from typing import Dict, Any, Optional
from mitmproxy import http
from smart_engine.utils.ad_utils import load_metrics_from_file
from smart_engine.utils.account_mapper import AccountMapper
from smart_engine.utils.debug_logger import warning_log, debug_log, error_log, info_log


class AccountExtractor:
    """账户信息提取和映射处理器"""
    
    def __init__(self, metrics, account_mapper=None):
        """初始化账户提取器"""
        self.metrics = metrics
        self.account_mapper = account_mapper
    
    def auto_extract_real_account(self, flow: http.HTTPFlow):
        """自动提取真实账户信息"""
        if not hasattr(flow, 'response') or not flow.response:
            return False
            
        try:
            # 安全获取响应文本
            response_text = self.safe_get_response_text(flow.response)
            if not response_text:
                return False
                
            # 解析响应JSON
            response_data = json.loads(response_text)
            
            # 查找账户信息的常见字段
            real_account_info = self.extract_account_from_response(response_data, flow.request.url)
            
            if real_account_info:
                # 保存真实账户信息
                return self.save_real_account_mapping(real_account_info)
                    
        except (UnicodeDecodeError, UnicodeEncodeError):
            # 完全忽略编码错误
            pass
        except json.JSONDecodeError:
            # 忽略非JSON响应
            pass
        except Exception as e:
            # 静默处理错误，不影响正常流程
            pass
        
        return False
    
    def extract_account_from_response(self, data, url):
        """从响应数据中提取账户信息"""
        # 常见的账户信息字段模式
        account_patterns = [
            # 直接字段
            ('advertiser_name', 'advertiser_id'),
            ('account_name', 'account_id'), 
            ('name', 'id'),
            ('company_name', 'company_id'),
            # 嵌套字段
            ('data.advertiser_name', 'data.advertiser_id'),
            ('data.account_name', 'data.account_id'),
            ('result.advertiser_name', 'result.advertiser_id'),
        ]
        
        def get_nested_value(obj, path):
            """获取嵌套字段值"""
            keys = path.split('.')
            current = obj
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        
        # 尝试各种模式提取账户信息
        for name_pattern, id_pattern in account_patterns:
            name = get_nested_value(data, name_pattern)
            account_id = get_nested_value(data, id_pattern)
            
            if name and account_id:
                # 确保这是真实信息（不是我们已经虚拟化的）
                virtual_name = self.metrics.get('Account Name', '')
                virtual_id = self.metrics.get('Account ID', '')
                
                if str(name) != virtual_name or str(account_id) != virtual_id:
                    print(f"🔍 [自动提取] 发现真实账户信息: {name} ({account_id})")
                    return {
                        'name': str(name),
                        'id': str(account_id),
                        'source_url': url
                    }
        
        return None
    
    def save_real_account_mapping(self, real_account_info):
        """保存真实账户映射配置"""
        try:
            # 获取当前虚拟账户信息
            virtual_name = self.metrics.get('Account Name', '')
            virtual_id = self.metrics.get('Account ID', '')

            # 创建账户映射配置
            account_mapping = {
                'real_account': {
                    'name': real_account_info['name'],
                    'id': real_account_info['id']
                },
                'virtual_account': {
                    'name': virtual_name,
                    'id': virtual_id
                },
                'auto_extracted': True,
                'extraction_source': real_account_info['source_url']
            }

            # 更新配置
            updated_metrics = self.metrics.copy()
            updated_metrics['account_mapping'] = account_mapping

            # 🔥 修复：不再直接覆盖全局 metrics.json，而是更新内存配置
            # 避免多账户配置冲突
            self.metrics = updated_metrics

            # 🔥 如果在多账户环境中，应该保存到专用配置文件
            # 而不是覆盖全局配置
            from smart_engine.utils.debug_logger import info_log, warning_log
            info_log(f"✅ [账户提取器] 账户映射已更新到内存配置")
            info_log(f"   📋 映射: {real_account_info['name']} → {virtual_name}")
            warning_log(f"⚠️ [账户提取器] 不再覆盖全局 metrics.json，避免多账户冲突")
            
            print(f"✅ [自动提取] 账户映射配置已保存")
            print(f"   🔑 真实账户: {real_account_info['name']} ({real_account_info['id']})")
            print(f"   🎭 虚拟账户: {virtual_name} ({virtual_id})")
            
            # 更新内部metrics
            self.metrics = updated_metrics
            
            return True
            
        except Exception as e:
            print(f"❌ [自动提取] 保存配置失败: {e}")
            return False
    
    def safe_get_response_text(self, response):
        """安全获取响应文本，处理编码问题"""
        try:
            if hasattr(response, 'content') and response.content:
                # 尝试UTF-8解码
                try:
                    return response.content.decode('utf-8')
                except UnicodeDecodeError:
                    # 如果UTF-8失败，尝试其他编码
                    try:
                        return response.content.decode('gbk')
                    except UnicodeDecodeError:
                        try:
                            return response.content.decode('latin-1')
                        except UnicodeDecodeError:
                            return None
            # 避免访问可能有编码问题的text属性
        except Exception:
            pass
        return None
    
    def apply_response_account_mapping(self, flow: http.HTTPFlow):
        """应用响应账户信息映射：真实 → 虚拟"""
        if not self.account_mapper:
            # 只对oceanengine.com相关请求记录缺少映射器的信息
            if 'oceanengine.com' in flow.request.pretty_host:
                debug_log(f"账户映射器未激活，跳过处理: {flow.request.url[:80]}...")
            return
            
        try:
            # 只处理oceanengine.com相关的重要请求
            if 'oceanengine.com' not in flow.request.pretty_host:
                return
                
            # 安全获取响应文本
            response_text = self.safe_get_response_text(flow.response)
            if not response_text:
                # 只对重要的API请求记录获取响应失败的信息
                if any(api in flow.request.url for api in ['/api/', '/superior/api/', '/platform/api/']):
                    debug_log(f"无法获取响应文本: {flow.request.url[:80]}...")
                return
            
            debug_log(f"开始处理响应: {flow.request.url[:80]}...")
            debug_log(f"响应长度: {len(response_text)} 字符")
            
            # 尝试解析响应JSON并进行转换
            try:
                original_data = json.loads(response_text)
                debug_log("JSON解析成功", {"数据类型": type(original_data).__name__})
                
                # 创建数据副本用于比较
                import copy
                transformed_data = copy.deepcopy(original_data)
                
                # 执行转换
                has_transformation = self.account_mapper.transform_response(transformed_data, flow.request.url)
                
                if has_transformation:
                    # 安全设置响应文本
                    new_text = json.dumps(transformed_data, ensure_ascii=False)
                    if hasattr(flow.response, 'content'):
                        flow.response.content = new_text.encode('utf-8')
                    if hasattr(flow.response, 'text'):
                        flow.response.text = new_text
                    
                    info_log(f"✅ 响应转换完成: {flow.request.url[:80]}...")
                    
                    # 记录详细的转换分析
                    try:
                        from smart_engine.utils.debug_logger import response_analysis_log
                        response_analysis_log(flow.request.url, original_data, transformed_data)
                    except ImportError:
                        # 如果debug_logger不存在，跳过日志记录
                        pass
                    
                else:
                    debug_log(f"⭕ 响应无需转换: {flow.request.url[:80]}...")
                    
            except json.JSONDecodeError as e:
                # 只对重要的API请求记录JSON解析失败的信息
                if any(api in flow.request.url for api in ['/api/', '/superior/api/', '/platform/api/']):
                    # 尝试分析响应文本的前100个字符
                    preview = response_text[:100].replace('\n', '\\n').replace('\r', '\\r')
                    debug_log(f"JSON解析失败: {flow.request.url[:80]}...", {
                        "错误": str(e),
                        "响应预览": preview + "..." if len(response_text) > 100 else preview
                    })
                
        except Exception as e:
            # 只对重要的错误进行记录
            if 'oceanengine.com' in flow.request.pretty_host:
                error_log(f"处理出错: {flow.request.url[:80]}...", {"错误": str(e)}) 