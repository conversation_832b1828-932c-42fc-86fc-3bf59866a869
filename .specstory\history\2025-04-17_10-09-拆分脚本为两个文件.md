<!-- Generated by SpecStory -->

# 拆分脚本为两个文件 (2025-04-17 18:09:18)

_**User**_

@mitmPro_AD.py  这个脚本太长了，拆成2个文件，不改变任何代码，你明白我的意思吗

---

_**Assistant**_



---

我将把 `mitmPro_AD.py` 拆分成两个文件：`ad_utils.py` 和 `mitmPro_AD.py`。

首先，让我创建 `ad_utils.py` 文件，包含所有工具函数和辅助方法：

---

```diff
- 
+ import json
+ import random
+ 
+ metrics = {}  # 存储广告各项指标，例如消耗、转化数等
+ 
+ # 保存metrics到文件
+ def save_metrics_to_file(metrics, filename='metrics.json'):
+     with open(filename, 'w') as f:
+         json.dump(metrics, f)
+ 
+ # 从文件中读取metrics
+ def load_metrics_from_file(filename='metrics.json'):
+     try:
+         with open(filename, 'r') as f:
+             return json.load(f)
+     except FileNotFoundError:
+         return {}
+ 
+ 
+ # 判断嵌套的字典中是否包含某个key
+ def check_nested_dict_for_key(d, target_key):
+     if target_key in d:
+         return True
+     for value in d.values():
+         if isinstance(value, dict):
+             if check_nested_dict_for_key(value, target_key):
+                 return True
+     return False
+ 
+ # 计算各个广告指标
+ def calculate_ad_metrics(show_cnt, click_cnt, convert_cnt, stat_cost):
+     ctr = (click_cnt / show_cnt) * 100 if show_cnt else 0  # 点击率
+     conversion_rate = (convert_cnt / click_cnt) * 100 if click_cnt else 0  # 转化率
+     cpm = (stat_cost / show_cnt * 1000) if show_cnt else 0  # 每千次展示成本
+     cpc = (stat_cost / click_cnt) if click_cnt else 0  # 每次点击成本
+     conversion_cost = (stat_cost / convert_cnt) if convert_cnt else 0  # 每次转化成本
+ 
+     metrics = {
+         'CTR (%)': round(ctr, 2),
+         'Conversion Rate (%)': round(conversion_rate, 2),
+         'CPM': round(cpm, 2),
+         'CPC': round(cpc, 2),
+         'Conversion Cost': round(conversion_cost, 2),
+         'Stat Cost': stat_cost,
+         'Show Count': show_cnt,
+         'Click Count': click_cnt,
+         'Convert Count': convert_cnt
+     }
+ 
+     return metrics
+ 
+ def format_number_with_commas_int(number_str):
+     """
+     将数字字符串转换为带逗号的格式。
+ 
+     参数:
+     number_str (str): 要转换的数字字符串。
+ 
+     返回:
+     str: 转换后的带逗号的数字字符串。
+     """
+     try:
+         number = int(number_str)
+         formatted_number = "{:,}".format(number)
+         return formatted_number
+     except ValueError:
+         return "Invalid input"
+ 
+ 
+ 
+ def format_number_with_commas(number_str):
+     """
+     将数字字符串转换为带逗号的格式。
+ 
+     参数:
+     number_str (str): 要转换的数字字符串。
+ 
+     返回:
+     str: 转换后的带逗号的数字字符串。
+     """
+     try:
+         # 将字符串转换为浮点数
+         number = float(number_str)
+         # 将浮点数格式化为带逗号的字符串，并保留小数部分
+         formatted_number = "{:,.2f}".format(number)
+         return formatted_number
+     except ValueError:
+         return "Invalid input"
+ 
+ 
+ 
+ 
+ def generate_ratio_and_ratio_str(indicator, base_value):
+     """
+     为给定的广告投放指标随机生成Value, ValueStr, Ratio和RatioStr的值，确保正向效果。
+ 
+     参数:
+     indicator (str): 指标名称，例如'CTR (%)'。
+     base_value (float): 基础值，用于计算Value和ValueStr。
+ 
+     返回:
+     dict: 包含随机生成的'Value', 'ValueStr', 'Ratio'和'RatioStr'的字典。
+     """
+     if 'Stat Cost' in indicator:
+         #总消耗应该提升
+         ratio = random.uniform(0.01, 5.0)
+     elif 'Cost' in indicator or 'CPC' in indicator:
+         # 转化成本、平均转化成本和每次点击成本应该降低
+         ratio = random.uniform(-0.6, -0.01)
+     else:
+         # 其他指标应该提升
+         ratio = random.uniform(0.01, 5.0)
+ 
+     # 将Ratio保留14位小数
+     ratio = round(ratio, 14)
+ 
+     # 计算新的Value，并将其精确到小数点后两位
+     value = round(base_value * (1 + ratio), 2)
+     value_str = f"{value:.2f}"
+ 
+     # 将Ratio转换为字符串表示形式，乘以100并加上百分号
+     ratio_str = f"{ratio * 100:.2f}%"
+ 
+     return {
+         'Indicator': indicator,
+         'Value': value,
+         'ValueStr': value_str,
+         'Ratio': ratio,
+         'RatioStr': ratio_str
+     } 
```

---

现在，让我修改 `mitmPro_AD.py` 文件，从 `ad_utils.py` 导入所需函数：

---

```diff
  from mitmproxy import http
  from mitmproxy.tools.main import mitmdump
+ from ad_utils import (
+     load_metrics_from_file, save_metrics_to_file, check_nested_dict_for_key, 
+     calculate_ad_metrics, format_number_with_commas_int, format_number_with_commas,
+     generate_ratio_and_ratio_str
+ )
  
  metrics = {}  # 存储广告各项指标，例如消耗、转化数等
- 
- # 保存metrics到文件
- def save_metrics_to_file(metrics, filename='metrics.json'):
-     with open(filename, 'w') as f:
-         json.dump(metrics, f)
- 
- # 从文件中读取metrics
- def load_metrics_from_file(filename='metrics.json'):
-     try:
-         with open(filename, 'r') as f:
-             return json.load(f)
-     except FileNotFoundError:
-         return {}
- 
- 
- # 判断嵌套的字典中是否包含某个key
- def check_nested_dict_for_key(d, target_key):
-     if target_key in d:
-         return True
-     for value in d.values():
-         if isinstance(value, dict):
-             if check_nested_dict_for_key(value, target_key):
-                 return True
-     return False
- 
- # 计算各个广告指标
- def calculate_ad_metrics(show_cnt, click_cnt, convert_cnt, stat_cost):
-     ctr = (click_cnt / show_cnt) * 100 if show_cnt else 0  # 点击率
-     conversion_rate = (convert_cnt / click_cnt) * 100 if click_cnt else 0  # 转化率
-     cpm = (stat_cost / show_cnt * 1000) if show_cnt else 0  # 每千次展示成本
-     cpc = (stat_cost / click_cnt) if click_cnt else 0  # 每次点击成本
-     conversion_cost = (stat_cost / convert_cnt) if convert_cnt else 0  # 每次转化成本
- 
-     metrics = {
-         'CTR (%)': round(ctr, 2),
-         'Conversion Rate (%)': round(conversion_rate, 2),
-         'CPM': round(cpm, 2),
-         'CPC': round(cpc, 2),
-         'Conversion Cost': round(conversion_cost, 2),
-         'Stat Cost': stat_cost,
-         'Show Count': show_cnt,
-         'Click Count': click_cnt,
-         'Convert Count': convert_cnt
-     }
- 
-     return metrics
- 
- def format_number_with_commas_int(number_str):
-     """
-     将数字字符串转换为带逗号的格式。
- 
-     参数:
-     number_str (str): 要转换的数字字符串。
- 
-     返回:
-     str: 转换后的带逗号的数字字符串。
-     """
-     try:
-         number = int(number_str)
-         formatted_number = "{:,}".format(number)
-         return formatted_number
-     except ValueError:
-         return "Invalid input"
- 
- 
- 
- def format_number_with_commas(number_str):
-     """
-     将数字字符串转换为带逗号的格式。
- 
-     参数:
-     number_str (str): 要转换的数字字符串。
- 
-     返回:
-     str: 转换后的带逗号的数字字符串。
-     """
-     try:
-         # 将字符串转换为浮点数
-         number = float(number_str)
-         # 将浮点数格式化为带逗号的字符串，并保留小数部分
-         formatted_number = "{:,.2f}".format(number)
-         return formatted_number
-     except ValueError:
-         return "Invalid input"
- 
- 
- 
- 
- def generate_ratio_and_ratio_str(indicator, base_value):
-     """
-     为给定的广告投放指标随机生成Value, ValueStr, Ratio和RatioStr的值，确保正向效果。
- 
-     参数:
-     indicator (str): 指标名称，例如'CTR (%)'。
-     base_value (float): 基础值，用于计算Value和ValueStr。
- 
-     返回:
-     dict: 包含随机生成的'Value', 'ValueStr', 'Ratio'和'RatioStr'的字典。
-     """
-     if 'Stat Cost' in indicator:
-         #总消耗应该提升
-         ratio = random.uniform(0.01, 5.0)
-     elif 'Cost' in indicator or 'CPC' in indicator:
-         # 转化成本、平均转化成本和每次点击成本应该降低
-         ratio = random.uniform(-0.6, -0.01)
-     else:
-         # 其他指标应该提升
-         ratio = random.uniform(0.01, 5.0)
- 
-     # 将Ratio保留14位小数
-     ratio = round(ratio, 14)
- 
-     # 计算新的Value，并将其精确到小数点后两位
-     value = round(base_value * (1 + ratio), 2)
-     value_str = f"{value:.2f}"
- 
-     # 将Ratio转换为字符串表示形式，乘以100并加上百分号
-     ratio_str = f"{ratio * 100:.2f}%"
- 
-     return {
-         'Indicator': indicator,
-         'Value': value,
-         'ValueStr': value_str,
-         'Ratio': ratio,
-         'RatioStr': ratio_str
-     }
- 
  
  # 修改主屏
```

---

我已经成功将 `mitmPro_AD.py` 文件拆分为两个文件：

1. `ad_utils.py` - 包含了所有工具函数和辅助方法：
   - save_metrics_to_file / load_metrics_from_file (保存和加载指标)
   - check_nested_dict_for_key (检查嵌套字典键)
   - calculate_ad_metrics (计算广告指标)
   - format_number_with_commas / format_number_with_commas_int (格式化数字)
   - generate_ratio_and_ratio_str (生成比率和字符串)

2. `mitmPro_AD.py` - 包含了主要逻辑和入口点：
   - 从 ad_utils.py 导入所有必要函数
   - xiugaiZhuping (修改主屏幕数据)
   - response (响应处理函数)
   - get_ad_campaign_data (获取广告活动数据)
   - 主程序入口

两个文件协同工作，功能与原始单一文件完全相同，但结构更清晰。使用时，确保两个文件在同一目录下即可正常运行。

---



---

