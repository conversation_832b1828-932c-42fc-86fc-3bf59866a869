#!/usr/bin/env python3
"""
🧹 根目录清理器
清理项目根目录中的非必要文件，保持项目结构清洁
"""
import os
import shutil
import json
from datetime import datetime
from typing import List, Dict, Any


class RootDirectoryCleaner:
    """根目录清理器"""
    
    def __init__(self):
        """初始化清理器"""
        self.project_root = os.path.dirname(os.path.dirname(__file__))
        self.report = {
            'deleted_files': [],
            'moved_files': [],
            'deleted_directories': [],
            'kept_files': [],
            'errors': []
        }
        
        # 需要保留的核心文件
        self.keep_files = {
            'README.md',
            'launcher.py', 
            'requirements.txt',
            'metrics.json',
            'config_version.json',
            'cleanup_script.py'
        }
        
        # 需要保留的核心目录
        self.keep_directories = {
            'smart_engine',
            'src', 
            'tests',
            'docs',
            'tools',
            'config',
            'configs',
            'data',
            '__pycache__'  # 暂时保留，运行时会重新生成
        }
        
        # 需要删除的文件模式
        self.delete_patterns = [
            'test_*.py',  # 根目录的临时测试文件
            '*_report_*.json',  # 各种报告文件
            'quick_*.py',  # 快速调试脚本
            'simple_*.py',  # 简单测试脚本
            'verify_*.py',  # 验证脚本
            'erro_response.txt',  # 错误响应文件
        ]
    
    def run_cleanup(self):
        """运行完整的根目录清理"""
        print("🧹 开始根目录清理")
        print("=" * 60)
        
        try:
            # 1. 分析当前根目录状态
            self.analyze_root_directory()
            
            # 2. 清理临时和调试文件
            self.clean_temporary_files()
            
            # 3. 清理报告文件
            self.clean_report_files()
            
            # 4. 清理过期目录
            self.clean_obsolete_directories()
            
            # 5. 移动script目录内容到tools
            self.move_script_to_tools()
            
            # 6. 清理__pycache__目录
            self.clean_pycache_directories()
            
            # 7. 生成清理报告
            self.create_cleanup_report()
            
            print("\n✅ 根目录清理完成！")
            return True
            
        except Exception as e:
            print(f"❌ 清理过程中出现错误: {e}")
            self.report['errors'].append(str(e))
            return False
    
    def analyze_root_directory(self):
        """分析根目录当前状态"""
        print("\n📊 分析根目录状态...")
        
        all_items = os.listdir(self.project_root)
        files = [f for f in all_items if os.path.isfile(os.path.join(self.project_root, f))]
        directories = [d for d in all_items if os.path.isdir(os.path.join(self.project_root, d))]
        
        print(f"📁 总计: {len(files)} 个文件, {len(directories)} 个目录")
        
        # 分析文件
        keep_files = [f for f in files if f in self.keep_files]
        temp_files = [f for f in files if self._should_delete_file(f)]
        other_files = [f for f in files if f not in self.keep_files and not self._should_delete_file(f)]
        
        print(f"✅ 需要保留: {len(keep_files)} 个核心文件")
        print(f"🗑️ 可以删除: {len(temp_files)} 个临时文件")
        print(f"❓ 需要确认: {len(other_files)} 个其他文件")
        
        if other_files:
            print("   其他文件:", ', '.join(other_files))
    
    def clean_temporary_files(self):
        """清理临时和调试文件"""
        print("\n🗑️ 清理临时和调试文件...")
        
        files = [f for f in os.listdir(self.project_root) 
                if os.path.isfile(os.path.join(self.project_root, f))]
        
        deleted_count = 0
        for file in files:
            if self._should_delete_file(file) and file not in self.keep_files:
                file_path = os.path.join(self.project_root, file)
                try:
                    os.remove(file_path)
                    self.report['deleted_files'].append(file)
                    print(f"   🗑️ 删除: {file}")
                    deleted_count += 1
                except Exception as e:
                    self.report['errors'].append(f"删除文件失败 {file}: {e}")
        
        print(f"✅ 清理了 {deleted_count} 个临时文件")
    
    def clean_report_files(self):
        """清理报告文件"""
        print("\n📊 清理报告文件...")
        
        files = [f for f in os.listdir(self.project_root) 
                if os.path.isfile(os.path.join(self.project_root, f))]
        
        deleted_count = 0
        for file in files:
            if ('_report_' in file and file.endswith('.json')) or file.endswith('_report.json'):
                file_path = os.path.join(self.project_root, file)
                try:
                    os.remove(file_path)
                    self.report['deleted_files'].append(file)
                    print(f"   🗑️ 删除报告: {file}")
                    deleted_count += 1
                except Exception as e:
                    self.report['errors'].append(f"删除报告文件失败 {file}: {e}")
        
        print(f"✅ 清理了 {deleted_count} 个报告文件")
    
    def clean_obsolete_directories(self):
        """清理过期目录"""
        print("\n📁 清理过期目录...")
        
        # 检查launchers目录
        launchers_dir = os.path.join(self.project_root, 'launchers')
        if os.path.exists(launchers_dir):
            try:
                shutil.rmtree(launchers_dir)
                self.report['deleted_directories'].append('launchers/')
                print(f"   🗑️ 删除过期目录: launchers/")
            except Exception as e:
                self.report['errors'].append(f"删除launchers目录失败: {e}")
        
        print(f"✅ 过期目录清理完成")
    
    def move_script_to_tools(self):
        """移动script目录内容到tools"""
        print("\n📦 移动script目录到tools...")
        
        script_dir = os.path.join(self.project_root, 'script')
        tools_dir = os.path.join(self.project_root, 'tools')
        
        if os.path.exists(script_dir):
            moved_count = 0
            for item in os.listdir(script_dir):
                source_path = os.path.join(script_dir, item)
                target_path = os.path.join(tools_dir, item)
                
                if os.path.isfile(source_path):
                    try:
                        shutil.move(source_path, target_path)
                        self.report['moved_files'].append({
                            'from': f'script/{item}',
                            'to': f'tools/{item}'
                        })
                        print(f"   📦 移动: script/{item} → tools/{item}")
                        moved_count += 1
                    except Exception as e:
                        self.report['errors'].append(f"移动文件失败 {item}: {e}")
            
            # 删除空的script目录
            try:
                if not os.listdir(script_dir):  # 如果目录为空
                    os.rmdir(script_dir)
                    self.report['deleted_directories'].append('script/')
                    print(f"   🗑️ 删除空目录: script/")
            except Exception as e:
                self.report['errors'].append(f"删除script目录失败: {e}")
            
            print(f"✅ 移动了 {moved_count} 个文件")
        else:
            print("   ℹ️ script目录不存在")
    
    def clean_pycache_directories(self):
        """清理__pycache__目录"""
        print("\n🧹 清理__pycache__目录...")
        
        deleted_count = 0
        for root, dirs, files in os.walk(self.project_root):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_path)
                    rel_path = os.path.relpath(pycache_path, self.project_root)
                    self.report['deleted_directories'].append(rel_path)
                    print(f"   🗑️ 删除: {rel_path}")
                    deleted_count += 1
                except Exception as e:
                    self.report['errors'].append(f"删除__pycache__失败 {pycache_path}: {e}")
        
        print(f"✅ 清理了 {deleted_count} 个__pycache__目录")
    
    def _should_delete_file(self, filename: str) -> bool:
        """判断文件是否应该删除"""
        import fnmatch
        
        for pattern in self.delete_patterns:
            if fnmatch.fnmatch(filename, pattern):
                return True
        return False
    
    def create_cleanup_report(self):
        """创建清理报告"""
        print("\n📊 生成清理报告...")
        
        # 统计保留的文件
        remaining_files = [f for f in os.listdir(self.project_root) 
                          if os.path.isfile(os.path.join(self.project_root, f))]
        self.report['kept_files'] = remaining_files
        
        report = {
            'cleanup_summary': {
                'completed_at': datetime.now().isoformat(),
                'total_deleted_files': len(self.report['deleted_files']),
                'total_moved_files': len(self.report['moved_files']),
                'total_deleted_directories': len(self.report['deleted_directories']),
                'total_kept_files': len(self.report['kept_files']),
                'total_errors': len(self.report['errors'])
            },
            'deleted_files': self.report['deleted_files'],
            'moved_files': self.report['moved_files'],
            'deleted_directories': self.report['deleted_directories'],
            'kept_files': self.report['kept_files'],
            'errors': self.report['errors'],
            'final_root_structure': self._get_final_structure()
        }
        
        report_file = os.path.join(self.project_root, 'docs', 'reports', f'root_cleanup_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"✅ 清理报告已保存: {report_file}")
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")
    
    def _get_final_structure(self) -> List[str]:
        """获取最终的根目录结构"""
        items = []
        for item in os.listdir(self.project_root):
            item_path = os.path.join(self.project_root, item)
            if os.path.isfile(item_path):
                items.append(f"📄 {item}")
            elif os.path.isdir(item_path):
                items.append(f"📁 {item}/")
        return sorted(items)
    
    def show_current_status(self):
        """显示当前根目录状态"""
        print("\n📊 当前根目录状态")
        print("=" * 50)
        
        all_items = os.listdir(self.project_root)
        files = [f for f in all_items if os.path.isfile(os.path.join(self.project_root, f))]
        directories = [d for d in all_items if os.path.isdir(os.path.join(self.project_root, d))]
        
        print(f"\n📁 总计: {len(files)} 个文件, {len(directories)} 个目录")
        
        print(f"\n📄 文件列表:")
        for file in sorted(files):
            status = "✅ 保留" if file in self.keep_files else ("🗑️ 删除" if self._should_delete_file(file) else "❓ 确认")
            print(f"   {status}: {file}")
        
        print(f"\n📁 目录列表:")
        for directory in sorted(directories):
            status = "✅ 保留" if directory in self.keep_directories else "❓ 检查"
            print(f"   {status}: {directory}/")


def main():
    """主函数"""
    cleaner = RootDirectoryCleaner()
    
    print("🧹 根目录清理器")
    print("=" * 60)
    print("功能:")
    print("1. 清理临时和调试文件")
    print("2. 清理报告文件")
    print("3. 清理过期目录")
    print("4. 移动script到tools")
    print("5. 清理__pycache__目录")
    print()
    
    # 显示当前状态
    cleaner.show_current_status()
    
    # 确认执行
    confirm = input("\n是否开始根目录清理? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        success = cleaner.run_cleanup()
        if success:
            print("\n🎉 根目录清理完成！")
            print("✅ 项目根目录现在更加清洁和专业")
        else:
            print("\n❌ 清理过程中出现问题，请检查错误信息")
    else:
        print("👋 取消清理")


if __name__ == "__main__":
    main()
