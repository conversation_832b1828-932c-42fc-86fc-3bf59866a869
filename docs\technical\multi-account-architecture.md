# 多账户架构设计方案 v2.1

## 📋 核心理念

### 设计哲学：入口层模式
多账户系统**不是**重新发明轮子，而是在现有完善的单账户智能引擎**之前**添加一个简单的"入口层"：

```
用户 → [多账户入口层] → 原有单账户智能引擎
```

### 关键洞察
1. **原有系统已经完善**：单账户的SmartAPIEngine、AccountMapper等已经工作得很好
2. **只需要两个插入点**：账户列表显示 + 账户跳转映射
3. **一旦进入具体账户页面**：完全交给原有的单账户引擎处理
4. **⭐ 请求参数替换**：确保虚拟ID在请求中被替换为真实ID，解决权限问题

## 🎯 具体处理场景

### 场景1：账户列表显示
**触发条件**：用户访问多账户列表页面
**处理逻辑**：
- 拦截多账户列表API响应
- 将4个真实账户名称替换为虚拟账户名称：
  - `**************** "广州希鼎贸易"` → `"测试广告主A"`
  - `**************** "希鼎贸易"` → `"测试广告主B"`
  - `**************** "广州希鼎"` → `"测试广告主C"`
  - `**************** "初仔好得意"` → `"测试广告主D"`
- 用户看到虚拟账户列表

### 场景2：账户跳转处理
**触发条件**：用户点击虚拟账户，准备跳转到投放页面
**处理逻辑**：
- 识别用户点击的是哪个虚拟账户
- 将虚拟账户ID映射回真实账户ID
- 为该真实账户配置正确的AccountMapper配置
- 让跳转正常进入真实账户的投放页面

### 场景3：⭐ 请求参数替换（关键修复）
**触发条件**：用户在投放页面点击任何功能按钮（如定向包）
**处理逻辑**：
- **参数替换器核心思路**：
  1. **拦截阶段**：在请求发送到服务器之前拦截
  2. **识别参数**：识别URL参数和请求体中的虚拟账户ID
  3. **替换映射**：将虚拟ID替换为真实ID
  4. **透明传输**：让服务器收到真实ID，但用户界面仍显示虚拟数据
- **权限验证**：服务器收到有权限的真实ID，验证通过
- **用户体验**：功能按钮正常工作，不再跳转到账户列表

**示例**：
- 原始请求：`https://ad.oceanengine.com/ads/audience_package?aadvid=VIRTUAL_A`
- 替换后：`https://ad.oceanengine.com/ads/audience_package?aadvid=****************`

### 场景4：单账户智能引擎接管
**触发条件**：用户进入具体账户的投放管理页面
**处理逻辑**：
- **什么都不做**，完全交给原有的单账户智能引擎
- SmartAPIEngine、AccountMapper、各种Handler按原有逻辑工作
- 用户看到该账户对应的虚拟数据

## 🏗️ 简化架构设计

### 核心组件（极简版）

#### 1. 多账户入口代理 (MultiAccountEntryProxy)
**职责**：
- 拦截账户列表API，替换显示名称
- 拦截账户跳转请求，映射虚拟ID到真实ID
- 为选中账户配置正确的AccountMapper

**核心方法**：
```python
def handle_account_list_response(response) -> bool:
    """处理账户列表响应，替换显示名称"""
    
def handle_account_redirect_request(request) -> bool:
    """处理账户跳转请求，映射ID并配置AccountMapper"""
    
def setup_account_mapper_for_account(real_account_id) -> bool:
    """为指定真实账户配置AccountMapper"""
```

#### 2. ⭐ 多账户代理脚本 (MultiAccountProxyScript)
**职责**：
- 协调入口代理和智能引擎的工作
- **关键修复**：确保请求和响应都被正确处理

**核心方法**：
```python
def request(flow) -> None:
    """处理HTTP请求"""
    # 阶段1: 入口代理处理账户跳转
    entry_proxy.process_request(flow)
    
    # 阶段2: ⭐ SmartAPIEngine处理请求参数替换
    smart_engine.process_request_flow(flow)

def response(flow) -> None:
    """处理HTTP响应"""
    # 阶段1: 入口代理处理账户列表显示
    entry_proxy.process_response(flow)
    
    # 阶段2: SmartAPIEngine处理数据虚拟化
    smart_engine.process_response_flow(flow)
```

#### 3. 账户映射配置 (简化版)
**职责**：
- 存储真实账户ID到虚拟名称的映射
- 存储每个账户对应的AccountMapper配置

**配置文件格式**：
```json
{
  "mappings": {
    "****************": {
      "virtual_name": "测试广告主A",
      "real_name": "广州希鼎贸易",
      "account_mapper_config": {
        "account_mapping": {
          "real_account": {"name": "广州希鼎贸易", "id": "****************"},
          "virtual_account": {"name": "测试广告主A", "id": "VIRTUAL_A"}
        }
      }
    }
  }
}
```

### 处理流程（v2.1 完整版）

```mermaid
graph TD
    A[用户访问工作台] --> B[多账户列表API]
    B --> C[入口代理拦截响应]
    C --> D[替换显示名称]
    D --> E[用户看到虚拟账户列表]
    
    E --> F[用户点击虚拟账户A]
    F --> G[入口代理映射ID]
    G --> H[配置AccountMapper for 账户A]
    H --> I[跳转到真实账户A页面]
    
    I --> J[原有单账户智能引擎接管]
    J --> K[用户点击定向包按钮]
    K --> L[⭐ 请求参数替换]
    L --> M[虚拟ID → 真实ID]
    M --> N[服务器权限验证通过]
    N --> O[正常跳转到定向包页面]
    
    O --> P[SmartAPIEngine处理响应]
    P --> Q[AccountMapper数据虚拟化]
    Q --> R[用户看到虚拟数据]
```

## 📁 文件结构（极简版）

```
smart_engine/
├── config/
│   └── multi_account_mapping.json    # 多账户映射配置
├── engine/
│   ├── multi_account_entry_proxy.py  # 多账户入口代理
│   └── multi_account_proxy_script.py # ⭐ 多账户代理脚本（修复版）
└── utils/
    └── account_config_loader.py      # 账户配置加载器

launchers/
└── multi_account_launcher.py         # 多账户启动器（使用入口代理）
```

## 🔧 具体实现策略

### 1. 拦截关键API
只需要拦截这两个API：
- `GET /platform/api/v1/bp/multi_accounts/org_with_account_list/` - 账户列表显示
- 账户跳转相关的请求（具体URL待确认）

### 2. ⭐ 请求参数替换（关键修复）
**实现原理**：
- 在`multi_account_proxy_script.py`的`request()`函数中
- 调用`smart_engine.process_request_flow(flow)`
- SmartAPIEngine的AccountMapper自动处理参数替换
- 支持URL参数、请求体、表单数据的替换

**替换范围**：
- URL参数：`aadvid`, `advertiser_id`, `account_id`, `user_id`
- 请求体JSON：所有账户相关字段
- 表单数据：包含账户ID的表单字段

### 3. 配置管理
- 每个真实账户预配置一个AccountMapper配置
- 用户选择账户时，动态加载对应的配置
- 配置格式与现有AccountMapper完全兼容

### 4. 与现有系统的集成
- **零修改**：不修改SmartAPIEngine、AccountMapper等现有代码
- **配置驱动**：通过配置文件控制映射关系
- **代理模式**：在mitmproxy中添加入口代理逻辑

## 🎯 开发任务清单

### Phase 1: 入口代理实现 ✅
- [x] 实现 MultiAccountEntryProxy 类
- [x] 处理账户列表响应的名称替换
- [x] 创建多账户映射配置文件

### Phase 2: 账户跳转处理 ✅
- [x] 识别和拦截账户跳转请求
- [x] 实现虚拟ID到真实ID的映射
- [x] 动态配置AccountMapper

### Phase 3: ⭐ 请求参数替换修复 ✅
- [x] 修复多账户代理脚本的请求处理
- [x] 集成SmartAPIEngine的请求处理流程
- [x] 验证参数替换功能正常工作

### Phase 4: 集成测试 ✅
- [x] 创建多账户启动器
- [x] 测试完整流程
- [x] 验证与原有系统的兼容性
- [x] 验证定向包权限问题修复

## ✅ 预期效果

1. **极简实现**：只需要3个文件，约200行代码
2. **零侵入**：不修改现有的任何代码
3. **完美集成**：用户体验完全一致
4. **易维护**：逻辑简单清晰，便于理解和维护
5. **⭐ 权限修复**：定向包等功能按钮正常工作，不再有权限问题

## 🚀 关键优势

1. **不重新发明轮子**：充分利用现有的完善系统
2. **职责清晰**：入口代理只负责入口，单账户引擎负责具体处理
3. **配置驱动**：通过配置文件控制行为，灵活可调
4. **向后兼容**：完全不影响原有的单账户模式
5. **⭐ 参数替换**：透明的虚拟ID到真实ID转换，解决权限验证问题

## 🔍 技术细节：参数替换机制

### 工作原理
```python
# 用户点击定向包按钮，浏览器发起请求
原始请求: GET /ads/audience_package?aadvid=VIRTUAL_A

# mitmproxy拦截请求
↓ multi_account_proxy_script.request()
↓ smart_engine.process_request_flow()
↓ account_mapper.transform_request()
↓ URL参数替换: VIRTUAL_A → ****************

# 服务器收到真实ID请求
转换后请求: GET /ads/audience_package?aadvid=****************

# 权限验证通过，返回正常响应
# 响应数据再通过AccountMapper转换为虚拟数据显示
```

### 支持的参数类型
- **URL查询参数**：`?aadvid=VIRTUAL_A`
- **路径参数**：`/advertiser/VIRTUAL_A/campaigns`
- **POST请求体**：JSON中的账户ID字段
- **表单数据**：`advertiser_id=VIRTUAL_A`

---

*本设计方案版本：v2.1 (请求参数替换修复版)*  
*设计理念：入口层模式，极简实现 + 参数替换*  
*创建时间：2025-01-14*  
*修复时间：2025-06-14* 