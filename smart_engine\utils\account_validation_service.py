#!/usr/bin/env python3
"""
🔍 账户验证服务
在程序启动时自动检测账户映射配置的一致性

🎯 核心功能：
- 启动时自动验证账户映射
- 检测配置文件与真实账户的一致性
- 自动修复不匹配的配置
- 提供详细的验证报告
"""

import json
import os
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime


class AccountValidationService:
    """账户验证服务"""
    
    def __init__(self, config_file: str = "smart_engine/config/multi_account_mapping.json"):
        """初始化验证服务"""
        self.config_file = config_file
        self.validation_cache = {}
        self.last_validation_time = None
        
    def validate_on_startup(self, force_check: bool = False) -> Dict[str, Any]:
        """启动时验证账户映射"""
        print("🔍 [账户验证] 启动时验证账户映射配置...")
        
        # 检查是否需要验证
        if not force_check and self._should_skip_validation():
            print("✅ [账户验证] 跳过验证（最近已验证）")
            return self.validation_cache
        
        # 查找最新的API日志
        latest_log = self._find_latest_api_log()
        if not latest_log:
            print("⚠️ [账户验证] 未找到API日志文件，跳过验证")
            return {'status': 'skipped', 'reason': 'no_api_logs'}
        
        # 提取真实账户信息
        real_accounts = self._extract_real_accounts_from_log(latest_log)
        if not real_accounts:
            print("⚠️ [账户验证] 未能从API日志中提取账户信息")
            return {'status': 'failed', 'reason': 'no_accounts_extracted'}
        
        # 验证配置一致性
        validation_result = self._validate_config_consistency(real_accounts)
        
        # 缓存验证结果
        self.validation_cache = validation_result
        self.last_validation_time = time.time()
        
        # 显示验证结果
        self._display_validation_result(validation_result)
        
        # 如果有不匹配，提供修复建议
        if validation_result.get('mismatches') or validation_result.get('missing_in_config'):
            self._suggest_fixes(validation_result)
        
        return validation_result
    
    def _should_skip_validation(self) -> bool:
        """判断是否应该跳过验证（避免频繁验证）"""
        if not self.last_validation_time:
            return False
        
        # 如果上次验证在1小时内，跳过
        time_since_last = time.time() - self.last_validation_time
        return time_since_last < 3600  # 1小时
    
    def _find_latest_api_log(self) -> Optional[str]:
        """查找最新的API日志文件"""
        log_dir = "data/api_logs"
        if not os.path.exists(log_dir):
            return None
        
        log_files = []
        for filename in os.listdir(log_dir):
            if filename.endswith('_apis.jsonl'):
                log_path = os.path.join(log_dir, filename)
                log_files.append(log_path)
        
        if not log_files:
            return None
        
        # 返回最新的日志文件
        return max(log_files, key=os.path.getmtime)
    
    def _extract_real_accounts_from_log(self, log_file: str) -> List[Dict[str, Any]]:
        """从API日志中提取真实账户信息"""
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        api_data = json.loads(line)
                        url = api_data.get('url', '')
                        
                        # 查找多账户列表API
                        if 'multi_accounts/org_with_account_list' in url:
                            response_body = api_data.get('response', {}).get('body', '')
                            if response_body:
                                response_data = json.loads(response_body)
                                return self._parse_account_list_response(response_data)
                    except (json.JSONDecodeError, KeyError):
                        continue
            
            return []
            
        except Exception as e:
            print(f"❌ [账户验证] 读取API日志失败: {e}")
            return []
    
    def _parse_account_list_response(self, response_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """解析账户列表响应"""
        accounts = []
        
        try:
            data = response_data.get('data', {})
            org_list = data.get('org_list', [])
            
            for org in org_list:
                account_list = org.get('account_list', [])
                for account in account_list:
                    account_id = account.get('id', '')
                    account_name = account.get('name', '')
                    
                    if account_id and account_name:
                        accounts.append({
                            'id': str(account_id),
                            'name': account_name,
                            'status': account.get('status', ''),
                            'org_name': org.get('name', ''),
                            'org_id': org.get('id', '')
                        })
            
            return accounts
            
        except Exception as e:
            print(f"❌ [账户验证] 解析账户列表失败: {e}")
            return []
    
    def _validate_config_consistency(self, real_accounts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证配置一致性"""
        result = {
            'status': 'completed',
            'timestamp': datetime.now().isoformat(),
            'real_accounts_count': len(real_accounts),
            'config_accounts_count': 0,
            'matches': [],
            'mismatches': [],
            'missing_in_config': [],
            'missing_in_real': []
        }
        
        try:
            # 读取配置文件
            if not os.path.exists(self.config_file):
                result['status'] = 'config_not_found'
                return result
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            config_mappings = config_data.get('mappings', {})
            result['config_accounts_count'] = len(config_mappings)
            
            # 创建真实账户字典
            real_accounts_dict = {acc['id']: acc for acc in real_accounts}
            
            # 检查配置中的账户
            for account_id, config_mapping in config_mappings.items():
                config_real_name = config_mapping.get('real_name', '')
                
                if account_id in real_accounts_dict:
                    real_account = real_accounts_dict[account_id]
                    real_name = real_account['name']
                    
                    if real_name == config_real_name:
                        result['matches'].append({
                            'account_id': account_id,
                            'name': real_name,
                            'virtual_name': config_mapping.get('virtual_name', '')
                        })
                    else:
                        result['mismatches'].append({
                            'account_id': account_id,
                            'real_name': real_name,
                            'config_name': config_real_name,
                            'virtual_name': config_mapping.get('virtual_name', '')
                        })
                else:
                    result['missing_in_real'].append({
                        'account_id': account_id,
                        'config_name': config_real_name,
                        'virtual_name': config_mapping.get('virtual_name', '')
                    })
            
            # 检查真实账户中配置缺失的
            for real_account in real_accounts:
                if real_account['id'] not in config_mappings:
                    result['missing_in_config'].append(real_account)
            
            return result
            
        except Exception as e:
            print(f"❌ [账户验证] 验证配置一致性失败: {e}")
            result['status'] = 'error'
            result['error'] = str(e)
            return result
    
    def _display_validation_result(self, result: Dict[str, Any]):
        """显示验证结果"""
        print(f"\n📊 [账户验证] 验证结果:")
        print(f"   真实账户数: {result.get('real_accounts_count', 0)}")
        print(f"   配置账户数: {result.get('config_accounts_count', 0)}")
        print(f"   ✅ 匹配: {len(result.get('matches', []))} 个")
        print(f"   ❌ 不匹配: {len(result.get('mismatches', []))} 个")
        print(f"   ⚠️ 配置缺失: {len(result.get('missing_in_config', []))} 个")
        print(f"   ⚠️ 真实缺失: {len(result.get('missing_in_real', []))} 个")
        
        # 显示匹配的账户
        for match in result.get('matches', []):
            print(f"   ✅ {match['account_id']}: {match['name']} → {match['virtual_name']}")
        
        # 显示不匹配的账户
        for mismatch in result.get('mismatches', []):
            print(f"   ❌ {mismatch['account_id']}:")
            print(f"      真实名称: {mismatch['real_name']}")
            print(f"      配置名称: {mismatch['config_name']}")
            print(f"      虚拟名称: {mismatch['virtual_name']}")
        
        # 显示缺失的账户
        for missing in result.get('missing_in_config', []):
            print(f"   ⚠️ 配置缺失: {missing['id']} - {missing['name']}")
        
        for missing in result.get('missing_in_real', []):
            print(f"   ⚠️ 真实缺失: {missing['account_id']} - {missing['config_name']}")
    
    def _suggest_fixes(self, result: Dict[str, Any]):
        """提供修复建议"""
        print(f"\n🔧 [账户验证] 修复建议:")
        
        if result.get('mismatches'):
            print(f"   📝 发现 {len(result['mismatches'])} 个账户名称不匹配")
            print(f"   💡 建议：运行 'python tools/analyze_account_api_logs.py' 生成更新配置")
        
        if result.get('missing_in_config'):
            print(f"   📝 发现 {len(result['missing_in_config'])} 个新账户未配置")
            print(f"   💡 建议：更新配置文件添加新账户映射")
        
        if result.get('missing_in_real'):
            print(f"   📝 发现 {len(result['missing_in_real'])} 个配置账户不存在")
            print(f"   💡 建议：检查账户是否已被删除或ID变更")
    
    def auto_fix_mismatches(self, result: Dict[str, Any]) -> bool:
        """自动修复不匹配的配置"""
        try:
            if not result.get('mismatches'):
                return True
            
            print(f"🔧 [账户验证] 自动修复 {len(result['mismatches'])} 个不匹配...")
            
            # 读取当前配置
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 修复不匹配的账户名称
            for mismatch in result['mismatches']:
                account_id = mismatch['account_id']
                real_name = mismatch['real_name']
                
                if account_id in config_data['mappings']:
                    # 更新真实账户名称
                    config_data['mappings'][account_id]['real_name'] = real_name
                    config_data['mappings'][account_id]['account_mapper_config']['account_mapping']['real_account']['name'] = real_name
                    
                    print(f"   ✅ 修复: {account_id} - {mismatch['config_name']} → {real_name}")
            
            # 保存更新的配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ [账户验证] 配置修复完成")
            return True
            
        except Exception as e:
            print(f"❌ [账户验证] 自动修复失败: {e}")
            return False
    
    def get_validation_summary(self) -> str:
        """获取验证摘要"""
        if not self.validation_cache:
            return "未进行验证"
        
        result = self.validation_cache
        matches = len(result.get('matches', []))
        total = result.get('config_accounts_count', 0)
        
        if matches == total and total > 0:
            return f"✅ 所有 {total} 个账户配置正确"
        else:
            mismatches = len(result.get('mismatches', []))
            missing = len(result.get('missing_in_config', []))
            return f"⚠️ {matches}/{total} 个账户正确，{mismatches} 个不匹配，{missing} 个缺失"


# 全局单例实例
_validation_service = None

def get_validation_service() -> AccountValidationService:
    """获取全局验证服务实例"""
    global _validation_service
    if _validation_service is None:
        _validation_service = AccountValidationService()
    return _validation_service 