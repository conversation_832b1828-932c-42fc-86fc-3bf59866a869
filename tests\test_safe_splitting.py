#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
安全拆分测试模块

测试所有拆分方案的正确性，确保mitmproxy集成不会被破坏
"""

import unittest
import sys
import os
import importlib.util

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class SafeSplittingTest(unittest.TestCase):
    """安全拆分测试"""
    
    def test_01_proxy_pattern_mitmproxy_compatibility(self):
        """测试1：代理模式mitmproxy兼容性"""
        print("\n🧪 测试1：代理模式mitmproxy兼容性")
        
        try:
            # 测试代理文件的mitmproxy入口点
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_proxy", 
                "smart_engine/engine/smart_api_engine_proxy.py"
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 验证所有mitmproxy入口点存在
            required_functions = ['load', 'request', 'response']
            for func_name in required_functions:
                self.assertTrue(hasattr(module, func_name))
                self.assertTrue(callable(getattr(module, func_name)))
                print(f"✅ 代理模式 '{func_name}' 函数正常")
            
            # 验证SmartAPIEngine类存在且可实例化
            self.assertTrue(hasattr(module, 'SmartAPIEngine'))
            engine = module.SmartAPIEngine()
            self.assertIsNotNone(engine)
            print("✅ 代理模式SmartAPIEngine实例化成功")
            
            print("✅ 代理模式mitmproxy兼容性测试通过")
            
        except Exception as e:
            self.fail(f"代理模式mitmproxy兼容性测试失败: {e}")
    
    def test_02_factory_pattern_functionality(self):
        """测试2：工厂模式功能性"""
        print("\n🧪 测试2：工厂模式功能性")
        
        try:
            from smart_engine.engine.engine_factory import EngineFactory, EngineInitializer
            
            # 测试工厂类实例化
            config = {"api_patterns": {}, "status_mappings": {}}
            factory = EngineFactory(config)
            self.assertIsNotNone(factory)
            print("✅ EngineFactory实例化成功")
            
            # 测试初始化器
            initializer = EngineInitializer(factory)
            self.assertIsNotNone(initializer)
            print("✅ EngineInitializer实例化成功")
            
            # 测试工厂方法
            test_metrics = {"test": "data"}
            account_mapper = factory.create_account_mapper(test_metrics)
            # account_mapper可能为None（如果没有mapping配置），这是正常的
            print("✅ 工厂方法调用正常")
            
            print("✅ 工厂模式功能性测试通过")
            
        except Exception as e:
            self.fail(f"工厂模式功能性测试失败: {e}")
    
    def test_03_http_flow_processor_functionality(self):
        """测试3：HTTP流处理器功能性"""
        print("\n🧪 测试3：HTTP流处理器功能性")
        
        try:
            from smart_engine.engine.http_flow_processor import HTTPFlowProcessor, HTTPFlowEncodingFixer
            from smart_engine.engine.engine_factory import EngineFactory, EngineInitializer
            
            # 测试HTTP流处理器
            config = {"api_patterns": {}}
            factory = EngineFactory(config)
            initializer = EngineInitializer(factory)
            processor = HTTPFlowProcessor(factory, initializer)
            
            self.assertIsNotNone(processor)
            print("✅ HTTPFlowProcessor实例化成功")
            
            # 测试编码修复器
            self.assertTrue(hasattr(HTTPFlowEncodingFixer, 'fix_flow_encoding'))
            self.assertTrue(callable(HTTPFlowEncodingFixer.fix_flow_encoding))
            print("✅ HTTPFlowEncodingFixer方法存在")
            
            print("✅ HTTP流处理器功能性测试通过")
            
        except Exception as e:
            self.fail(f"HTTP流处理器功能性测试失败: {e}")
    
    def test_04_core_engine_functionality(self):
        """测试4：核心引擎功能性"""
        print("\n🧪 测试4：核心引擎功能性")
        
        try:
            from smart_engine.engine.core_engine import SmartAPIEngineCore
            
            # 测试核心引擎实例化
            core_engine = SmartAPIEngineCore()
            self.assertIsNotNone(core_engine)
            print("✅ SmartAPIEngineCore实例化成功")
            
            # 验证关键方法存在
            required_methods = ['process_request_flow', 'process_flow']
            for method_name in required_methods:
                self.assertTrue(hasattr(core_engine, method_name))
                self.assertTrue(callable(getattr(core_engine, method_name)))
                print(f"✅ 核心引擎 '{method_name}' 方法存在")
            
            print("✅ 核心引擎功能性测试通过")
            
        except Exception as e:
            self.fail(f"核心引擎功能性测试失败: {e}")
    
    def test_05_file_size_compliance(self):
        """测试5：文件大小合规性"""
        print("\n🧪 测试5：文件大小合规性")
        
        try:
            files_to_check = [
                ("smart_engine/engine/smart_api_engine_proxy.py", "代理模式入口文件"),
                ("smart_engine/engine/core_engine.py", "核心引擎文件"),
                ("smart_engine/engine/engine_factory.py", "引擎工厂文件"),
                ("smart_engine/engine/http_flow_processor.py", "HTTP流处理器文件")
            ]
            
            for file_path, description in files_to_check:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        line_count = len(f.readlines())
                    
                    print(f"📊 {description}: {line_count}行")
                    
                    # 代理文件应该很小（<100行）
                    if "proxy" in file_path:
                        self.assertLess(line_count, 100, f"{description}应该小于100行")
                    else:
                        # 其他文件应该在200行限制内
                        self.assertLessEqual(line_count, 200, f"{description}应该不超过200行")
                    
                    print(f"✅ {description}大小合规")
            
            print("✅ 文件大小合规性测试通过")
            
        except Exception as e:
            self.fail(f"文件大小合规性测试失败: {e}")
    
    def test_06_split_completeness(self):
        """测试6：拆分完整性"""
        print("\n🧪 测试6：拆分完整性")
        
        try:
            # 验证代理模式可以完全替代原文件功能
            from smart_engine.engine.smart_api_engine_proxy import SmartAPIEngine
            
            # 实例化引擎
            engine = SmartAPIEngine()
            
            # 验证关键属性存在
            required_attrs = ['config', 'metrics', 'api_identifier']
            for attr_name in required_attrs:
                self.assertTrue(hasattr(engine, attr_name))
                print(f"✅ 代理引擎属性 '{attr_name}' 存在")
            
            # 验证关键方法存在
            required_methods = ['process_request_flow', 'process_flow']
            for method_name in required_methods:
                self.assertTrue(hasattr(engine, method_name))
                self.assertTrue(callable(getattr(engine, method_name)))
                print(f"✅ 代理引擎方法 '{method_name}' 可调用")
            
            print("✅ 拆分完整性测试通过")
            
        except Exception as e:
            self.fail(f"拆分完整性测试失败: {e}")

def run_safe_splitting_tests():
    """运行安全拆分测试"""
    print("🚀 开始安全拆分测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(SafeSplittingTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有安全拆分测试通过！")
    else:
        print("❌ 部分安全拆分测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_safe_splitting_tests() 