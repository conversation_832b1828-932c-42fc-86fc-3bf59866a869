#!/usr/bin/env python3
"""
🧪 账户映射高级测试模块
专门测试数据驱动映射、特殊字符处理、边界情况等高级功能
文件长度限制：200行以内
"""

import os
import sys
import unittest
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.utils.account_mapper import AccountMapper

class TestAccountMappingAdvanced(unittest.TestCase):
    """账户映射高级功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试账户
        self.test_accounts = test_data_loader.get_test_accounts()
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_data_driven_account_mapping(self):
        """测试数据驱动的账户映射"""
        print("\n🧪 测试数据驱动账户映射")
        
        # 使用测试数据加载器的账户数据
        for account in self.test_accounts:
            account_name = account.get('name', 'unknown')
            real_account = account.get('real_account', {})
            virtual_account = account.get('virtual_account', {})
            
            print(f"  🔍 测试账户: {account_name}")
            
            # 跳过不完整的账户数据
            if not real_account or not virtual_account:
                continue
                
            # 创建包含映射的测试配置
            test_config = {
                'account_mapping': {
                    'real_account': real_account,
                    'virtual_account': virtual_account
                }
            }
            
            # 创建映射器并测试
            try:
                mapper = AccountMapper(test_config)
                
                # 验证ID转换
                real_id = real_account.get('id')
                virtual_id = virtual_account.get('id')
                
                if real_id and virtual_id:
                    converted_virtual = mapper.real_to_virtual['id'].get(real_id)
                    self.assertEqual(converted_virtual, virtual_id)
                    
                    converted_real = mapper.virtual_to_real['id'].get(virtual_id)
                    self.assertEqual(converted_real, real_id)
                
                # 验证名称转换
                real_name = real_account.get('name')
                virtual_name = virtual_account.get('name')
                
                if real_name and virtual_name:
                    converted_virtual_name = mapper.real_to_virtual['name'].get(real_name)
                    self.assertEqual(converted_virtual_name, virtual_name)
                    
                    converted_real_name = mapper.virtual_to_real['name'].get(virtual_name)
                    self.assertEqual(converted_real_name, real_name)
                
            except Exception as e:
                self.fail(f"账户 {account_name} 的映射测试失败: {e}")
        
        print("✅ 数据驱动账户映射测试通过")

    def test_edge_cases_and_special_characters(self):
        """测试边界情况和特殊字符"""
        print("\n🧪 测试边界情况和特殊字符")
        
        # 创建包含特殊字符的映射配置
        special_config = {
            'account_mapping': {
                'real_account': {
                    'id': '****************',
                    'name': '测试公司-特殊字符!@#$%^&*()'
                },
                'virtual_account': {
                    'id': '****************',
                    'name': 'Test Company - 🎯📊💼'
                }
            }
        }
        
        mapper = AccountMapper(special_config)
        
        # 测试特殊字符的转换
        real_name = '测试公司-特殊字符!@#$%^&*()'
        virtual_name = 'Test Company - 🎯📊💼'
        
        converted_virtual = mapper.real_to_virtual['name'].get(real_name)
        self.assertEqual(converted_virtual, virtual_name)
        
        converted_real = mapper.virtual_to_real['name'].get(virtual_name)
        self.assertEqual(converted_real, real_name)
        
        print("✅ 边界情况和特殊字符测试通过")

    def test_unicode_and_encoding(self):
        """测试Unicode和编码处理"""
        print("\n🧪 测试Unicode和编码处理")
        
        # 创建包含各种Unicode字符的配置
        unicode_config = {
            'account_mapping': {
                'real_account': {
                    'id': '****************',
                    'name': '中文公司名称测试'
                },
                'virtual_account': {
                    'id': '****************',
                    'name': 'English Company Name Test'
                }
            }
        }
        
        mapper = AccountMapper(unicode_config)
        
        # 测试中文字符转换
        chinese_name = '中文公司名称测试'
        english_name = 'English Company Name Test'
        
        converted_english = mapper.real_to_virtual['name'].get(chinese_name)
        self.assertEqual(converted_english, english_name)
        
        converted_chinese = mapper.virtual_to_real['name'].get(english_name)
        self.assertEqual(converted_chinese, chinese_name)
        
        print("✅ Unicode和编码处理测试通过")

    def test_large_scale_mapping(self):
        """测试大规模映射处理"""
        print("\n🧪 测试大规模映射处理")
        
        # 创建多个映射配置进行批量测试
        test_configs = []
        for i in range(10):
            config = {
                'account_mapping': {
                    'real_account': {
                        'id': f'real_id_{i:03d}',
                        'name': f'真实公司_{i:03d}'
                    },
                    'virtual_account': {
                        'id': f'virtual_id_{i:03d}',
                        'name': f'虚拟公司_{i:03d}'
                    }
                }
            }
            test_configs.append(config)
        
        # 测试每个配置
        for i, config in enumerate(test_configs):
            mapper = AccountMapper(config)
            
            real_id = f'real_id_{i:03d}'
            virtual_id = f'virtual_id_{i:03d}'
            real_name = f'真实公司_{i:03d}'
            virtual_name = f'虚拟公司_{i:03d}'
            
            # 验证ID转换
            self.assertEqual(mapper.real_to_virtual['id'].get(real_id), virtual_id)
            self.assertEqual(mapper.virtual_to_real['id'].get(virtual_id), real_id)
            
            # 验证名称转换
            self.assertEqual(mapper.real_to_virtual['name'].get(real_name), virtual_name)
            self.assertEqual(mapper.virtual_to_real['name'].get(virtual_name), real_name)
        
        print("✅ 大规模映射处理测试通过")

    def test_mapping_consistency_across_instances(self):
        """测试映射器实例间的一致性"""
        print("\n🧪 测试映射器实例间一致性")
        
        # 使用相同配置创建多个映射器实例
        config = {
            'account_mapping': {
                'real_account': {
                    'id': 'consistency_real_id',
                    'name': '一致性测试真实公司'
                },
                'virtual_account': {
                    'id': 'consistency_virtual_id',
                    'name': '一致性测试虚拟公司'
                }
            }
        }
        
        # 创建多个映射器实例
        mappers = [AccountMapper(config) for _ in range(5)]
        
        # 验证所有实例的映射结果一致
        real_id = 'consistency_real_id'
        virtual_id = 'consistency_virtual_id'
        
        for i, mapper in enumerate(mappers):
            converted_virtual = mapper.real_to_virtual['id'].get(real_id)
            converted_real = mapper.virtual_to_real['id'].get(virtual_id)
            
            self.assertEqual(converted_virtual, virtual_id, f"映射器 {i} 的虚拟ID转换不一致")
            self.assertEqual(converted_real, real_id, f"映射器 {i} 的真实ID转换不一致")
        
        print("✅ 映射器实例间一致性测试通过")

    def test_mapping_performance(self):
        """测试映射性能"""
        print("\n🧪 测试映射性能")
        
        import time
        
        # 创建映射配置
        config = {
            'account_mapping': {
                'real_account': {
                    'id': 'performance_real_id',
                    'name': '性能测试真实公司'
                },
                'virtual_account': {
                    'id': 'performance_virtual_id',
                    'name': '性能测试虚拟公司'
                }
            }
        }
        
        mapper = AccountMapper(config)
        
        # 测试大量查询的性能
        start_time = time.time()
        for _ in range(1000):
            mapper.real_to_virtual['id'].get('performance_real_id')
            mapper.virtual_to_real['id'].get('performance_virtual_id')
        end_time = time.time()
        
        query_time = end_time - start_time
        
        # 性能应该在合理范围内（通常小于1秒）
        self.assertLess(query_time, 1.0, "映射查询性能过慢")
        
        print(f"  📊 1000次查询耗时: {query_time:.3f}秒")
        print("✅ 映射性能测试通过")

def run_account_mapping_advanced_tests():
    """运行账户映射高级测试"""
    print("🚀 开始账户映射高级测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestAccountMappingAdvanced)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 账户映射高级测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有账户映射高级测试通过！")
    else:
        print("❌ 部分账户映射高级测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_account_mapping_advanced_tests()
