"""
智能API引擎核心模块

包含SmartAPIEngine的完整实现，不包含mitmproxy入口点
"""

import json
import sys
import os
from typing import Optional, Dict, Any
from mitmproxy import http

# 支持中文输出
if sys.platform.startswith('win'):
    os.environ['PYTHONIOENCODING'] = 'utf-8'

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 使用绝对导入
from smart_engine.utils.ad_utils import load_metrics_from_file
from smart_engine.utils.account_mapper import AccountMapper
from smart_engine.utils.config_paths import get_api_rules_config_path, get_account_metrics_path
from smart_engine.engine.statistics_handler import StatisticsHandler
from smart_engine.engine.balance_handler import BalanceHandler
from smart_engine.engine.metrics_handler import MetricsHandler
from smart_engine.engine.list_handler import ListHandler
from smart_engine.engine.detail_handler import DetailHandler
from smart_engine.engine.api_identifier import APIIdentifier
from smart_engine.engine.common_handler import CommonHandler
from smart_engine.engine.account_extractor import AccountExtractor
from smart_engine.engine.api_handlers import APIHandlers
from smart_engine.utils.debug_logger import info_log, success_log, warning_log, error_log, process_log, debug_log

class SmartAPIEngine:
    def __init__(self):
        """初始化智能API引擎"""
        try:
            config_path = get_api_rules_config_path()
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            success_log("✅ 配置文件加载成功")
        except Exception as e:
            error_log(f"❌ 配置文件加载失败: {e}")
            self.config = {"api_patterns": {}, "status_mappings": {}, "metrics_template": {}}
        self.metrics = {}
        self.statistics_handler = None
        self.balance_handler = None
        self.metrics_handler = None
        self.list_handler = None
        self.detail_handler = None
        self.api_identifier = APIIdentifier(self.config)
        self.common_handler = None
        self.account_mapper = None
        self.account_extractor = None
        self.api_handlers = None
        
        # 🚀 启动时预提取账户映射
        self._startup_account_mapping()
    
    def _startup_account_mapping(self):
        """启动时预提取账户映射配置"""
        from smart_engine.utils.debug_logger import debug_log

        process_log("🔄 [启动预提取] 正在检查账户映射配置...")
        debug_log(f"🔄 [启动预提取] 开始启动时账户映射初始化")

        # 简化：只使用外部传入的配置，不加载全局配置文件
        if not hasattr(self, 'metrics') or not self.metrics:
            warning_log("⚠️  [启动预提取] 未提供配置，将在运行时动态加载专用配置")
            self.metrics = {}
        else:
            info_log(f"🔄 [启动预提取] 使用外部配置: {self.metrics.get('Account Name', '未知账户')}")
            debug_log(f"   配置键: {list(self.metrics.keys())}")

        # 如果已有映射配置，直接使用
        if 'account_mapping' in self.metrics and self.metrics['account_mapping']:
            mapping = self.metrics['account_mapping']
            success_log(f"✅ [启动预提取] 发现现有映射配置:")
            info_log(f"   🔑 真实账户: {mapping['real_account']['name']} ({mapping['real_account']['id']})")
            info_log(f"   🎭 虚拟账户: {mapping['virtual_account']['name']} ({mapping['virtual_account']['id']})")

            # 初始化账户映射器
            try:
                debug_log(f"🔄 [启动预提取] 创建AccountMapper实例")
                self.account_mapper = AccountMapper(self.metrics)
                success_log(f"✅ [启动预提取] 账户映射器已激活")
                process_log(f"🔄 [启动预提取] 账户映射器初始化完成")
            except Exception as e:
                error_log(f"❌ [启动预提取] AccountMapper创建失败: {e}")
                import traceback
                debug_log(f"详细错误: {traceback.format_exc()}")
                self.account_mapper = None
            return

        # 如果没有映射配置，启动后台任务主动获取
        warning_log("⚠️  [启动预提取] 未发现映射配置，将在首次访问ad.oceanengine.com时自动提取")
        info_log("💡 [启动预提取] 建议先访问广告后台主页以激活映射功能")
        debug_log(f"   当前metrics配置: {self.metrics}")
    
    def process_request_flow(self, flow: http.HTTPFlow) -> None:
        """处理HTTP请求流 - 账户信息转换"""
        try:
            # 只处理oceanengine相关请求
            if 'oceanengine.com' not in flow.request.pretty_host:
                return
                
            debug_log(f"🔍 [请求转换] 处理请求: {flow.request.pretty_url}")
            
            # 检查账户映射器状态
            if self.account_mapper is None:
                debug_log(f"⚠️  [请求转换] 账户映射器未激活，跳过转换")
                return
                
            # 执行请求转换：虚拟 → 真实
            debug_log(f"🔄 [请求转换] 开始账户ID转换...")
            original_url = flow.request.pretty_url
            self.account_mapper.transform_request(flow)
            new_url = flow.request.pretty_url
            
            if original_url != new_url:
                info_log(f"✅ [请求转换] 请求已转换")
                debug_log(f"   📤 原始: {original_url}")
                debug_log(f"   📥 转换: {new_url}")
            else:
                debug_log(f"ℹ️  [请求转换] 请求无需转换（无账户ID参数）")
                
        except (UnicodeDecodeError, UnicodeEncodeError):
            # 完全忽略编码错误
            pass
        except Exception as e:
            # 只记录非编码相关的错误
            if 'codec' not in str(e).lower() and 'decode' not in str(e).lower():
                error_log(f"❌ [请求转换] 处理异常: {e}")
                import traceback
                traceback.print_exc()
    
    def process_flow(self, flow: http.HTTPFlow) -> None:
        """处理HTTP流 - 简化版"""
        # 🔥 修复：强制检测账户ID并加载专用配置，避免使用全局配置
        from smart_engine.utils.debug_logger import debug_log

        # 尝试从请求中检测账户ID，加载专用配置
        account_id = self._detect_account_id_from_flow(flow)
        if account_id:
            specialized_config = self._load_specialized_config(account_id)
            if specialized_config:
                # 🔥 修复：强制使用专用配置，即使已有配置
                self.metrics = specialized_config
                debug_log(f"🔄 [引擎] 强制加载专用配置: {self.metrics.get('Account Name', '未知账户')} (账户: {account_id})")
            else:
                # 如果专用配置不存在，保持当前配置或使用空配置
                if not self.metrics or len(self.metrics) == 0:
                    debug_log(f"🔄 [引擎] 专用配置不存在且无当前配置，使用空配置")
                    self.metrics = {}
                else:
                    debug_log(f"🔄 [引擎] 专用配置不存在，保持当前配置: {self.metrics.get('Account Name', '未知账户')}")
        else:
            # 如果无法检测账户ID，保持当前配置
            debug_log(f"🔄 [引擎] 无法检测账户ID，保持当前配置: {self.metrics.get('Account Name', '未知账户') if self.metrics else '无配置'}")
        
        # 初始化账户提取器
        if self.account_extractor is None or self.account_extractor.metrics != self.metrics:
            self.account_extractor = AccountExtractor(self.metrics, self.account_mapper)
        
        # 🔍 自动提取真实账户信息（如果缺少映射配置且映射器未激活）
        if self.account_mapper is None and 'account_mapping' not in self.metrics:
            if self.account_extractor.auto_extract_real_account(flow):
                # 如果提取成功，重新加载配置并初始化映射器
                self.metrics = self.account_extractor.metrics
                self.account_mapper = AccountMapper(self.metrics)
                # 更新账户提取器的映射器引用
                self.account_extractor.account_mapper = self.account_mapper
        
        # 🔥 修复：强制重新初始化所有处理器，确保使用最新配置
        # 检查是否需要重新初始化处理器（配置发生变化）
        need_reinit = (
            self.statistics_handler is None or 
            self.balance_handler is None or 
            self.metrics_handler is None or
            (hasattr(self.statistics_handler, 'metrics') and self.statistics_handler.metrics != self.metrics) or
            (hasattr(self.balance_handler, 'metrics') and self.balance_handler.metrics != self.metrics) or
            (hasattr(self.metrics_handler, 'metrics') and self.metrics_handler.metrics != self.metrics)
        )
        
        if need_reinit:
            debug_log(f"🔄 [引擎] 配置变化，重新初始化处理器...")
            self.statistics_handler = StatisticsHandler(self.metrics)
            self.balance_handler = BalanceHandler(self.metrics)
            self.metrics_handler = MetricsHandler(self.config, self.metrics)
            self.list_handler = ListHandler(self.metrics_handler)
            self.detail_handler = DetailHandler(self.metrics_handler)
            self.common_handler = CommonHandler(self.metrics_handler)
            self.api_handlers = None  # 重置API处理器，下面会重新初始化
            debug_log(f"✅ [引擎] 处理器重新初始化完成")
        
        # 初始化API处理器
        if self.api_handlers is None:
            self.api_handlers = APIHandlers(
                self.statistics_handler,
                self.balance_handler,
                self.list_handler,
                self.detail_handler,
                self.common_handler
            )
        
        # 识别API类型
        api_info = self.api_identifier.identify_api(flow)
        
        # 🔄 先执行账户信息转换：真实 → 虚拟，确保后续处理器使用正确的账户信息
        self.account_extractor.apply_response_account_mapping(flow)
        
        if api_info:
            # 调用对应的处理器（此时使用的是已转换的虚拟账户信息）
            handler_name = api_info.get('handler')
            handler_method = handler_name  # 保持原方法名，因为APIHandlers中的方法名就是modify_*
            
            if hasattr(self.api_handlers, handler_method):
                try:
                    getattr(self.api_handlers, handler_method)(flow, api_info)
                    info_log(f"✅ [引擎] {handler_name} 处理完成 - {self.metrics.get('Account Name', '未知')}")
                except Exception as e:
                    error_log(f"❌ [引擎] 处理API出错 [{handler_name}]: {e}")
                    self.api_handlers._log_error(flow, e)
            else:
                # 使用通用处理器
                self.api_handlers.generic_handler(flow, api_info)
                info_log(f"✅ [引擎] 通用处理完成 - {self.metrics.get('Account Name', '未知')}")
        else:
            # 尝试通用状态更新
            self.metrics_handler.generic_status_update(flow)
            debug_log(f"ℹ️ [引擎] 通用状态更新完成 - {self.metrics.get('Account Name', '未知')}")

    def _detect_account_id_from_flow(self, flow: http.HTTPFlow) -> Optional[str]:
        """从HTTP流中检测账户ID"""
        try:
            # 从URL参数中检测
            url = flow.request.pretty_url
            import re

            # 检测常见的账户ID参数
            patterns = [
                r'aadvid=(\d+)',
                r'advertiser_id=(\d+)',
                r'account_id=(\d+)',
                r'aavid=(\d+)'
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    account_id = match.group(1)
                    debug_log(f"🔍 [引擎] 从URL检测到账户ID: {account_id}")
                    return account_id

            # 从请求体中检测
            if hasattr(flow.request, 'content') and flow.request.content:
                try:
                    import json
                    request_body = json.loads(flow.request.content.decode('utf-8'))

                    # 检查常见的账户ID字段
                    for field in ['aadvid', 'advertiser_id', 'account_id', 'aavid']:
                        if field in request_body:
                            account_id = str(request_body[field])
                            debug_log(f"🔍 [引擎] 从请求体检测到账户ID: {account_id}")
                            return account_id
                except:
                    pass

        except Exception as e:
            debug_log(f"⚠️ [引擎] 账户ID检测失败: {e}")

        return None

    def _load_specialized_config(self, account_id: str) -> Optional[Dict[str, Any]]:
        """加载专用账户配置"""
        try:
            import os
            config_path = get_account_metrics_path(account_id)
            if os.path.exists(config_path):
                config = load_metrics_from_file(config_path)
                if config:
                    debug_log(f"✅ [引擎] 加载专用配置成功: {config.get('Account Name', '未知账户')}")
                    return config
            else:
                debug_log(f"⚠️ [引擎] 专用配置文件不存在: {config_path}")
        except Exception as e:
            debug_log(f"❌ [引擎] 加载专用配置失败: {e}")

        return None

# 兼容性：支持直接运行测试
if __name__ == "__main__":
    info_log("🧪 智能引擎测试模式")
    engine = SmartAPIEngine()
    success_log("✅ 智能引擎创建成功")