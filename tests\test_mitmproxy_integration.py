#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
mitmproxy集成测试模块

专门测试mitmproxy入口点和集成功能，防止拆分等操作破坏核心功能
"""

import unittest
import sys
import os
import importlib.util

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

class MitmproxyIntegrationTest(unittest.TestCase):
    """mitmproxy集成测试"""
    
    def setUp(self):
        """测试初始化"""
        self.engine_module_path = 'smart_engine/engine/smart_api_engine_minimal.py'
        
    def test_01_mitmproxy_entry_points_exist(self):
        """测试1：mitmproxy入口点函数存在性"""
        print("\n🧪 测试1：mitmproxy入口点函数存在性")
        
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_minimal", 
                self.engine_module_path
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查必需的入口点函数
            required_functions = ['load', 'request', 'response']
            
            for func_name in required_functions:
                self.assertTrue(
                    hasattr(module, func_name),
                    f"mitmproxy入口点函数 '{func_name}' 不存在"
                )
                func = getattr(module, func_name)
                self.assertTrue(
                    callable(func),
                    f"'{func_name}' 不是可调用函数"
                )
                print(f"✅ 入口点函数 '{func_name}' 存在且可调用")
            
            print("✅ 所有mitmproxy入口点函数存在性测试通过")
            
        except Exception as e:
            self.fail(f"mitmproxy入口点函数存在性测试失败: {e}")
    
    def test_02_global_engine_variable_exists(self):
        """测试2：全局引擎变量存在性"""
        print("\n🧪 测试2：全局引擎变量存在性")
        
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_minimal", 
                self.engine_module_path
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查全局引擎变量
            self.assertTrue(
                hasattr(module, 'smart_engine'),
                "全局变量 'smart_engine' 不存在"
            )
            
            # 初始状态应该是None
            self.assertIsNone(
                module.smart_engine,
                "全局变量 'smart_engine' 初始状态应为None"
            )
            
            print("✅ 全局引擎变量存在性测试通过")
            
        except Exception as e:
            self.fail(f"全局引擎变量存在性测试失败: {e}")
    
    def test_03_load_function_functionality(self):
        """测试3：load函数功能性"""
        print("\n🧪 测试3：load函数功能性")
        
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_minimal", 
                self.engine_module_path
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 模拟调用load函数
            mock_loader = None  # mitmproxy loader对象
            
            # 调用load函数
            module.load(mock_loader)
            
            # 验证全局引擎实例已创建
            self.assertIsNotNone(
                module.smart_engine,
                "load函数调用后，全局引擎实例应该被创建"
            )
            
            # 验证引擎实例类型
            self.assertEqual(
                type(module.smart_engine).__name__,
                'SmartAPIEngine',
                "全局引擎实例类型不正确"
            )
            
            print("✅ load函数功能性测试通过")
            
        except Exception as e:
            self.fail(f"load函数功能性测试失败: {e}")
    
    def test_04_request_response_function_structure(self):
        """测试4：request和response函数结构"""
        print("\n🧪 测试4：request和response函数结构")
        
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_minimal", 
                self.engine_module_path
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查函数签名（通过检查参数数量）
            import inspect
            
            # 检查request函数
            request_sig = inspect.signature(module.request)
            self.assertEqual(
                len(request_sig.parameters), 1,
                "request函数应该接受1个参数(flow)"
            )
            
            # 检查response函数  
            response_sig = inspect.signature(module.response)
            self.assertEqual(
                len(response_sig.parameters), 1,
                "response函数应该接受1个参数(flow)"
            )
            
            print("✅ request和response函数结构测试通过")
            
        except Exception as e:
            self.fail(f"request和response函数结构测试失败: {e}")
    
    def test_05_helper_functions_exist(self):
        """测试5：辅助函数存在性"""
        print("\n🧪 测试5：辅助函数存在性")
        
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                "smart_api_engine_minimal", 
                self.engine_module_path
            )
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 检查辅助函数
            helper_functions = ['_fix_flow_encoding', '_fix_message_encoding']
            
            for func_name in helper_functions:
                self.assertTrue(
                    hasattr(module, func_name),
                    f"辅助函数 '{func_name}' 不存在"
                )
                func = getattr(module, func_name)
                self.assertTrue(
                    callable(func),
                    f"'{func_name}' 不是可调用函数"
                )
                print(f"✅ 辅助函数 '{func_name}' 存在且可调用")
            
            print("✅ 辅助函数存在性测试通过")
            
        except Exception as e:
            self.fail(f"辅助函数存在性测试失败: {e}")
    
    def test_06_engine_file_importability(self):
        """测试6：引擎文件导入性（模拟mitmproxy导入）"""
        print("\n🧪 测试6：引擎文件导入性")
        
        try:
            # 模拟mitmproxy的导入方式
            sys.path.insert(0, os.path.dirname(os.path.abspath(self.engine_module_path)))
            
            # 尝试导入
            module_name = os.path.basename(self.engine_module_path).replace('.py', '')
            
            # 清理可能的缓存
            if module_name in sys.modules:
                del sys.modules[module_name]
            
            # 动态导入
            spec = importlib.util.spec_from_file_location(module_name, self.engine_module_path)
            module = importlib.util.module_from_spec(spec)
            sys.modules[module_name] = module
            spec.loader.exec_module(module)
            
            # 验证导入成功且包含所需函数
            required_functions = ['load', 'request', 'response']
            for func_name in required_functions:
                self.assertTrue(hasattr(module, func_name))
            
            print("✅ 引擎文件导入性测试通过")
            
        except Exception as e:
            self.fail(f"引擎文件导入性测试失败: {e}")

def run_mitmproxy_integration_tests():
    """运行mitmproxy集成测试"""
    print("🚀 开始mitmproxy集成测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(MitmproxyIntegrationTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有mitmproxy集成测试通过！")
    else:
        print("❌ 部分mitmproxy集成测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_mitmproxy_integration_tests() 