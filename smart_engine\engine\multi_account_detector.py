#!/usr/bin/env python3
"""
🔍 多账户检测器
从 multi_account_proxy_script.py 拆分出来的账户检测逻辑
保持文件长度在200行以内
"""

import re
import json
from typing import Optional
from mitmproxy import http
from smart_engine.utils.debug_logger import debug_log


class MultiAccountDetector:
    """多账户检测器"""
    
    def __init__(self, entry_proxy):
        """
        初始化检测器
        
        Args:
            entry_proxy: 入口代理实例
        """
        self.entry_proxy = entry_proxy
    
    def detect_account_from_request(self, flow: http.HTTPFlow) -> Optional[str]:
        """智能检测请求中的账户ID"""
        try:
            # 方法1: 从URL参数中提取
            from urllib.parse import urlparse, parse_qs

            url = flow.request.url

            # 检查常见的账户ID参数
            account_params = ['aadvid', 'advertiser_id', 'account_id', 'aavid']
            for param in account_params:
                match = re.search(rf'[?&]{param}=(\d{{15,20}})', url)
                if match:
                    account_id = match.group(1)
                    if self.entry_proxy and account_id in self.entry_proxy.mappings:
                        debug_log(f"🔍 [智能检测] 从URL参数 {param} 检测到账户: {account_id}")
                        return account_id

            # 方法2: 从请求体中提取（POST请求）
            if flow.request.method == 'POST' and flow.request.content:
                try:
                    body_text = flow.request.content.decode('utf-8', errors='ignore')

                    # 尝试解析JSON
                    if body_text.startswith('{'):
                        body_data = json.loads(body_text)
                        for param in account_params:
                            if param in body_data and isinstance(body_data[param], (str, int)):
                                account_id = str(body_data[param])
                                if self.entry_proxy and account_id in self.entry_proxy.mappings:
                                    debug_log(f"🔍 [智能检测] 从请求体 {param} 检测到账户: {account_id}")
                                    return account_id

                    # 尝试从表单数据中提取
                    for param in account_params:
                        match = re.search(rf'{param}["\']?\s*[:=]\s*["\']?(\d{{15,20}})', body_text)
                        if match:
                            account_id = match.group(1)
                            if self.entry_proxy and account_id in self.entry_proxy.mappings:
                                debug_log(f"🔍 [智能检测] 从请求体文本 {param} 检测到账户: {account_id}")
                                return account_id

                except Exception:
                    pass

            # 方法3: 从Cookie中提取
            if 'cookie' in flow.request.headers:
                cookie_str = flow.request.headers['cookie']
                for param in ['trace_log_adv_id', 'current_adv_id']:
                    match = re.search(rf'{param}=(\d{{15,20}})', cookie_str)
                    if match:
                        account_id = match.group(1)
                        if self.entry_proxy and account_id in self.entry_proxy.mappings:
                            debug_log(f"🔍 [智能检测] 从Cookie {param} 检测到账户: {account_id}")
                            return account_id

            return None

        except Exception as e:
            debug_log(f"⚠️ [智能检测] 账户检测失败: {e}")
            return None

    def detect_account_from_response(self, flow: http.HTTPFlow) -> Optional[str]:
        """智能检测响应中的账户ID"""
        try:
            # 方法1: 从请求URL中检测（最可靠）
            detected_from_request = self.detect_account_from_request(flow)
            if detected_from_request:
                return detected_from_request

            # 方法2: 从响应内容中检测账户信息
            if flow.response and flow.response.content:
                try:
                    response_text = flow.response.content.decode('utf-8', errors='ignore')

                    # 尝试解析JSON响应
                    if response_text.strip().startswith('{'):
                        response_data = json.loads(response_text)

                        # 检查常见的账户ID字段
                        account_fields = ['advertiser_id', 'account_id', 'aadvid', 'id']

                        def search_in_data(data, path=""):
                            """递归搜索数据中的账户ID"""
                            if isinstance(data, dict):
                                for key, value in data.items():
                                    if key in account_fields and isinstance(value, (str, int)):
                                        account_id = str(value)
                                        if self.entry_proxy and account_id in self.entry_proxy.mappings:
                                            debug_log(f"🔍 [智能检测] 从响应 {path}.{key} 检测到账户: {account_id}")
                                            return account_id

                                    # 递归搜索
                                    result = search_in_data(value, f"{path}.{key}" if path else key)
                                    if result:
                                        return result

                            elif isinstance(data, list):
                                for i, item in enumerate(data):
                                    result = search_in_data(item, f"{path}[{i}]" if path else f"[{i}]")
                                    if result:
                                        return result

                            return None

                        detected_id = search_in_data(response_data)
                        if detected_id:
                            return detected_id

                except Exception:
                    pass

            return None

        except Exception as e:
            debug_log(f"⚠️ [智能检测] 响应账户检测失败: {e}")
            return None

    def is_multi_account_api(self, url: str) -> bool:
        """检查是否是多账户相关API"""
        keywords = [
            'get_account_list', 'get_part_data', 'multi_account', 'account_list', 'org_with_account'
        ]
        return any(keyword in url for keyword in keywords)

    def is_overview_api(self, url: str) -> bool:
        """检查是否是概览数据API"""
        return '/bp/api/promotion/promotion_common/get_overview_data' in url

    def get_multi_account_api_keywords(self) -> list:
        """获取多账户API关键词列表"""
        return [
            'get_account_list', 'get_part_data', 'multi_account', 'account_list',
            'org_with_account', 'get_overview_data'
        ]

    def should_skip_single_engine(self, url: str) -> bool:
        """判断是否应该跳过单账户引擎处理"""
        # 概览数据API直接返回
        if self.is_overview_api(url):
            return True
        
        # 其他多账户API也直接返回
        if any(api in url for api in self.get_multi_account_api_keywords()):
            return True
            
        return False
