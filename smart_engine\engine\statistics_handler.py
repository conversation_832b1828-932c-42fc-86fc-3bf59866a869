import json
import math
import random
from mitmproxy import http

# 使用绝对导入
from smart_engine.utils.ad_utils import check_nested_dict_for_key, format_number_with_commas, format_number_with_commas_int

class StatisticsHandler:
    """统计数据处理器 - 性能优化版"""
    
    def __init__(self, metrics_data):
        """初始化统计处理器
        
        Args:
            metrics_data: 指标数据字典
        """
        self.metrics = metrics_data
        # 🚀 添加缓存机制
        self._totals_cache = None
        self._wave_factors_cache = {}
    
    def modify_statistics(self, flow: http.HTTPFlow, api_info: dict):
        """修改统计数据API - 性能优化版"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
                
            if "StatsData" not in response_data["data"]:
                return
            
            stats_data = response_data["data"]["StatsData"]
            
            # 处理Totals数据（汇总数据）
            if "Totals" in stats_data:
                self._update_totals_data(stats_data["Totals"])
            
            # 处理Rows数据（时间序列图表数据）
            if "Rows" in stats_data and isinstance(stats_data["Rows"], list):
                self._update_chart_rows_data_optimized(stats_data["Rows"])
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 统计数据API修改完成 - 优化版")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改统计数据API出错: {e}")
            raise e
    
    def _update_totals_data(self, totals):
        """更新汇总数据 - 使用缓存"""
        if self._totals_cache is None:
            # 🚀 预计算汇总数据映射
            self._totals_cache = {
                'ctr': ('CTR (%)', True),  # (metric_key, has_percent)
                'conversion_rate': ('Conversion Rate (%)', True),
                'cpm_platform': ('CPM', False),
                'cpc_platform': ('CPC', False),
                'conversion_cost': ('Conversion Cost', False),
                'stat_cost': ('Stat Cost', False),
                'show_cnt': ('Show Count', False),
                'click_cnt': ('Click Count', False),
                'convert_cnt': ('Convert Count', False)
            }
        
        for field, (metric_key, has_percent) in self._totals_cache.items():
            if field in totals:
                value = self.metrics.get(metric_key, 0)
                
                # 更新Value和ValueStr
                totals[field]["Value"] = value
                if has_percent:
                    totals[field]["ValueStr"] = str(value) + "%"
                else:
                    totals[field]["ValueStr"] = str(value)
                
                # 添加对比数据
                if 'Comparison Data' in self.metrics and metric_key in self.metrics['Comparison Data']:
                    comparison_data = self.metrics['Comparison Data'][metric_key]
                    totals[field]["Comparison"] = {
                        "Value": comparison_data['Value'],
                        "ValueStr": comparison_data['ValueStr'],
                        "Ratio": comparison_data['Ratio'],
                        "RatioStr": comparison_data['RatioStr']
                    }
    
    def _get_wave_factors(self, num_points):
        """获取缓存的波动因子"""
        if num_points not in self._wave_factors_cache:
            # 🚀 预计算波动因子，避免重复计算
            factors = []
            for i in range(num_points):
                # 简化波动计算，减少复杂的数学运算
                base_factor = 0.7 + 0.6 * (i / max(1, num_points - 1))  # 线性变化
                random_factor = random.uniform(0.8, 1.2)  # 简化随机因子
                wave_factor = base_factor * random_factor
                wave_factor = max(0.3, min(1.7, wave_factor))  # 限制范围
                factors.append(wave_factor)
            
            self._wave_factors_cache[num_points] = factors
        
        return self._wave_factors_cache[num_points]
    
    def _update_chart_rows_data_optimized(self, rows):
        """更新图表时间序列数据 - 性能优化版"""
        if not rows:
            return
        
        num_points = len(rows)
        
        # 🚀 预计算基础指标值
        base_metrics = {
            'show_cnt': self.metrics.get('Show Count', 0),
            'click_cnt': self.metrics.get('Click Count', 0),
            'convert_cnt': self.metrics.get('Convert Count', 0),
            'stat_cost': self.metrics.get('Stat Cost', 0),
            'ctr': self.metrics.get('CTR (%)', 0),
            'conversion_rate': self.metrics.get('Conversion Rate (%)', 0),
            'cpm_platform': self.metrics.get('CPM', 0),
            'cpc_platform': self.metrics.get('CPC', 0),
            'conversion_cost': self.metrics.get('Conversion Cost', 0)
        }
        
        # 🚀 获取预计算的波动因子
        wave_factors = self._get_wave_factors(num_points)
        
        # 🚀 批量处理，减少循环嵌套
        for i, row in enumerate(rows):
            if "Metrics" not in row:
                continue
            
            metrics = row["Metrics"]
            wave_factor = wave_factors[i]
            
            # 🚀 批量更新指标，减少重复判断
            for metric_key, base_value in base_metrics.items():
                if metric_key not in metrics or not isinstance(base_value, (int, float)) or base_value <= 0:
                    continue
                
                # 🚀 简化计算逻辑
                if metric_key in ['show_cnt', 'click_cnt', 'convert_cnt']:
                    point_value = int((base_value / num_points) * wave_factor)
                    value_str = format_number_with_commas_int(str(point_value))
                elif metric_key == 'stat_cost':
                    point_value = round((base_value / num_points) * wave_factor, 2)
                    value_str = str(point_value)
                elif metric_key in ['ctr', 'conversion_rate']:
                    point_value = round(base_value * wave_factor * 0.9, 2)  # 简化波动
                    value_str = f"{point_value}%"
                else:
                    point_value = round(base_value * wave_factor, 2)
                    value_str = str(point_value)
                
                # 更新指标数据
                metrics[metric_key]["Value"] = point_value
                metrics[metric_key]["ValueStr"] = value_str
                
                # 🚀 简化对比数据处理
                if 'Comparison Data' in self.metrics:
                    comparison_key = 'CTR' if metric_key == 'ctr' else ('Conversion Rate' if metric_key == 'conversion_rate' else None)
                    
                    if comparison_key and comparison_key in self.metrics['Comparison Data']:
                        comparison_data = self.metrics['Comparison Data'][comparison_key]
                        comp_value = round(comparison_data['Value'] * wave_factor, 2)
                        metrics[metric_key]["Comparison"] = {
                            "Value": comp_value,
                            "ValueStr": f"{comp_value}%" if metric_key in ['ctr', 'conversion_rate'] else str(comp_value),
                            "Ratio": comparison_data['Ratio'],
                            "RatioStr": comparison_data['RatioStr']
                        } 