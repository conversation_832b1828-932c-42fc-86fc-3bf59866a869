#!/usr/bin/env python3
"""
📊 测试数据加载器 - Phase 1 数据驱动测试支持
为回归测试提供结构化的测试数据和账户映射信息

🎯 功能：
- 提供标准化的测试账户数据
- 提供各种测试场景的指标数据
- 支持数据驱动的测试用例
- 为端到端测试提供基础数据支持
"""

import json
import os

class TestDataLoader:
    """测试数据加载器"""
    
    def __init__(self):
        """初始化测试数据加载器"""
        self._test_accounts = self._initialize_test_accounts()
        self._test_metrics = self._initialize_test_metrics()
        self._api_samples = self._initialize_api_samples()
    
    def _initialize_test_accounts(self):
        """初始化测试账户数据"""
        return {
            'basic_account': {
                'real_account': {
                    'name': '真实测试账户',
                    'id': '****************'
                },
                'virtual_account': {
                    'name': 'WH-测试公司',
                    'id': '****************'
                }
            },
            'premium_account': {
                'real_account': {
                    'name': '高级真实账户',
                    'id': '****************'
                },
                'virtual_account': {
                    'name': 'WH-高级广告公司',
                    'id': '****************'
                }
            }
        }
    
    def _initialize_test_metrics(self):
        """初始化测试指标数据"""
        return [
            {
                'name': 'standard_campaign',
                'description': '标准广告活动测试数据',
                'input': {
                    'show_cnt': 50000,
                    'click_cnt': 2500,
                    'convert_cnt': 500,
                    'stat_cost': 100.0
                },
                'expected': {
                    'CTR (%)': 5.0,
                    'Conversion Rate (%)': 20.0,
                    'CPM': 2.0,
                    'CPC': 0.04,
                    'Conversion Cost': 0.2,
                    'Stat Cost': 100.0,
                    'Show Count': 50000,
                    'Click Count': 2500,
                    'Convert Count': 500
                }
            }
        ]
    
    def _initialize_api_samples(self):
        """初始化API响应样本数据"""
        return {
            'balance_api': {
                'url': '/ad/api/account/balance',
                'method': 'GET',
                'response': {
                    'code': 0,
                    'message': 'OK',
                    'data': {
                        'balance': 100000.50,
                        'available_balance': 95000.30,
                        'frozen_balance': 5000.20,
                        'advertiser_id': '****************'
                    }
                }
            }
        }
    
    def get_account_by_name(self, account_name):
        """根据账户名称获取账户数据"""
        return self._test_accounts.get(account_name, self._test_accounts['basic_account'])
    
    def get_metrics_by_name(self, metrics_name):
        """根据指标名称获取指标数据"""
        for metrics in self._test_metrics:
            if metrics['name'] == metrics_name:
                return metrics
        return self._test_metrics[0]
    
    def get_test_metrics(self):
        """获取所有测试指标数据"""
        return self._test_metrics
    
    def get_api_responses(self, api_type):
        """根据API类型获取响应数据"""
        if api_type == 'balance_api':
            try:
                import json
                with open('tests/test_data/api_responses/balance_api_responses.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        elif api_type == 'statistics_api':
            try:
                import json
                with open('tests/test_data/api_responses/statistics_api_responses.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def get_test_accounts(self):
        """获取测试账户列表"""
        return [
            {
                'name': 'basic_account',
                'real_account': {'name': '真实测试账户', 'id': '****************'},
                'virtual_account': {'name': 'WH-测试公司', 'id': '****************'}
            },
            {
                'name': 'premium_account', 
                'real_account': {'name': '高级真实账户', 'id': '****************'},
                'virtual_account': {'name': 'WH-高级广告公司', 'id': '****************'}
            }
        ]

# 创建全局实例
test_data_loader = TestDataLoader()