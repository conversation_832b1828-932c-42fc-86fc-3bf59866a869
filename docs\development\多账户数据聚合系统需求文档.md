# 多账户数据聚合系统需求文档

## 📋 项目背景

用户在Oceanengine多账户管理页面（`https://business.oceanengine.com/site/account-manage/ad/bidding/superior/account`）需要实现虚拟账户数据展示功能，将每个真实账户的昵称、ID、投放数据全部替换为虚拟数据，包括多账户列表页面的概览数据，并确保虚拟数据之间的逻辑一致性。

## 🎯 核心需求

### 1. **功能本质**
- **账户信息虚拟化**：真实账户昵称、ID → 虚拟账户昵称、ID
- **投放数据虚拟化**：真实投放数据 → 虚拟投放数据
- **概览数据虚拟化**：多账户列表页面的汇总数据也要虚拟化
- **逻辑一致性保证**：虚拟概览数据 = 各虚拟账户数据汇总

### 2. **页面结构理解**
- **多账户列表页面**：包含三个标签页
  - "全部"：显示所有广告类型的汇总数据
  - "通投广告"：显示通投广告的汇总数据
  - "搜索广告"：显示搜索广告的汇总数据
- **单账户详情页面**：点击具体账户进入的详情页面

### 3. **虚拟数据关系逻辑**
#### **标签页数据关系**：
- **全部广告数据 = 通投广告数据 + 搜索广告数据**
- 当前业务情况：搜索广告数据为0（账户未投放搜索广告）
- 因此：全部广告数据 = 通投广告数据

#### **虚拟账户映射关系**：
- **真实账户** → **虚拟账户**：
  - 希赢贸易 (****************) → 虚拟账户A (VIRTUAL_A)
  - 广州希赢贸易 (****************) → 虚拟账户B (VIRTUAL_B)
  - 广州希赢 (1830452056275719) → 虚拟账户C (VIRTUAL_C)
  - 初仔好得意 (1829638227214851) → 虚拟账户D (VIRTUAL_D)

#### **虚拟数据分配示例**：
- **多账户列表页面**（虚拟概览数据）：
  - 总消耗：1,515.10元（虚拟汇总）
  - 虚拟账户A：约1,460元（96.4%）
  - 虚拟账户B：约52元（3.4%）
  - 虚拟账户C：约3元（0.2%）
  - 虚拟账户D：0元（0%）

- **单账户详情页面**（虚拟分账户数据）：
  - 点击"虚拟账户A" → 显示：消耗1,460元及对应的虚拟展示、转化数据
  - 点击"虚拟账户B" → 显示：消耗52元及对应的虚拟展示、转化数据
  - 点击"虚拟账户C" → 显示：消耗3元及对应的虚拟展示、转化数据
  - 点击"虚拟账户D" → 显示：消耗0元及对应的虚拟展示、转化数据

#### **数学一致性验证**：
```
虚拟账户A + 虚拟账户B + 虚拟账户C + 虚拟账户D = 虚拟概览总数据 ✅
1,460 + 52 + 3 + 0 = 1,515元 ✅
```

## 🔧 技术实现要求

### 1. **API拦截处理**
- **多账户列表API**：`/nbs/api/bm/promotion/ad/get_account_list`
- **广告类型识别**：通过请求参数`pricingCategory`区分
  - `[2]` = 通投广告
  - `[1]` = 搜索广告
  - `[]` 或不传 = 全部广告

### 2. **虚拟数据策略**
- **通投广告**：使用配置的虚拟数据（消耗1,515.10元，展示64,794，转化44）
- **搜索广告**：所有虚拟数据为0
- **全部广告**：通投广告虚拟数据（因搜索广告为0）

### 3. **虚拟账户数据分配**
- **虚拟数据分配比例**：
  - 虚拟账户A：96.4%（主要账户）
  - 虚拟账户B：3.4%（次要账户）
  - 虚拟账户C：0.2%（少量数据）
  - 虚拟账户D：0%（无数据）

### 4. **虚拟数据一致性要求**
- **数值汇总**：各虚拟账户数据汇总必须等于虚拟概览总数据
- **比率计算**：转化成本、转化率等需重新计算，不能简单相加
- **逻辑一致性**：单虚拟账户数据与多虚拟账户概览数据必须保持数学逻辑一致

## ✅ 已完成功能

### 1. **多账户虚拟化功能**
- ✅ 广告类型识别（全部/通投/搜索）
- ✅ 虚拟数据替换逻辑实现
- ✅ 虚拟账户信息生成
- ✅ API响应数据替换
- ✅ JSON序列化处理

### 2. **虚拟数据配置统一**
- ✅ 更新`metrics.json`配置文件
- ✅ 确保单虚拟账户与多虚拟账户数据源一致
- ✅ 修正所有相关虚拟指标（CTR、转化率、转化成本等）

### 3. **测试验证**
- ✅ 多账户列表页面虚拟数据正确显示
- ✅ 三个标签页虚拟数据逻辑正确
- ✅ 虚拟数据一致性验证通过

## 🚧 待完成功能

### 1. **多账户概览数据虚拟化聚合**（唯一缺失功能）
- ❌ 在多账户列表API响应中聚合各账户的虚拟投放数据
- ❌ 实现广告类型识别（全部/通投/搜索）
- ❌ 确保概览数据 = 各虚拟账户数据汇总

### 2. **技术实现细节**
- 扩展 `MultiAccountEntryProxy.handle_account_list_response` 方法
- 从各账户配置文件读取虚拟数据并聚合
- 替换API响应中的投放数据字段（消耗、展示、转化等）

## 🔄 设计方案优化

### **当前系统架构**（已实现且最优雅）：
- **入口层模式**：`MultiAccountEntryProxy` 处理账户列表显示和跳转
- **数据虚拟化**：完全复用 `SmartAPIEngine` 处理单账户数据
- **配置驱动**：每个账户独立配置文件，单一数据源保证一致性

### **最优方案**（基于当前架构补充）：
保持当前优雅架构，只需补充**多账户概览数据虚拟化聚合**功能：

#### **核心思路**：
1. **保持现有架构**：不修改 `SmartAPIEngine`、`AccountMapper` 等核心组件
2. **单一数据源**：每个账户只有一个配置文件 `configs/accounts/metrics_{account_id}.json`
3. **实时聚合**：从各账户配置文件读取虚拟数据，实时计算概览汇总
4. **天然一致性**：概览数据 = 各账户虚拟数据汇总，无需手动保证一致性

#### **当前系统配置文件结构**（已实现且优雅）：

**1. 账户映射配置** (`smart_engine/config/multi_account_mapping.json`)：
```json
{
  "mappings": {
    "****************": {
      "virtual_name": "测试广告主B",
      "real_name": "希赢贸易",
      "account_mapper_config": {
        "account_mapping": {
          "real_account": {"name": "希赢贸易", "id": "****************"},
          "virtual_account": {"name": "测试广告主B", "id": "VIRTUAL_B"}
        }
      }
    }
  }
}
```

**2. 单账户虚拟数据配置** (`configs/accounts/metrics_{account_id}.json`)：
```json
{
  "Account Name": "测试广告主B",
  "Account ID": "VIRTUAL_B",
  "Show Count": 62445,
  "Click Count": 3122,
  "Convert Count": 42,
  "Stat Cost": 1460.56,
  "account_mapping": {
    "real_account": {"name": "希赢贸易", "id": "****************"},
    "virtual_account": {"name": "测试广告主B", "id": "VIRTUAL_B"}
  }
}
```

#### **实现逻辑**（基于当前架构补充）：
```python
def handle_account_list_response(self, flow):
    """处理多账户列表响应 - 补充数据虚拟化聚合"""
    # 1. 替换账户名称（已实现）
    self._replace_account_names(flow)

    # 2. 聚合虚拟数据（新增功能）
    ad_type = self._identify_ad_type(flow.request)  # feed/search/all
    aggregated_data = self._aggregate_virtual_data(ad_type)

    # 3. 替换API响应中的投放数据
    self._replace_performance_data(flow, aggregated_data)

def _aggregate_virtual_data(self, ad_type):
    """从各账户配置文件聚合虚拟数据"""
    total_cost = 0
    total_show = 0
    total_convert = 0

    for account_id in self.mappings:
        config = self.config_manager.get_account_config(account_id)
        if config:
            total_cost += config.get('Stat Cost', 0)
            total_show += config.get('Show Count', 0)
            total_convert += config.get('Convert Count', 0)

    return {"cost": total_cost, "show": total_show, "convert": total_convert}
```

#### **当前架构优势**：
- ✅ **架构优雅**：完全复用现有 `SmartAPIEngine` 和 `AccountMapper`
- ✅ **单一数据源**：每个账户只有一个配置文件，天然保证一致性
- ✅ **职责分离**：入口代理只处理关键节点，数据处理交给专业组件
- ✅ **零重复开发**：不需要重新实现数据虚拟化逻辑
- ✅ **扩展性极佳**：添加新账户只需新增一个配置文件
- ✅ **维护简单**：修改账户数据只需修改对应配置文件
- ✅ **配置独立**：符合微服务配置管理理念

## 📊 数据配置

### **当前配置数据**（`metrics.json`）：
```json
{
  "Stat Cost": 1515.10,
  "Show Count": 64794,
  "Convert Count": 44,
  "Conversion Cost": 34.43,
  "CTR (%)": 0.07,
  "Conversion Rate (%)": 0.07
}
```

### **虚拟账户映射示例**：
```json
{
  "account_mapping": {
    "****************": {
      "real_name": "希赢贸易",
      "virtual_name": "虚拟账户A",
      "virtual_id": "VIRTUAL_A"
    },
    "****************": {
      "real_name": "广州希赢贸易",
      "virtual_name": "虚拟账户B",
      "virtual_id": "VIRTUAL_B"
    }
  }
}
```

## 🎯 最终目标

实现完整的账户信息和数据虚拟化系统：
1. **多账户列表页面**：显示虚拟账户信息和虚拟概览数据
2. **单账户详情页面**：显示对应虚拟账户的虚拟数据
3. **虚拟数据逻辑一致性**：确保所有虚拟数据在数学上完全一致
4. **用户体验**：用户看到的是逻辑合理的虚拟账户和虚拟投放数据

## 📝 关键约束

1. **文件长度限制**：每个代码文件不超过200行
2. **虚拟数据一致性**：严格保证虚拟数据数学逻辑正确
3. **业务真实性**：虚拟数据分布符合真实业务场景
4. **系统稳定性**：不影响现有功能的正常运行

## 📅 文档信息

- **创建时间**：2025-06-17
- **最后更新**：2025-06-17
- **版本**：v1.1
- **状态**：设计方案优化中
- **负责人**：开发团队
- **相关文件**：
  - `smart_engine/engine/balance_handler.py`
  - `metrics.json`
  - `docs/technical/multi-account-architecture.md`

## 📝 版本历史

### v1.1 (2025-06-17)
- ✅ 添加设计方案优化章节
- ✅ 提出预配置数据映射方案
- ✅ 简化技术实现逻辑
- ✅ 充分利用系统已有配置驱动能力

### v1.0 (2025-06-17)
- ✅ 初始需求文档创建
- ✅ 定义核心功能需求
- ✅ 明确数据关系逻辑
- ✅ 记录已完成和待完成功能
