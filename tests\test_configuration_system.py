#!/usr/bin/env python3
"""
🧪 配置系统测试模块
测试metrics.json加载/保存、配置验证、默认值处理等配置管理功能

从原有拆分文件迁移的测试：
- test_regression_suite.py: test_01_config_loading
- test_missing_coverage.py: test_02_config_loading_edge_cases
- test_regression_suite_extended.py: test_13_integration_config_loading
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.utils import ad_utils

class TestConfigurationSystem(unittest.TestCase):
    """配置系统测试套件"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_basic_config_loading(self):
        """测试基础配置加载功能"""
        print("\n🧪 测试基础配置加载")
        
        # 测试正常加载
        metrics = ad_utils.load_metrics_from_file('metrics.json')
        self.assertEqual(metrics['Account Name'], 'WH-测试公司')
        self.assertEqual(metrics['Show Count'], 50000)
        self.assertEqual(metrics['CTR (%)'], 5.0)
        
        # 验证账户映射数据
        if 'account_mapping' in metrics:
            self.assertIn('real_account', metrics['account_mapping'])
            self.assertIn('virtual_account', metrics['account_mapping'])
        
        print("✅ 基础配置加载测试通过")

    def test_config_file_not_exists(self):
        """测试配置文件不存在的处理"""
        print("\n🧪 测试配置文件不存在处理")
        
        # 测试文件不存在时的默认处理
        metrics_empty = ad_utils.load_metrics_from_file('nonexistent.json')
        self.assertEqual(metrics_empty, {})
        
        # 测试空文件名
        metrics_empty_name = ad_utils.load_metrics_from_file('')
        self.assertEqual(metrics_empty_name, {})
        
        print("✅ 配置文件不存在处理测试通过")

    def test_config_save_and_load_cycle(self):
        """测试配置保存和加载的完整周期"""
        print("\n🧪 测试配置保存和加载周期")
        
        # 创建测试数据
        test_data = {
            'test_key': 'test_value',
            'numeric_value': 123.45,
            'boolean_value': True,
            'nested_object': {
                'inner_key': 'inner_value',
                'inner_number': 67.89
            },
            'array_value': [1, 2, 3, 'string_in_array']
        }
        
        # 保存配置
        ad_utils.save_metrics_to_file(test_data, 'test_save.json')
        self.assertTrue(os.path.exists('test_save.json'))
        
        # 加载配置
        loaded_data = ad_utils.load_metrics_from_file('test_save.json')
        
        # 验证数据完整性
        self.assertEqual(loaded_data['test_key'], 'test_value')
        self.assertEqual(loaded_data['numeric_value'], 123.45)
        self.assertEqual(loaded_data['boolean_value'], True)
        self.assertEqual(loaded_data['nested_object']['inner_key'], 'inner_value')
        self.assertEqual(loaded_data['nested_object']['inner_number'], 67.89)
        self.assertEqual(loaded_data['array_value'], [1, 2, 3, 'string_in_array'])
        
        print("✅ 配置保存和加载周期测试通过")

    def test_config_loading_edge_cases(self):
        """测试配置加载的边界情况"""
        print("\n🧪 测试配置加载边界情况")
        
        # 测试空JSON文件
        with open('empty.json', 'w', encoding='utf-8') as f:
            f.write('{}')
        
        empty_config = ad_utils.load_metrics_from_file('empty.json')
        self.assertEqual(empty_config, {})
        
        # 测试无效JSON文件
        with open('invalid.json', 'w', encoding='utf-8') as f:
            f.write('invalid json content')

        # 测试无效JSON的处理 - 可能抛出异常或返回空字典
        try:
            invalid_config = ad_utils.load_metrics_from_file('invalid.json')
            # 如果没有抛出异常，应该返回空字典
            self.assertEqual(invalid_config, {})
        except Exception:
            # 如果抛出异常，这也是合理的行为
            pass
        
        # 测试包含特殊字符的JSON
        special_data = {
            'chinese_text': '中文测试',
            'emoji': '🎯📊',
            'special_chars': '!@#$%^&*()',
            'unicode': '\u4e2d\u6587'
        }
        
        ad_utils.save_metrics_to_file(special_data, 'special.json')
        loaded_special = ad_utils.load_metrics_from_file('special.json')
        self.assertEqual(loaded_special['chinese_text'], '中文测试')
        self.assertEqual(loaded_special['emoji'], '🎯📊')
        
        print("✅ 配置加载边界情况测试通过")

    def test_config_file_permissions(self):
        """测试配置文件权限相关问题"""
        print("\n🧪 测试配置文件权限")
        
        # 创建测试文件
        test_data = {'permission_test': 'value'}
        ad_utils.save_metrics_to_file(test_data, 'permission_test.json')
        
        # 验证文件可读
        loaded_data = ad_utils.load_metrics_from_file('permission_test.json')
        self.assertEqual(loaded_data['permission_test'], 'value')
        
        # 在Windows环境下，权限测试可能不适用，所以这里主要测试基本的读写功能
        print("✅ 配置文件权限测试通过")

    def test_config_backup_mechanism(self):
        """测试配置备份机制"""
        print("\n🧪 测试配置备份机制")
        
        # 创建原始配置
        original_data = {'version': 1, 'data': 'original'}
        ad_utils.save_metrics_to_file(original_data, 'backup_test.json')
        
        # 模拟配置更新
        updated_data = {'version': 2, 'data': 'updated'}
        ad_utils.save_metrics_to_file(updated_data, 'backup_test.json')
        
        # 验证更新后的配置
        current_data = ad_utils.load_metrics_from_file('backup_test.json')
        self.assertEqual(current_data['version'], 2)
        self.assertEqual(current_data['data'], 'updated')
        
        print("✅ 配置备份机制测试通过")



def run_configuration_tests():
    """运行配置系统测试"""
    print("🚀 开始配置系统测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestConfigurationSystem)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 配置系统测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有配置系统测试通过！")
    else:
        print("❌ 部分配置系统测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_configuration_tests()
