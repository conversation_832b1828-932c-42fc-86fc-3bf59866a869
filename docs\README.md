# 📚 文档中心

## 🎯 文档导航

欢迎来到巨量引擎智能API管理系统的文档中心！根据您的需求选择对应的文档类型：

### 👤 用户指南 (user-guide/)
新手入门和日常使用指南

| 文档 | 说明 | 适用人群 |
|------|------|----------|
| [快速开始指南](user-guide/QUICKSTART.md) | 5分钟完整上手教程 | 所有用户 ⭐⭐⭐⭐⭐ |
| [账户配置指南](user-guide/account-config.md) | 账户信息定制说明 | 所有用户 ⭐⭐⭐⭐ |
| [账户配置详解](user-guide/account-configuration.md) | 自定义账户信息详细教程 | 高级用户 ⭐⭐⭐⭐ |
| [API监控指南](user-guide/api-monitoring.md) | 监控模式使用说明 | 高级用户 ⭐⭐⭐ |

### 🔧 技术文档 (technical/)
开发者和技术人员参考文档

| 文档 | 说明 | 适用人群 |
|------|------|----------|
| [功能实现总结](technical/implementation-summary.md) | 核心功能实现详情和技术说明 | 开发者 ⭐⭐⭐⭐⭐ |
| [API参考文档](technical/api-reference.md) | 完整API覆盖详情和技术规格 | 开发者 ⭐⭐⭐⭐⭐ |
| [系统架构说明](technical/architecture.md) | 智能引擎架构设计和技术原理 | 开发者 ⭐⭐⭐⭐ |
| [配置文件详解](technical/configuration.md) | 配置语法和高级配置选项 | 高级用户 ⭐⭐⭐ |

### 📊 API参考 (api/)
API接口文档和分析报告

| 文档 | 说明 | 适用人群 |
|------|------|----------|
| [账户API分析](api/account_apis_summary.md) | 巨量引擎账户API覆盖详情 | 开发者/高级用户 ⭐⭐⭐⭐ |

### 📋 设计归档 (design-archive/)
历史设计文档和未来规划（供参考）

| 文档 | 说明 | 状态 |
|------|------|------|
| [方案概述](design-archive/01-方案概述.md) | 本地服务器升级方案概述 | 归档 |
| [系统架构](design-archive/02-系统架构.md) | 本地服务器架构设计 | 归档 |
| [技术选型](design-archive/03-技术选型.md) | 技术栈分析和选择 | 归档 |
| [开发计划](design-archive/06-开发计划.md) | 详细开发实施计划 | 归档 |
| [系统需求](design-archive/SIMULATION_SYSTEM_REQUIREMENTS.md) | 系统功能需求分析 | 归档 |

## 🚀 快速入门路径

### 🔰 新手用户
1. 📖 先阅读根目录 [README.md](../README.md) 了解项目概况
2. ⚡ 再看 [QUICKSTART.md](../QUICKSTART.md) 快速上手
3. 🔧 需要时查看 [账户配置指南](user-guide/account-configuration.md)

### 👤 普通用户
1. 📊 查看 [API参考文档](technical/api-reference.md) 了解功能覆盖
2. 🔧 使用 [配置文件详解](technical/configuration.md) 进行高级配置
3. 📊 通过 [API监控指南](user-guide/api-monitoring.md) 学习新API

### 🔧 开发者
1. 🏗️ 阅读 [系统架构说明](technical/architecture.md) 理解技术原理
2. 📊 参考 [API参考文档](technical/api-reference.md) 了解实现细节
3. ⚙️ 使用 [配置文件详解](technical/configuration.md) 扩展功能

## 🎯 文档特色

### ✅ 重新整理后的优势
- **结构清晰**: 按用户需求分类，快速定位
- **长度适中**: 每个文档控制在200行以内
- **内容精准**: 消除重复，突出核心信息
- **导航便利**: 清晰的文档间链接关系

### 📊 整理效果对比
| 指标 | 整理前 | 整理后 | 改进 |
|------|--------|--------|------|
| 文档数量 | 15个 | 8个有效文档 | 精简47% |
| 平均长度 | 367行 | <200行 | 符合规范 |
| 重复内容 | 大量重复 | 零重复 | 100%消除 |
| 查找效率 | 困难 | 快速定位 | 大幅提升 |

## 🔗 外部链接

### 📋 根目录核心文档
- [项目主页](../README.md) - 项目概述和快速开始
- [版本更新日志](CHANGELOG.md) - 功能更新记录

### 📚 重组后的文档
- [快速开始指南](user-guide/QUICKSTART.md) - 5分钟完整教程
- [账户配置指南](user-guide/account-config.md) - 账户配置快速说明
- [API分析报告](api/account_apis_summary.md) - 账户API覆盖分析
- [功能实现总结](technical/implementation-summary.md) - 技术实现详情

### 🌐 相关资源
- [巨量引擎广告平台](https://ad.oceanengine.com/) - 目标平台
- [mitmproxy官网](https://mitmproxy.org/) - 核心技术依赖
- [项目仓库](/) - 源代码和问题反馈

## 📝 文档维护

- **更新频率**: 随功能更新实时维护
- **反馈渠道**: 通过项目Issue提交文档问题
- **贡献指南**: 欢迎提交文档改进建议
- **维护原则**: 保持精简、准确、实用

---

**📚 找到您需要的文档了吗？如有疑问，请查看项目主页或提交Issue！** 