#!/usr/bin/env python3
"""
📥 多账户请求处理器
从 multi_account_entry_proxy.py 拆分出来的请求处理逻辑
保持文件长度在200行以内
"""

from typing import Dict, Any, Optional
from mitmproxy import http
from smart_engine.utils.debug_logger import info_log, debug_log, error_log, process_log


class MultiAccountRequestHandler:
    """多账户请求处理器"""
    
    def __init__(self, mappings: Dict[str, Dict[str, Any]], url_handler, config_handler):
        """
        初始化请求处理器
        
        Args:
            mappings: 账户映射配置
            url_handler: URL处理器
            config_handler: 配置处理器
        """
        self.mappings = mappings
        self.url_handler = url_handler
        self.config_handler = config_handler
        self.current_account_id = None
    
    def handle_account_redirect_request(self, flow: http.HTTPFlow) -> bool:
        """
        处理账户跳转请求，映射虚拟ID到真实ID并配置AccountMapper
        
        Args:
            flow: HTTP流对象
            
        Returns:
            bool: 是否处理了请求
        """
        try:
            # 🔥 关键修改：只检测真正的账户跳转，而不是所有包含账户ID的请求
            url = flow.request.pretty_url
            
            # 只处理特定的账户跳转URL模式
            if not self.url_handler.is_account_switch_url(url):
                return False

            # 从URL中提取账户ID
            account_id = self.url_handler.extract_account_id_from_url(url)
            if not account_id:
                return False
                
            # 检查是否是我们管理的账户
            if account_id not in self.mappings:
                return False
            
            # 🔥 新增：检查是否已经是当前账户，避免重复配置
            if (hasattr(self, 'current_account_id') and 
                self.current_account_id == account_id):
                info_log(f"ℹ️ [请求处理器] 账户 {account_id} 已是当前账户，跳过配置")
                return False
            
            # 配置AccountMapper
            self.config_handler.setup_account_mapper_for_account(account_id)
            
            # 记录当前账户
            self.current_account_id = account_id
            
            process_log(f"🔄 [请求处理器] 账户跳转处理: {self.mappings[account_id]['virtual_name']} → {account_id}")
            return True
            
        except Exception as e:
            error_log(f"❌ [请求处理器] 账户跳转处理失败: {e}")
            
        return False
    
    def handle_account_switch_request(self, flow: http.HTTPFlow) -> bool:
        """处理账户跳转请求"""
        try:
            # 提取账户ID
            account_id = self.url_handler.extract_account_id_from_url(flow.request.url)
            if not account_id or account_id not in self.mappings:
                return False
            
            # 检查是否需要切换账户
            if (hasattr(self, 'current_account_id') and 
                self.current_account_id == account_id):
                debug_log(f"ℹ️ [请求处理器] 账户 {account_id} 已是当前账户，跳过配置")
                return False
            
            # 执行账户配置
            if self.config_handler.configure_account(account_id):
                # 设置当前账户
                self.current_account_id = account_id
                
                info_log(f"🔄 [请求处理器] 账户跳转处理: {self.mappings[account_id]['virtual_name']} → {account_id}")
                return True
                
        except Exception as e:
            error_log(f"❌ [请求处理器] 账户跳转处理失败: {e}")
            
        return False
    
    def process_request(self, flow: http.HTTPFlow) -> bool:
        """
        处理HTTP请求
        
        Args:
            flow: HTTP流对象
            
        Returns:
            bool: 是否处理了请求
        """
        # 只处理账户跳转请求
        return self.handle_account_redirect_request(flow)
    
    def get_current_account_id(self) -> Optional[str]:
        """获取当前账户ID"""
        return self.current_account_id
    
    def set_current_account_id(self, account_id: str):
        """设置当前账户ID"""
        self.current_account_id = account_id
