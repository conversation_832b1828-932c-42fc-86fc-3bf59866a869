# 📊 数据模型

## 🎯 数据模型设计原则

### 核心设计理念
1. **🎯 简单高效**：避免过度规范化，优化查询性能
2. **🔄 扩展友好**：预留扩展字段，支持未来功能增强
3. **📊 数据一致性**：确保关联数据的完整性和一致性
4. **⚡ 读写优化**：针对读多写少的场景优化表结构
5. **🛡️ 数据安全**：敏感数据加密存储，操作日志完整

### 技术约束
- **数据库**：SQLite 3.x，单文件部署
- **数据量**：预期10万条API日志，1000条规则
- **并发**：单用户使用，无高并发要求
- **备份**：支持文件级备份和恢复

## 🗄️ 核心数据表设计

### 1. **📊 指标配置表 (metrics_config)**

#### 表结构设计
```sql
CREATE TABLE metrics_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_name TEXT NOT NULL DEFAULT 'default',
    
    -- 基础指标数据
    show_count INTEGER NOT NULL DEFAULT 0,
    click_count INTEGER NOT NULL DEFAULT 0,
    convert_count INTEGER NOT NULL DEFAULT 0,
    stat_cost REAL NOT NULL DEFAULT 0.0,
    
    -- 计算指标 (运行时计算，不存储)
    -- ctr = (click_count / show_count) * 100
    -- conversion_rate = (convert_count / click_count) * 100
    -- cpm = (stat_cost / show_count) * 1000
    -- cpc = stat_cost / click_count
    -- conversion_cost = stat_cost / convert_count
    
    -- 环比数据配置
    comparison_data TEXT, -- JSON格式存储环比数据
    
    -- 账户配置
    account_balance REAL DEFAULT 25000.0,
    daily_budget REAL DEFAULT 10000.0,
    
    -- 状态配置
    project_status TEXT DEFAULT '启用中',
    promotion_status TEXT DEFAULT '投放中',
    material_status TEXT DEFAULT '正常',
    
    -- 元数据
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(config_name)
);
```

#### 字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `id` | INTEGER | 主键ID | 1 |
| `config_name` | TEXT | 配置名称 | "default", "campaign_a" |
| `show_count` | INTEGER | 广告展示量 | 50000 |
| `click_count` | INTEGER | 广告点击量 | 2500 |
| `convert_count` | INTEGER | 转化量 | 500 |
| `stat_cost` | REAL | 广告消耗金额 | 5000.0 |
| `comparison_data` | TEXT | 环比数据JSON | {"ctr": {"ratio": 0.12}} |
| `account_balance` | REAL | 账户余额 | 25000.0 |
| `project_status` | TEXT | 项目状态 | "启用中" |

#### 示例数据
```sql
INSERT INTO metrics_config (
    config_name, show_count, click_count, convert_count, stat_cost,
    comparison_data, account_balance, project_status
) VALUES (
    'default', 50000, 2500, 500, 5000.0,
    '{"ctr":{"ratio":0.12,"ratio_str":"+12%"},"conversion_rate":{"ratio":0.08,"ratio_str":"+8%"}}',
    25000.0, '启用中'
);
```

### 2. **📋 API规则表 (api_rules)**

#### 表结构设计
```sql
CREATE TABLE api_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_name TEXT NOT NULL,
    
    -- URL匹配规则
    url_pattern TEXT NOT NULL,
    method TEXT DEFAULT 'GET', -- GET, POST, PUT, DELETE
    
    -- API分类
    api_type TEXT NOT NULL, -- project, promotion, statistics, material
    api_category TEXT, -- list, detail, search, create, update
    
    -- 处理规则配置
    field_mappings TEXT NOT NULL, -- JSON格式字段映射规则
    conditions TEXT, -- JSON格式条件配置
    actions TEXT, -- JSON格式处理动作
    
    -- 规则优先级和状态
    priority INTEGER DEFAULT 100, -- 数值越小优先级越高
    is_enabled BOOLEAN DEFAULT TRUE,
    
    -- 性能和统计
    match_count INTEGER DEFAULT 0, -- 匹配次数统计
    success_count INTEGER DEFAULT 0, -- 成功处理次数
    last_matched_at TIMESTAMP, -- 最后匹配时间
    avg_process_time_ms REAL DEFAULT 0, -- 平均处理时间(毫秒)
    
    -- 元数据
    description TEXT, -- 规则描述
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    UNIQUE(rule_name)
);

-- 创建索引优化查询性能
CREATE INDEX idx_api_rules_url_pattern ON api_rules(url_pattern);
CREATE INDEX idx_api_rules_api_type ON api_rules(api_type);
CREATE INDEX idx_api_rules_priority ON api_rules(priority);
CREATE INDEX idx_api_rules_enabled ON api_rules(is_enabled);
```

#### 字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `rule_name` | TEXT | 规则名称 | "项目列表API处理规则" |
| `url_pattern` | TEXT | URL匹配模式 | "/api/promotion/project/list" |
| `api_type` | TEXT | API类型 | "project", "promotion" |
| `field_mappings` | TEXT | 字段映射规则JSON | 详见下方示例 |
| `priority` | INTEGER | 优先级(1-1000) | 100 |
| `match_count` | INTEGER | 累计匹配次数 | 1250 |

#### 规则配置JSON示例

##### **字段映射规则 (field_mappings)**
```json
{
  "status_mappings": {
    "promotion_status_first_name": {
      "target_value": "启用中",
      "condition": "always"
    },
    "project_status_name": {
      "target_value": "投放中",
      "condition": "if_not_empty"
    }
  },
  "data_injections": {
    "metrics": {
      "source": "metrics_config",
      "fields": {
        "show_cnt": "show_count",
        "click_cnt": "click_count",
        "convert_cnt": "convert_count",
        "stat_cost": "stat_cost"
      }
    }
  },
  "clear_fields": [
    "project_status_second",
    "promotion_status_second_name",
    "diagnosis_interfere_status"
  ]
}
```

##### **条件配置 (conditions)**
```json
{
  "match_conditions": [
    {
      "type": "url_contains",
      "value": "/promotion/project/",
      "operator": "and"
    },
    {
      "type": "response_contains_field",
      "value": "project_status_first_name",
      "operator": "and"
    }
  ],
  "execution_conditions": [
    {
      "type": "response_not_empty",
      "field": "data"
    }
  ]
}
```

##### **处理动作 (actions)**
```json
{
  "pre_actions": [
    "backup_original_response",
    "validate_response_structure"
  ],
  "main_actions": [
    "apply_status_mappings",
    "inject_metrics_data",
    "clear_specified_fields"
  ],
  "post_actions": [
    "validate_modified_response",
    "log_processing_result"
  ]
}
```

### 3. **📋 处理日志表 (processing_logs)**

#### 表结构设计
```sql
CREATE TABLE processing_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- 请求信息
    url TEXT NOT NULL,
    method TEXT DEFAULT 'GET',
    request_headers TEXT, -- JSON格式
    
    -- 处理结果
    api_type TEXT,
    rule_id INTEGER, -- 关联api_rules表
    processing_result TEXT NOT NULL, -- success, failed, skipped
    
    -- 性能指标
    processing_time_ms REAL NOT NULL,
    response_size_bytes INTEGER,
    
    -- 错误信息
    error_code TEXT,
    error_message TEXT,
    stack_trace TEXT,
    
    -- 元数据
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_agent TEXT,
    client_ip TEXT DEFAULT '127.0.0.1',
    
    -- 外键约束
    FOREIGN KEY(rule_id) REFERENCES api_rules(id)
);

-- 创建索引优化查询
CREATE INDEX idx_processing_logs_timestamp ON processing_logs(timestamp);
CREATE INDEX idx_processing_logs_url ON processing_logs(url);
CREATE INDEX idx_processing_logs_result ON processing_logs(processing_result);
CREATE INDEX idx_processing_logs_api_type ON processing_logs(api_type);
```

#### 字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| `url` | TEXT | 请求URL | "/api/promotion/project/list" |
| `api_type` | TEXT | 识别的API类型 | "project" |
| `processing_result` | TEXT | 处理结果 | "success", "failed", "skipped" |
| `processing_time_ms` | REAL | 处理耗时(毫秒) | 15.6 |
| `error_message` | TEXT | 错误信息 | "JSON解析失败" |

### 4. **🔧 系统配置表 (system_config)**

#### 表结构设计
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key TEXT NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    config_type TEXT DEFAULT 'string', -- string, number, boolean, json
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE, -- 是否为敏感配置
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(config_key)
);
```

#### 预置配置项
```sql
INSERT INTO system_config (config_key, config_value, config_type, description) VALUES
('proxy_port', '8080', 'number', 'mitmproxy代理端口'),
('web_port', '5000', 'number', 'Web管理界面端口'),
('log_level', 'INFO', 'string', '日志级别'),
('auto_discovery', 'true', 'boolean', '是否自动发现新API'),
('cache_enabled', 'true', 'boolean', '是否启用缓存'),
('max_log_days', '30', 'number', '日志保留天数'),
('performance_threshold_ms', '100', 'number', '性能告警阈值(毫秒)');
```

## 📄 配置文件结构

### 1. **主配置文件 (config/main.json)**

```json
{
  "system": {
    "name": "巨量引擎数据管理平台",
    "version": "1.0.0",
    "proxy_port": 8080,
    "web_port": 5000,
    "log_level": "INFO",
    "debug_mode": false
  },
  "database": {
    "path": "data/app.db",
    "backup_enabled": true,
    "backup_interval_hours": 24,
    "max_backup_files": 7
  },
  "performance": {
    "cache_enabled": true,
    "cache_ttl_seconds": 300,
    "max_concurrent_requests": 100,
    "processing_timeout_ms": 5000
  },
  "security": {
    "api_key_enabled": false,
    "cors_enabled": true,
    "allowed_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]
  }
}
```

### 2. **URL模式配置 (config/url_patterns.json)**

```json
{
  "patterns": {
    "project": {
      "exact_matches": [
        "/api/promotion/project/list",
        "/superior/api/promote/project/list"
      ],
      "regex_patterns": [
        "/api/promotion/project/\\d+",
        "/superior/api/promote/project/\\d+"
      ],
      "contains_patterns": [
        "/project/",
        "/promote/project/"
      ]
    },
    "promotion": {
      "exact_matches": [
        "/api/promotion/ads/list",
        "/api/promotion/ads/attribute/list"
      ],
      "regex_patterns": [
        "/api/promotion/ads/detail/\\d+",
        "/promotion/materials/list"
      ],
      "contains_patterns": [
        "/promotion/ads/",
        "/promotion/materials/"
      ]
    },
    "statistics": {
      "exact_matches": [
        "/superior/api/agw/statistics_sophonx/statQuery"
      ],
      "contains_patterns": [
        "/statistics/",
        "/metrics/",
        "statQuery"
      ]
    }
  },
  "content_keywords": {
    "status_fields": [
      "project_status_first_name",
      "promotion_status_first_name",
      "project_status_name",
      "promotion_status_name",
      "material_status_name"
    ],
    "metrics_fields": [
      "show_cnt",
      "click_cnt",
      "convert_cnt",
      "stat_cost",
      "ctr",
      "conversion_rate",
      "cpm_platform",
      "cpc_platform"
    ]
  }
}
```

### 3. **默认规则配置 (config/default_rules.json)**

```json
{
  "global_status_mappings": {
    "未投放": "启用中",
    "已暂停": "启用中",
    "暂停": "投放中",
    "不在投放时段": "投放中",
    "新建审核中": "投放中",
    "广告已暂停": "投放中",
    "审核中": "投放中",
    "审核不通过": "投放中"
  },
  "default_clear_fields": [
    "project_status_second",
    "project_status_second_name",
    "promotion_status_second",
    "promotion_status_second_name", 
    "diagnosis_interfere_status"
  ],
  "metrics_field_mappings": {
    "show_cnt": "show_count",
    "click_cnt": "click_count",
    "convert_cnt": "convert_count",
    "stat_cost": "stat_cost"
  }
}
```

## 🔄 数据操作接口

### 1. **指标配置操作**

#### **获取当前配置**
```python
def get_current_metrics_config():
    """获取当前激活的指标配置"""
    query = """
    SELECT * FROM metrics_config 
    WHERE is_active = TRUE 
    ORDER BY updated_at DESC 
    LIMIT 1
    """
    return db.execute(query).fetchone()
```

#### **更新指标配置**
```python
def update_metrics_config(config_data):
    """更新指标配置"""
    query = """
    UPDATE metrics_config 
    SET show_count = ?, click_count = ?, convert_count = ?, 
        stat_cost = ?, comparison_data = ?, updated_at = CURRENT_TIMESTAMP
    WHERE config_name = ?
    """
    return db.execute(query, (
        config_data['show_count'],
        config_data['click_count'], 
        config_data['convert_count'],
        config_data['stat_cost'],
        json.dumps(config_data['comparison_data']),
        config_data.get('config_name', 'default')
    ))
```

### 2. **API规则操作**

#### **根据URL匹配规则**
```python
def match_api_rule(url, method='GET'):
    """根据URL匹配对应的处理规则"""
    query = """
    SELECT * FROM api_rules 
    WHERE is_enabled = TRUE 
    AND (url_pattern = ? OR ? LIKE '%' || url_pattern || '%')
    ORDER BY priority ASC, LENGTH(url_pattern) DESC
    LIMIT 1
    """
    return db.execute(query, (url, url)).fetchone()
```

#### **更新规则统计信息**
```python
def update_rule_statistics(rule_id, processing_time_ms, success=True):
    """更新规则的统计信息"""
    query = """
    UPDATE api_rules 
    SET match_count = match_count + 1,
        success_count = success_count + ?,
        last_matched_at = CURRENT_TIMESTAMP,
        avg_process_time_ms = (avg_process_time_ms * match_count + ?) / (match_count + 1)
    WHERE id = ?
    """
    return db.execute(query, (1 if success else 0, processing_time_ms, rule_id))
```

### 3. **日志记录操作**

#### **记录处理日志**
```python
def log_api_processing(url, api_type, rule_id, result, processing_time_ms, error_msg=None):
    """记录API处理日志"""
    query = """
    INSERT INTO processing_logs 
    (url, api_type, rule_id, processing_result, processing_time_ms, error_message)
    VALUES (?, ?, ?, ?, ?, ?)
    """
    return db.execute(query, (url, api_type, rule_id, result, processing_time_ms, error_msg))
```

#### **查询处理统计**
```python
def get_processing_statistics(time_range_hours=24):
    """获取指定时间范围内的处理统计"""
    query = """
    SELECT 
        api_type,
        COUNT(*) as total_count,
        SUM(CASE WHEN processing_result = 'success' THEN 1 ELSE 0 END) as success_count,
        AVG(processing_time_ms) as avg_processing_time,
        MAX(processing_time_ms) as max_processing_time
    FROM processing_logs 
    WHERE timestamp >= datetime('now', '-{} hours')
    GROUP BY api_type
    """.format(time_range_hours)
    return db.execute(query).fetchall()
```

## 📊 数据流转模型

### 🔄 配置数据流转

```mermaid
flowchart TD
    A[Web界面修改] --> B[验证数据格式]
    B --> C[更新数据库]
    C --> D[计算派生指标]
    D --> E[广播配置更新]
    E --> F[API处理引擎接收]
    F --> G[更新内存缓存]
    G --> H[生效新配置]
    
    C --> I[备份旧配置]
    I --> J[记录变更日志]
```

### 📋 规则数据流转

```mermaid
flowchart TD
    A[发现新API] --> B[URL模式匹配]
    B --> C{匹配成功?}
    C -->|是| D[应用已有规则]
    C -->|否| E[内容分析识别]
    E --> F[生成建议规则]
    F --> G[用户确认配置]
    G --> H[保存新规则]
    H --> I[规则立即生效]
    
    D --> J[更新匹配统计]
    I --> J
    J --> K[记录处理日志]
```

### 📈 监控数据流转

```mermaid
flowchart TD
    A[API请求处理] --> B[记录开始时间]
    B --> C[执行处理逻辑]
    C --> D[记录结束时间]
    D --> E[计算处理耗时]
    E --> F[更新性能指标]
    F --> G[写入处理日志]
    G --> H[更新规则统计]
    H --> I[检查性能阈值]
    I --> J{超过阈值?}
    J -->|是| K[触发告警]
    J -->|否| L[正常结束]
```

## 🔧 数据维护策略

### 🧹 数据清理

#### **日志清理策略**
```sql
-- 清理30天前的处理日志
DELETE FROM processing_logs 
WHERE timestamp < datetime('now', '-30 days');

-- 清理失败率过高的无效规则
UPDATE api_rules 
SET is_enabled = FALSE 
WHERE success_count * 1.0 / match_count < 0.5 
AND match_count > 100;
```

#### **性能优化**
```sql
-- 重建索引优化查询性能
REINDEX;

-- 收集表统计信息
ANALYZE;

-- 清理数据库碎片
VACUUM;
```

### 💾 数据备份

#### **自动备份策略**
```python
def backup_database():
    """数据库自动备份"""
    backup_path = f"backup/app_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    shutil.copy2('data/app.db', backup_path)
    
    # 清理过期备份文件
    cleanup_old_backups(max_files=7)
```

#### **配置导出导入**
```python
def export_config(config_name='default'):
    """导出配置数据"""
    config_data = {
        'metrics': get_metrics_config(config_name),
        'rules': get_all_api_rules(),
        'system': get_system_config(),
        'export_time': datetime.now().isoformat()
    }
    return json.dumps(config_data, indent=2, ensure_ascii=False)

def import_config(config_json):
    """导入配置数据"""
    config_data = json.loads(config_json)
    
    # 导入指标配置
    import_metrics_config(config_data['metrics'])
    
    # 导入规则配置  
    import_api_rules(config_data['rules'])
    
    # 导入系统配置
    import_system_config(config_data['system'])
```

这个数据模型设计确保了**数据的完整性**、**查询的高效性**和**系统的可维护性**，为整个平台提供了坚实的数据基础。 