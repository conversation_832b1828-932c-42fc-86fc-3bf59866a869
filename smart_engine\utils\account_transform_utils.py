#!/usr/bin/env python3
"""
🔧 账户转换工具类
从AccountMapper中拆分出来的转换工具方法

🎯 包含功能：
- 字段判断工具
- 值转换工具  
- 数据处理工具
- 安全编码处理
"""

import json
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, parse_qs, urlencode
from smart_engine.utils.account_encoding_utils import AccountEncodingUtils


class AccountTransformUtils:
    """账户转换工具类"""
    
    def __init__(self, real_account: Dict[str, Any], virtual_account: Dict[str, Any]):
        """初始化转换工具
        
        Args:
            real_account: 真实账户信息
            virtual_account: 虚拟账户信息
        """
        self.real_account = real_account
        self.virtual_account = virtual_account
        self.encoding_utils = AccountEncodingUtils(real_account, virtual_account)
    
    def transform_value_virtual_to_real(self, value: Any) -> Any:
        """转换值：虚拟 → 真实 - 使用统一的虚拟ID映射"""
        if not isinstance(value, str):
            return value

        # 🔧 修复：使用统一的虚拟ID管理器获取所有映射
        try:
            from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
            from smart_engine.utils.virtual_id_manager import VirtualIdManager

            temp_proxy = MultiAccountEntryProxy()
            all_mappings = VirtualIdManager.get_all_standard_mappings(temp_proxy.mappings)

            # 检查所有虚拟ID映射
            for virtual_id, mapping in all_mappings.items():
                real_id = mapping['real_id']
                virtual_name = mapping['virtual_name']
                real_name = mapping['real_name']

                # ID转换
                if virtual_id and value == virtual_id:
                    return real_id

                # 名称转换
                if virtual_name and value == virtual_name:
                    return real_name

        except Exception:
            # 回退到单账户模式（仅作为最后手段）
            virtual_id = self.virtual_account.get('id', '')
            real_id = self.real_account.get('id', '')
            if virtual_id and value == virtual_id:
                return real_id

            virtual_name = self.virtual_account.get('name', '')
            real_name = self.real_account.get('name', '')
            if virtual_name and value == virtual_name:
                return real_name

        return value
    
    def transform_value_real_to_virtual(self, value: Any) -> Any:
        """转换值：真实 → 虚拟 - 使用统一的虚拟ID映射"""
        if not isinstance(value, str):
            return value

        # 🔧 修复：使用统一的虚拟ID管理器获取所有映射
        try:
            from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
            from smart_engine.utils.virtual_id_manager import VirtualIdManager

            temp_proxy = MultiAccountEntryProxy()
            all_mappings = VirtualIdManager.get_all_standard_mappings(temp_proxy.mappings)

            # 检查所有虚拟ID映射
            for virtual_id, mapping in all_mappings.items():
                real_id = mapping['real_id']
                virtual_name = mapping['virtual_name']
                real_name = mapping['real_name']

                # ID转换
                if real_id and value == real_id:
                    return virtual_id

                # 名称转换
                if real_name and value == real_name:
                    return virtual_name

        except Exception:
            # 回退到单账户模式（仅作为最后手段）
            real_id = self.real_account.get('id', '')
            virtual_id = self.virtual_account.get('id', '')
            if real_id and value == real_id:
                return virtual_id

            real_name = self.real_account.get('name', '')
            virtual_name = self.virtual_account.get('name', '')
            if real_name and value == real_name:
                return virtual_name

        return value
    
    def is_request_account_field(self, field_name: str, transform_fields: List[str]) -> bool:
        """判断字段是否是请求中的账户相关字段（全转换）"""
        return field_name.lower() in [f.lower() for f in transform_fields]
    
    def is_response_display_field(self, field_name: str, whitelist: List[str]) -> bool:
        """判断字段是否是响应中的显示字段（白名单模式）"""
        return field_name.lower() in [f.lower() for f in whitelist]
    
    def is_potential_account_field(self, key: str, value: Any) -> bool:
        """判断是否是潜在的账户字段（用于发现遗漏的字段）"""
        # 检查字段名是否包含账户相关关键词
        account_keywords = ['account', 'advertiser', 'user', 'company', 'name', 'id']
        key_lower = key.lower()
        
        # 如果字段名包含账户关键词
        if any(keyword in key_lower for keyword in account_keywords):
            # 检查值是否与真实账户信息匹配
            if isinstance(value, str):
                real_name = self.real_account.get('name', '')
                real_id = self.real_account.get('id', '')
                return value == real_name or value == real_id
        
        return False
    
    def safe_get_request_text(self, request):
        """安全获取请求文本，处理编码问题"""
        return self.encoding_utils.safe_get_request_text(request)
    
    def safe_set_request_text(self, request, text):
        """安全设置请求文本"""
        return self.encoding_utils.safe_set_request_text(request, text)
    
    def get_sample_data(self, data: Any, max_depth: int = 2) -> Any:
        """获取数据样本，用于日志记录"""
        return self.encoding_utils.get_sample_data(data, max_depth)
    
    def safe_value_preview(self, value: Any, max_length: int = 100) -> str:
        """安全的值预览，避免过长的字符串"""
        return self.encoding_utils.safe_value_preview(value, max_length)
    
    def transform_url_params(self, flow) -> bool:
        """转换URL参数"""
        return self.encoding_utils.transform_url_params(flow)
    
    def transform_form_data(self, flow) -> bool:
        """转换表单数据"""
        return self.encoding_utils.transform_form_data(flow) 