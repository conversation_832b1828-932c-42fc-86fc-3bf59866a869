#!/usr/bin/env python3
"""
分析多账户列表API的工具
"""
import json

def analyze_multi_account_api():
    """分析多账户列表API的响应数据"""
    
    # 从日志中提取的API响应数据
    api_response = {
        "msg": "", 
        "code": 0, 
        "data": {
            "org_list": [{
                "account_total_count": 4, 
                "account_list": [
                    {
                        "status": 4, 
                        "sign_url": "https://p3-adv-sign.byteimg.com/web.business.image/19d1cc224def960af9c7a7f9f010c56c~tplv-pu1jpk0zxi-image.png?lk3s=e5fcdeb8&x-expires=**********&x-signature=j40j549T%2BcC8ct6CrtUrU08hLBw%3D", 
                        "name": "广州希赢贸易", 
                        "avatar_uri": "web.business.image/19d1cc224def960af9c7a7f9f010c56c", 
                        "id": "****************"
                    }, 
                    {
                        "status": 4, 
                        "sign_url": "https://p9-adv-sign.byteimg.com/web.business.image/31bed3117be76e77d6eafaa04c09fc29~tplv-pu1jpk0zxi-image.png?lk3s=e5fcdeb8&x-expires=**********&x-signature=94y2hupWCyHgZzYJg1QlOPlgXT4%3D", 
                        "name": "希赢贸易", 
                        "avatar_uri": "web.business.image/31bed3117be76e77d6eafaa04c09fc29", 
                        "id": "*********5114907"
                    }, 
                    {
                        "status": 4, 
                        "sign_url": "https://p9-adv-sign.byteimg.com/web.business.image/3064f0589f8dd6ff4ad84fa2837215ca~tplv-pu1jpk0zxi-image.png?lk3s=e5fcdeb8&x-expires=**********&x-signature=4HX1hqjxXOf3ia4uxB6c6k%2BYksY%3D", 
                        "name": "广州希赢", 
                        "avatar_uri": "web.business.image/3064f0589f8dd6ff4ad84fa2837215ca", 
                        "id": "1830452056275719"
                    }, 
                    {
                        "status": 8, 
                        "sign_url": "https://p6-adv-sign.byteimg.com/web.business.image/95c36473aa662ab739f74fd33340ff5d~tplv-pu1jpk0zxi-image.png?lk3s=e5fcdeb8&x-expires=**********&x-signature=yUgoufvIx5uQYEH8qqpEnYTTpb4%3D", 
                        "name": "初仔好得意", 
                        "avatar_uri": "web.business.image/95c36473aa662ab739f74fd33340ff5d", 
                        "id": "****************"
                    }
                ], 
                "name": "BP-****************", 
                "id": "****************"
            }], 
            "org_total_count": 1
        }, 
        "extra": {}
    }
    
    print("🔍 多账户列表API分析")
    print("=" * 60)
    print(f"API路径: /platform/api/v1/bp/multi_accounts/org_with_account_list/")
    print(f"响应格式: {json.dumps(api_response, ensure_ascii=False, indent=2)}")
    
    print("\n📋 账户列表分析:")
    for org in api_response['data']['org_list']:
        print(f"\n🏢 组织: {org['name']} (ID: {org['id']})")
        print(f"   账户总数: {org['account_total_count']}")
        
        for i, account in enumerate(org['account_list'], 1):
            print(f"   账户 #{i}: {account['name']}")
            print(f"     ID: {account['id']}")
            print(f"     状态: {account['status']}")
    
    print("\n🎯 需要虚拟化的字段:")
    print("1. account['name'] - 账户名称")
    print("2. account['id'] - 账户ID") 
    print("3. 需要添加投放数据字段（消耗、展示、转化等）")
    
    return api_response

if __name__ == "__main__":
    analyze_multi_account_api()
