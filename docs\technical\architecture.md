# 🏗️ 系统架构

## 🎯 架构概览

智能引擎系统采用**模块化代理架构**，基于mitmproxy构建，实现API拦截、识别、修改的统一处理。

## 🏛️ 核心架构

```
┌─────────────────────────────────────────────┐
│                用户层                        │
│    🌐 浏览器 → 巨量引擎广告平台              │
└─────────────────────────────────────────────┘
                       ↕ HTTP/HTTPS
┌─────────────────────────────────────────────┐
│              智能代理层                      │
│       🔄 mitmproxy (端口:8080)              │
│     📡 HTTP拦截 + ✨ 实时响应修改             │
└─────────────────────────────────────────────┘
                       ↕ 
┌─────────────────────────────────────────────┐
│             智能引擎核心                     │
│  🧠 API识别器 + ⚙️ 规则引擎 + 🎨 处理器集群   │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│               配置数据层                     │
│  📄 JSON配置 + 💾 指标数据 + 📋 日志记录      │
└─────────────────────────────────────────────┘
                       ↕
┌─────────────────────────────────────────────┐
│               目标服务器                     │
│       🌐 oceanengine.com API服务器          │
└─────────────────────────────────────────────┘
```

## 🔧 核心模块

### 1. 🧠 智能API识别器

#### 工作流程
```
HTTP请求 → URL模式匹配 → 内容分析 → 规则匹配 → API类型确定
```

#### 识别策略
- **URL模式匹配** (第一优先级)
  - 项目API: `/project/`, `/promote/project/`
  - 广告API: `/promotion/`, `/ads/`
  - 统计API: `/statistics/`, `/statQuery`
  - 账户API: `/account/`, `/balance/`

- **请求体分析** (第二优先级)
  - 搜索参数: `search_type`, `keyword`
  - 业务参数: `local_project_id`, `promotion_id`

- **响应内容分析** (第三优先级)
  - 状态字段: `*_status_*`, `*_name`
  - 指标字段: `metrics`, `*_cnt`, `*_cost`

### 2. ⚙️ 智能规则引擎

#### 配置驱动架构
```json
{
  "api_patterns": {
    "balance": {
      "patterns": ["/account/info", "/dashboard/account_info"],
      "type": "balance",
      "handler": "modify_balance"
    }
  },
  "status_mappings": {
    "balance": {
      "fields": ["state_machine", "status", "notify_status"]
    }
  }
}
```

#### 规则处理流程
1. **API匹配**: 根据URL和内容匹配API类型
2. **处理器选择**: 根据配置选择对应的处理器
3. **状态映射**: 应用预定义的状态转换规则
4. **数据注入**: 注入自定义的指标数据

### 3. 🎨 专业处理器集群

#### 处理器架构
```
🔧 CommonHandler (通用处理器)
├── 🧠 智能API识别
├── ⚙️ 规则引擎调用
└── 🎯 通用错误处理

💰 BalanceHandler (余额处理器)
├── 💰 账户信息修改
├── 💳 余额数据生成
└── 🏢 用户信息定制

📊 StatisticsHandler (统计处理器) 
├── 📈 数据指标注入
├── 🔄 环比数据生成
└── 📊 图表数据优化

📋 ListHandler (列表处理器)
├── 🎯 状态批量修改
├── 📊 指标批量注入
└── 🔍 分页数据处理

📝 DetailHandler (详情处理器)
├── 🎯 单项状态修改
├── 📊 详细指标注入
└── 🔧 字段级别处理

📊 MetricsHandler (指标处理器)
├── 🎯 指标数据构建
├── 🔄 状态字段更新
└── 📈 环比数据计算
```

## 📊 数据流向

### 请求处理流程
```
1. 🌐 用户浏览器发起HTTP请求
2. 🔄 mitmproxy拦截请求，转发到目标服务器
3. 📥 接收目标服务器的原始响应
4. 🧠 智能识别器分析API类型
5. ⚙️ 规则引擎选择处理器
6. 🎨 专业处理器修改响应数据
7. ✨ 返回修改后的响应给浏览器
8. 📱 浏览器渲染优化后的数据
```

### 配置管理流程
```
📄 JSON配置文件 → 🔄 热加载机制 → 💾 内存缓存 → ⚙️ 规则引擎应用
```

## 🛠️ 技术特性

### 🔄 热更新机制
- **配置更新**: 修改JSON配置文件实时生效
- **指标调整**: 修改metrics.json立即应用
- **规则扩展**: 添加新API规则无需重启

### 🛡️ 容错机制
- **API识别失败**: 自动降级到通用处理器
- **数据格式异常**: 保持原始响应，避免破坏
- **处理器异常**: 完善的异常捕获和日志记录

### ⚡ 性能优化
- **缓存机制**: 配置文件内存缓存，避免频繁IO
- **异步处理**: 非阻塞的响应处理机制
- **资源管理**: 合理的内存和CPU资源控制

## 📁 目录结构

```
smart_engine/
├── config/                 # 配置文件
│   ├── api_rules_config.json    # API规则配置
│   └── system_config.json       # 系统配置
├── engine/                 # 核心引擎
│   ├── smart_api_engine_minimal.py     # 核心引擎
│   ├── smart_api_engine_with_monitor.py # 监控增强版
│   ├── api_identifier.py              # API识别器
│   ├── balance_handler.py              # 余额处理器
│   ├── statistics_handler.py           # 统计处理器
│   ├── list_handler.py                # 列表处理器
│   ├── detail_handler.py              # 详情处理器
│   ├── metrics_handler.py             # 指标处理器
│   └── common_handler.py              # 通用处理器
└── utils/                  # 工具模块
    ├── ad_utils.py                    # 工具函数
    ├── api_monitor.py                 # API监控
    └── config_generator.py            # 配置生成
```

## 🎯 设计原则

### 🎨 模块化设计
- **职责分离**: 每个处理器专注特定API类型
- **接口统一**: 所有处理器遵循统一的接口规范
- **低耦合**: 模块间通过配置文件和标准接口交互

### ⚙️ 配置驱动
- **规则外置**: 所有处理规则定义在配置文件中
- **无代码扩展**: 添加新API无需修改代码
- **运行时配置**: 支持动态配置更新

### 🛡️ 可靠性保证
- **异常隔离**: 单个处理器异常不影响整体系统
- **降级机制**: 处理失败时优雅降级
- **监控机制**: 完善的日志和状态监控

## 📈 扩展能力

### 🔧 新API类型支持
1. 在`api_rules_config.json`中添加新的API模式
2. 定义状态映射和数据模板
3. 可选：创建专用处理器

### 🎯 新业务需求支持
1. 扩展`metrics.json`数据模板
2. 添加新的状态映射规则
3. 自定义数据生成算法

### 🌐 新平台支持
1. 分析目标平台API特征
2. 定义平台特定的识别规则
3. 适配处理器逻辑

## 🔗 相关文档

- 📊 [API参考文档](./api-reference.md) - API覆盖详情
- ⚙️ [配置文件说明](./configuration.md) - 配置语法详解
- 🚀 [开发指南](./development.md) - 扩展开发指导

---

**🏗️ 智能、模块化、可扩展的API处理架构** 