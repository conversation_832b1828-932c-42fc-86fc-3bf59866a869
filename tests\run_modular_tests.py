#!/usr/bin/env python3
"""
🧪 模块化测试运行器
运行重构后的功能模块测试，每个模块控制在200行以内
符合AI编程友好的设计原则

测试模块结构：
- test_core_calculations.py (~200行) - 核心计算逻辑
- test_core_file_operations.py (~200行) - 文件操作
- test_configuration_system.py (~200行) - 配置系统基础
- test_configuration_advanced.py (~200行) - 配置系统高级
- test_api_processing.py (~200行) - API处理
- test_account_mapping.py (~200行) - 账户映射基础
- test_account_mapping_advanced.py (~200行) - 账户映射高级
- test_multi_account_system.py (~200行) - 多账户系统基础
- test_multi_account_advanced.py (~200行) - 多账户系统高级
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def run_modular_test_suite():
    """运行完整的模块化测试套件"""
    print("🚀 开始运行模块化测试套件")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📂 工作目录: {os.getcwd()}")
    print("=" * 80)
    
    # 定义测试模块列表
    test_modules = [
        {
            'name': '核心计算逻辑',
            'module': 'tests.test_core_calculations',
            'function': 'run_core_calculation_tests',
            'description': '测试广告指标计算、工具函数、涨幅数据生成'
        },
        {
            'name': '核心文件操作',
            'module': 'tests.test_core_file_operations',
            'function': 'run_core_file_operation_tests',
            'description': '测试配置文件读写、编码处理、大文件处理'
        },
        {
            'name': '配置系统基础',
            'module': 'tests.test_configuration_system',
            'function': 'run_configuration_tests',
            'description': '测试配置加载、保存、验证、默认值处理'
        },
        {
            'name': '配置系统高级',
            'module': 'tests.test_configuration_advanced',
            'function': 'run_configuration_advanced_tests',
            'description': '测试大文件、数据类型、嵌套结构、特殊字符'
        },
        {
            'name': 'API处理系统',
            'module': 'tests.test_api_processing',
            'function': 'run_api_processing_tests',
            'description': '测试API识别器、各种处理器、Handler路由'
        },
        {
            'name': '账户映射基础',
            'module': 'tests.test_account_mapping',
            'function': 'run_account_mapping_tests',
            'description': '测试AccountMapper、ID/名称双向转换、映射配置'
        },
        {
            'name': '账户映射高级',
            'module': 'tests.test_account_mapping_advanced',
            'function': 'run_account_mapping_advanced_tests',
            'description': '测试数据驱动映射、特殊字符、大规模映射'
        },
        {
            'name': '多账户系统基础',
            'module': 'tests.test_multi_account_system',
            'function': 'run_multi_account_tests',
            'description': '测试多账户配置管理、账户切换、URL检测'
        },
        {
            'name': '多账户系统高级',
            'module': 'tests.test_multi_account_advanced',
            'function': 'run_multi_account_advanced_tests',
            'description': '测试响应检测、配置隔离、并发操作、错误处理'
        },
        {
            'name': '集成流程测试',
            'module': 'tests.test_integration_flows',
            'function': 'run_integration_flow_tests',
            'description': '测试端到端集成、用户工作流、系统集成'
        },
        {
            'name': '错误处理系统',
            'module': 'tests.test_error_handling',
            'function': 'run_error_handling_tests',
            'description': '测试异常处理、错误恢复、降级机制'
        }
    ]
    
    # 运行测试统计
    total_modules = len(test_modules)
    passed_modules = 0
    failed_modules = []
    test_results = []
    
    start_time = time.time()
    
    # 逐个运行测试模块
    for i, test_module in enumerate(test_modules, 1):
        module_name = test_module['name']
        module_path = test_module['module']
        function_name = test_module['function']
        description = test_module['description']
        
        print(f"\n📦 [{i}/{total_modules}] {module_name}")
        print(f"📝 {description}")
        print("-" * 60)
        
        module_start_time = time.time()
        
        try:
            # 动态导入模块
            module = __import__(module_path, fromlist=[function_name])
            test_function = getattr(module, function_name)
            
            # 运行测试
            success = test_function()
            
            module_end_time = time.time()
            module_duration = module_end_time - module_start_time
            
            if success:
                passed_modules += 1
                status = "✅ 通过"
                print(f"\n🎉 {module_name} 测试通过！")
            else:
                status = "❌ 失败"
                failed_modules.append(module_name)
                print(f"\n💥 {module_name} 测试失败！")
            
            test_results.append({
                'name': module_name,
                'status': status,
                'duration': module_duration,
                'success': success
            })
            
            print(f"⏱️  耗时: {module_duration:.2f}秒")
            
        except ImportError as e:
            print(f"❌ 无法导入模块 {module_path}: {e}")
            failed_modules.append(f"{module_name} (导入失败)")
            test_results.append({
                'name': module_name,
                'status': "❌ 导入失败",
                'duration': 0,
                'success': False
            })
        except AttributeError as e:
            print(f"❌ 找不到函数 {function_name}: {e}")
            failed_modules.append(f"{module_name} (函数缺失)")
            test_results.append({
                'name': module_name,
                'status': "❌ 函数缺失",
                'duration': 0,
                'success': False
            })
        except Exception as e:
            print(f"❌ 运行测试时出错: {e}")
            failed_modules.append(f"{module_name} (运行错误)")
            test_results.append({
                'name': module_name,
                'status': "❌ 运行错误",
                'duration': 0,
                'success': False
            })
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("📊 模块化测试套件总结")
    print("=" * 80)
    
    print(f"📈 总体结果: {passed_modules}/{total_modules} 个模块通过")
    print(f"⏱️  总耗时: {total_duration:.2f}秒")
    print(f"📅 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 详细结果表格
    print("\n📋 详细结果:")
    print("-" * 80)
    print(f"{'模块名称':<20} {'状态':<12} {'耗时(秒)':<10} {'描述'}")
    print("-" * 80)
    
    for i, result in enumerate(test_results):
        module_info = test_modules[i]
        print(f"{result['name']:<20} {result['status']:<12} {result['duration']:<10.2f} {module_info['description'][:40]}")
    
    # 失败模块列表
    if failed_modules:
        print(f"\n❌ 失败的模块 ({len(failed_modules)}个):")
        for failed_module in failed_modules:
            print(f"  - {failed_module}")
    
    # 成功率统计
    success_rate = (passed_modules / total_modules) * 100
    print(f"\n📊 成功率: {success_rate:.1f}%")
    
    if success_rate == 100:
        print("🎉 所有模块测试通过！项目测试体系重构成功！")
        print("✨ AI编程友好的模块化测试架构已就绪")
    elif success_rate >= 80:
        print("🎯 大部分模块测试通过，少数模块需要修复")
    elif success_rate >= 60:
        print("⚠️ 部分模块测试通过，需要重点关注失败的模块")
    else:
        print("🚨 多数模块测试失败，需要全面检查测试环境和代码")
    
    print("\n" + "=" * 80)
    print("🔧 重构完成说明:")
    print("✅ 每个测试文件控制在200行以内，符合AI编程限制")
    print("✅ 按功能模块组织，逻辑清晰，便于AI理解和修改")
    print("✅ 新增账户映射和多账户系统专门测试")
    print("✅ 补充了关键功能的测试盲点")
    print("✅ 支持单独运行特定功能模块测试")
    print("=" * 80)
    
    return success_rate == 100

def run_specific_module(module_name):
    """运行特定的测试模块"""
    test_modules = {
        'core_calculations': ('tests.test_core_calculations', 'run_core_calculation_tests'),
        'file_operations': ('tests.test_core_file_operations', 'run_core_file_operation_tests'),
        'configuration': ('tests.test_configuration_system', 'run_configuration_tests'),
        'configuration_advanced': ('tests.test_configuration_advanced', 'run_configuration_advanced_tests'),
        'api_processing': ('tests.test_api_processing', 'run_api_processing_tests'),
        'account_mapping': ('tests.test_account_mapping', 'run_account_mapping_tests'),
        'account_mapping_advanced': ('tests.test_account_mapping_advanced', 'run_account_mapping_advanced_tests'),
        'multi_account': ('tests.test_multi_account_system', 'run_multi_account_tests'),
        'multi_account_advanced': ('tests.test_multi_account_advanced', 'run_multi_account_advanced_tests'),
        'integration_flows': ('tests.test_integration_flows', 'run_integration_flow_tests'),
        'error_handling': ('tests.test_error_handling', 'run_error_handling_tests'),
    }
    
    if module_name not in test_modules:
        print(f"❌ 未知的测试模块: {module_name}")
        print(f"可用的模块: {', '.join(test_modules.keys())}")
        return False
    
    module_path, function_name = test_modules[module_name]
    
    try:
        module = __import__(module_path, fromlist=[function_name])
        test_function = getattr(module, function_name)
        return test_function()
    except Exception as e:
        print(f"❌ 运行模块 {module_name} 失败: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 运行特定模块
        module_name = sys.argv[1]
        success = run_specific_module(module_name)
        sys.exit(0 if success else 1)
    else:
        # 运行完整测试套件
        success = run_modular_test_suite()
        sys.exit(0 if success else 1)
