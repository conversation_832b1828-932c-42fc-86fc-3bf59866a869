#!/usr/bin/env python3
"""
🔍 通用虚拟数据检测器
能够检测所有HTTP请求中的虚拟昵称和ID，不限制域名
"""

import re
import json
from typing import Set, Dict, Any, Optional, List
from urllib.parse import urlparse, parse_qs
from mitmproxy import http


class UniversalVirtualDetector:
    """通用虚拟数据检测器"""
    
    def __init__(self):
        self.virtual_patterns = {
            # 虚拟ID模式
            'virtual_ids': [
                r'VIRTUAL_[A-Z]',           # VIRTUAL_A, VIRTUAL_B, etc.
                r'VIRTUAL_\d+',             # VIRTUAL_123, etc.
                r'VIRTUAL_[A-Z]+_ID',       # VIRTUAL_A_ID, etc.
                r'TEST_\w+',                # TEST_ACCOUNT, etc.
            ],
            # 虚拟昵称模式
            'virtual_names': [
                r'测试广告主[A-Z]',           # 测试广告主A, 测试广告主B, etc.
                r'测试\w+',                  # 测试账户, 测试用户, etc.
                r'虚拟\w+',                  # 虚拟账户, 虚拟用户, etc.
                r'Demo\w*',                 # Demo, DemoAccount, etc.
                r'Test\w*',                 # Test, TestAccount, etc.
            ]
        }
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = {
            'virtual_ids': [re.compile(pattern) for pattern in self.virtual_patterns['virtual_ids']],
            'virtual_names': [re.compile(pattern) for pattern in self.virtual_patterns['virtual_names']]
        }
    
    def detect_virtual_data_in_request(self, flow: http.HTTPFlow) -> Dict[str, Set[str]]:
        """
        检测HTTP请求中的所有虚拟数据
        
        Args:
            flow: mitmproxy的HTTP流对象
            
        Returns:
            Dict: {'virtual_ids': set(), 'virtual_names': set()}
        """
        detected = {
            'virtual_ids': set(),
            'virtual_names': set()
        }
        
        # 1. 检测URL中的虚拟数据
        url_detected = self._detect_in_url(flow.request.url)
        detected['virtual_ids'].update(url_detected['virtual_ids'])
        detected['virtual_names'].update(url_detected['virtual_names'])
        
        # 2. 检测请求体中的虚拟数据
        body_detected = self._detect_in_request_body(flow.request)
        detected['virtual_ids'].update(body_detected['virtual_ids'])
        detected['virtual_names'].update(body_detected['virtual_names'])
        
        # 3. 检测请求头中的虚拟数据
        headers_detected = self._detect_in_headers(flow.request.headers)
        detected['virtual_ids'].update(headers_detected['virtual_ids'])
        detected['virtual_names'].update(headers_detected['virtual_names'])
        
        return detected
    
    def _detect_in_url(self, url: str) -> Dict[str, Set[str]]:
        """检测URL中的虚拟数据"""
        detected = {'virtual_ids': set(), 'virtual_names': set()}
        
        # 检测URL路径和查询参数
        full_url_text = url
        
        # 检测虚拟ID
        for pattern in self.compiled_patterns['virtual_ids']:
            matches = pattern.findall(full_url_text)
            detected['virtual_ids'].update(matches)
        
        # 检测虚拟昵称
        for pattern in self.compiled_patterns['virtual_names']:
            matches = pattern.findall(full_url_text)
            detected['virtual_names'].update(matches)
        
        return detected
    
    def _detect_in_request_body(self, request: http.Request) -> Dict[str, Set[str]]:
        """检测请求体中的虚拟数据"""
        detected = {'virtual_ids': set(), 'virtual_names': set()}
        
        try:
            # 获取请求体内容
            if not request.content:
                return detected
            
            body_text = request.content.decode('utf-8', errors='ignore')
            
            # 尝试解析JSON
            try:
                json_data = json.loads(body_text)
                json_text = json.dumps(json_data, ensure_ascii=False)
                body_text = json_text
            except (json.JSONDecodeError, UnicodeDecodeError):
                # 如果不是JSON，直接使用原始文本
                pass
            
            # 检测虚拟ID
            for pattern in self.compiled_patterns['virtual_ids']:
                matches = pattern.findall(body_text)
                detected['virtual_ids'].update(matches)
            
            # 检测虚拟昵称
            for pattern in self.compiled_patterns['virtual_names']:
                matches = pattern.findall(body_text)
                detected['virtual_names'].update(matches)
                
        except Exception:
            # 忽略解析错误
            pass
        
        return detected
    
    def _detect_in_headers(self, headers) -> Dict[str, Set[str]]:
        """检测请求头中的虚拟数据"""
        detected = {'virtual_ids': set(), 'virtual_names': set()}
        
        try:
            # 将所有请求头转换为文本
            headers_text = str(headers)
            
            # 检测虚拟ID
            for pattern in self.compiled_patterns['virtual_ids']:
                matches = pattern.findall(headers_text)
                detected['virtual_ids'].update(matches)
            
            # 检测虚拟昵称
            for pattern in self.compiled_patterns['virtual_names']:
                matches = pattern.findall(headers_text)
                detected['virtual_names'].update(matches)
                
        except Exception:
            # 忽略解析错误
            pass
        
        return detected
    
    def has_virtual_data(self, flow: http.HTTPFlow) -> bool:
        """
        快速检查请求是否包含虚拟数据
        
        Args:
            flow: mitmproxy的HTTP流对象
            
        Returns:
            bool: 是否包含虚拟数据
        """
        detected = self.detect_virtual_data_in_request(flow)
        return len(detected['virtual_ids']) > 0 or len(detected['virtual_names']) > 0
    
    def get_detected_summary(self, flow: http.HTTPFlow) -> str:
        """
        获取检测结果摘要
        
        Args:
            flow: mitmproxy的HTTP流对象
            
        Returns:
            str: 检测结果摘要
        """
        detected = self.detect_virtual_data_in_request(flow)
        
        summary_parts = []
        if detected['virtual_ids']:
            summary_parts.append(f"虚拟ID: {', '.join(detected['virtual_ids'])}")
        if detected['virtual_names']:
            summary_parts.append(f"虚拟昵称: {', '.join(detected['virtual_names'])}")
        
        if summary_parts:
            return f"检测到 {' | '.join(summary_parts)}"
        else:
            return "未检测到虚拟数据"
    
    def add_custom_pattern(self, pattern_type: str, pattern: str):
        """
        添加自定义检测模式
        
        Args:
            pattern_type: 'virtual_ids' 或 'virtual_names'
            pattern: 正则表达式模式
        """
        if pattern_type in self.virtual_patterns:
            self.virtual_patterns[pattern_type].append(pattern)
            self.compiled_patterns[pattern_type].append(re.compile(pattern))
    
    def get_all_virtual_patterns(self) -> Dict[str, List[str]]:
        """获取所有虚拟数据模式"""
        return self.virtual_patterns.copy()


# 全局检测器实例
universal_detector = UniversalVirtualDetector()


def detect_virtual_data(flow: http.HTTPFlow) -> Dict[str, Set[str]]:
    """
    便捷函数：检测HTTP请求中的虚拟数据
    
    Args:
        flow: mitmproxy的HTTP流对象
        
    Returns:
        Dict: 检测到的虚拟数据
    """
    return universal_detector.detect_virtual_data_in_request(flow)


def has_virtual_data(flow: http.HTTPFlow) -> bool:
    """
    便捷函数：检查请求是否包含虚拟数据
    
    Args:
        flow: mitmproxy的HTTP流对象
        
    Returns:
        bool: 是否包含虚拟数据
    """
    return universal_detector.has_virtual_data(flow)
