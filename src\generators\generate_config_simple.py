#!/usr/bin/env python3
"""
⚙️ 智能配置生成器 - API学习模块

📦 模块：src.generators.generate_config_simple
🎯 功能：基于API日志自动生成智能引擎配置的独立工具
📍 位置：重构后移至src/generators/目录，专注配置生成

✨ 核心功能：
- API日志自动分析和模式识别
- 字段类型智能分类（状态字段/指标字段）
- 配置模板自动生成和优化
- 会话管理和增量学习

🔍 分析算法：
- 路径模式频次统计和聚类
- JSON结构递归解析和字段提取
- 关键词匹配的字段类型识别
- 多会话数据的合并和去重

📊 生成的配置：
- api_patterns: API路径匹配规则
- status_mappings: 状态字段映射关系
- metrics_template: 指标字段模板
- 完整的时间戳和来源信息

🔗 依赖：
- data/api_logs/: API调用日志数据
- smart_engine/config/: 配置文件输出目录
- JSON处理和文件操作

📋 重构说明：
- 原位于根目录，现移至src/generators/模块化管理
- 保持所有分析算法和生成逻辑不变
- 支持独立运行和集成调用

💡 使用方式：
- 直接运行: python src/generators/generate_config_simple.py
- 集成调用: 通过监控模式自动触发
"""
import json
import os
from datetime import datetime
from collections import defaultdict, Counter

def get_latest_session():
    """获取最新的学习会话"""
    log_dir = 'data/api_logs'
    if not os.path.exists(log_dir):
        return None
        
    sessions = []
    for file in os.listdir(log_dir):
        if file.endswith('_apis.jsonl'):
            sessions.append(file.replace('_apis.jsonl', ''))
    
    return sorted(sessions)[-1] if sessions else None

def analyze_session_logs(session_id):
    """分析会话日志"""
    log_file = os.path.join('data/api_logs', f"{session_id}_apis.jsonl")
    
    if not os.path.exists(log_file):
        print(f"❌ 会话日志文件不存在: {log_file}")
        return None
        
    apis = []
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    api_info = json.loads(line.strip())
                    apis.append(api_info)
        
        print(f"📊 已加载 {len(apis)} 个API调用记录")
        return analyze_apis(apis)
        
    except Exception as e:
        print(f"❌ 分析会话日志失败: {e}")
        return None

def analyze_apis(apis):
    """分析API数据"""
    analysis = {
        'total_apis': len(apis),
        'api_types': defaultdict(list),
        'path_patterns': defaultdict(Counter),
        'status_fields': defaultdict(set),
        'metrics_fields': defaultdict(set)
    }
    
    for api in apis:
        api_type = api.get('api_type', 'unknown')
        path = api.get('api_path', '')
        
        analysis['api_types'][api_type].append(api)
        analysis['path_patterns'][api_type][path] += 1
        
        # 分析响应结构
        if api.get('response') and api['response'].get('data_structure'):
            extract_field_info(api['response']['data_structure'], analysis, api_type)
    
    return analysis

def extract_field_info(structure, analysis, api_type):
    """提取字段信息"""
    if not isinstance(structure, dict):
        return
        
    if structure.get('type') == 'dict' and 'keys' in structure:
        for key_info in structure['keys']:
            key_name = key_info.get('name', '')
            
            # 识别状态字段
            if is_status_field(key_name):
                analysis['status_fields'][api_type].add(key_name)
            
            # 识别指标字段  
            elif is_metrics_field(key_name):
                analysis['metrics_fields'][api_type].add(key_name)
            
            # 递归处理嵌套结构
            if 'structure' in key_info:
                extract_field_info(key_info['structure'], analysis, api_type)

def is_status_field(field_name):
    """判断是否为状态字段"""
    status_keywords = ['status', 'state', 'opt_status']
    field_lower = field_name.lower()
    return any(keyword in field_lower for keyword in status_keywords)

def is_metrics_field(field_name):
    """判断是否为指标字段"""
    metrics_keywords = ['cnt', 'count', 'cost', 'rate', 'ctr', 'cpm', 'cpc']
    field_lower = field_name.lower()
    return any(keyword in field_lower for keyword in metrics_keywords)

def generate_api_config(analysis):
    """生成API配置"""
    config = {
        "api_patterns": {},
        "status_mappings": {},
        "metrics_template": {},
        "generated_at": datetime.now().isoformat(),
        "source_session": "",
        "summary": {
            "total_apis": analysis['total_apis'],
            "api_types": list(analysis['api_types'].keys())
        }
    }
    
    # 生成API模式配置
    for api_type, apis in analysis['api_types'].items():
        if api_type == 'unknown':
            continue
            
        path_counter = analysis['path_patterns'][api_type]
        common_paths = [path for path, count in path_counter.most_common(5)]
        
        config["api_patterns"][api_type] = {
            "patterns": common_paths,
            "type": api_type,
            "handler": f"modify_{api_type}",
            "count": len(apis)
        }
    
    # 生成状态映射
    for api_type, status_fields in analysis['status_fields'].items():
        if status_fields:
            config["status_mappings"][api_type] = {
                "fields": list(status_fields),
                "enabled_value": 1,
                "disabled_value": 0
            }
    
    # 生成指标模板
    for api_type, metrics_fields in analysis['metrics_fields'].items():
        if metrics_fields:
            config["metrics_template"][api_type] = {
                "fields": list(metrics_fields),
                "multiplier": 1.0
            }
    
    return config

def save_generated_config(config, session_id):
    """保存生成的配置"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"generated_api_config_{timestamp}.json"
    config_path = os.path.join('smart_engine/config', filename)
    
    # 设置源会话
    config["source_session"] = session_id
    
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存到: {config_path}")
        return config_path
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return ""

def main():
    print("⚙️ 智能引擎配置生成器")
    print("=" * 40)
    
    # 获取最新会话
    session_id = get_latest_session()
    if not session_id:
        print("❌ 没有找到学习会话数据")
        print("💡 请先运行监控模式收集API数据")
        return False
    
    print(f"📊 使用会话: {session_id}")
    
    # 分析会话数据
    print("🔍 正在分析API数据...")
    analysis = analyze_session_logs(session_id)
    
    if not analysis:
        print("❌ 分析API数据失败")
        return False
    
    # 显示分析结果
    print(f"\n📋 分析结果:")
    print(f"   总API数: {analysis['total_apis']}")
    print(f"   API类型: {list(analysis['api_types'].keys())}")
    
    for api_type, apis in analysis['api_types'].items():
        print(f"   {api_type}: {len(apis)} 个API")
        
        # 显示主要端点
        path_counter = analysis['path_patterns'][api_type]
        top_paths = path_counter.most_common(3)
        for path, count in top_paths:
            print(f"     └─ {path} ({count}次)")
    
    # 生成配置
    print("\n⚙️ 正在生成配置...")
    config = generate_api_config(analysis)
    
    # 保存配置
    config_path = save_generated_config(config, session_id)
    
    if config_path:
        print(f"\n🎉 配置生成成功!")
        print(f"📄 配置文件: {config_path}")
        print(f"\n📋 生成的配置包含:")
        print(f"   API模式: {len(config['api_patterns'])} 种")
        
        for api_type, pattern in config['api_patterns'].items():
            print(f"   - {api_type}: {pattern['count']} 个API, {len(pattern['patterns'])} 个端点")
        
        if config['status_mappings']:
            print(f"   状态映射: {len(config['status_mappings'])} 种")
        
        if config['metrics_template']:
            print(f"   指标模板: {len(config['metrics_template'])} 种")
        
        return config_path
    else:
        print("❌ 配置生成失败")
        return False

if __name__ == "__main__":
    result = main()
    if result:
        print("\n🚀 下一步: 使用配置合并工具集成到现有系统")
        print("   运行: python tools/config_merger.py")
        print(f"   配置文件: {result}")
    else:
        print("\n⚠️ 配置生成失败，请检查学习数据") 