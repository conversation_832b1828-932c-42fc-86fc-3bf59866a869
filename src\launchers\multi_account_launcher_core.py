#!/usr/bin/env python3
"""
👥 多账户启动器核心模块
从launcher.py中拆分出来，保持文件长度在200行以内
"""
import os
import subprocess


def start_multi_account_mode(log_level="NORMAL"):
    """启动多账户模式 - 整合自launchers/multi_account_launcher.py"""
    
    try:
        # 设置日志级别
        try:
            from smart_engine.utils.debug_logger import set_log_level, clear_log_file
            set_log_level(log_level)
            clear_log_file()
            print(f"✅ 日志级别设置为: {log_level}")
            print("✅ 调试日志已重新初始化")
        except Exception as e:
            print(f"⚠️ 日志设置失败: {e}")
        
        # 显示多账户系统信息
        print("👥 多账户智能引擎 v2.0")
        print("=" * 60)
        print("🎯 设计理念：入口层模式")
        print("   • 只在关键点插入处理逻辑")
        print("   • 账户列表：替换真实账户名称为虚拟账户名称")
        print("   • 账户跳转：映射虚拟ID到真实ID，配置AccountMapper")
        print("   • 单账户处理：完全交给原有的SmartAPIEngine")
        print()
        
        # 加载并显示支持的账户
        try:
            from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
            entry_proxy = MultiAccountEntryProxy()
            stats = entry_proxy.get_stats()
            
            print(f"📊 支持的账户数量: {stats['total_accounts']} 个")
            print("📋 账户映射列表:")
            
            for account_id, info in stats['accounts'].items():
                print(f"   • {info['real_name']} → {info['virtual_name']}")
                print(f"     ID: {account_id}")
            
            print()
            
        except Exception as e:
            print(f"⚠️ 无法加载账户配置: {e}")
            print()

        # 检查依赖
        print("🔍 检查系统依赖...")
        
        # 检查mitmproxy
        try:
            result = subprocess.run(["mitmdump", "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ mitmproxy 已安装")
            else:
                print("❌ mitmproxy 不可用")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print("❌ mitmproxy 未安装")
            print("💡 请安装: pip install mitmproxy")
            return False
        
        # 检查配置文件
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        config_file = os.path.join(project_root, "smart_engine", "config", "multi_account_mapping.json")
        if os.path.exists(config_file):
            print("✅ 多账户配置文件存在")
        else:
            print(f"❌ 配置文件不存在: {config_file}")
            return False
        
        print("✅ 依赖检查通过")
        print()
        
        # 确认启动
        print("🚀 准备启动多账户智能引擎")
        confirm = input("确认启动? (y/N): ").strip().lower()
        
        if confirm in ['y', 'yes']:
            # 启动代理服务
            start_multi_account_proxy(log_level)
        else:
            print("👋 取消启动")
            
    except Exception as e:
        print(f"❌ 多账户模式启动失败: {e}")
        import traceback
        traceback.print_exc()


def start_multi_account_proxy(log_level="NORMAL"):
    """启动多账户代理服务"""
    
    try:
        print("🚀 启动多账户代理服务...")
        
        # 设置环境变量，确保子进程使用正确的日志级别
        os.environ['SMART_ENGINE_LOG_LEVEL'] = log_level
        
        # 构建mitmproxy命令
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        proxy_script = os.path.join(project_root, "smart_engine", "engine", "multi_account_proxy_script.py")
        
        if not os.path.exists(proxy_script):
            print(f"❌ 代理脚本不存在: {proxy_script}")
            return False
        
        # mitmproxy命令参数
        mitm_cmd = [
            "mitmdump",
            "-s", proxy_script,
            "--listen-port", "8080",
            "--set", "confdir=~/.mitmproxy"
        ]
        
        print("📡 代理服务配置:")
        print(f"   • 监听端口: 8080")
        print(f"   • 代理脚本: {os.path.basename(proxy_script)}")
        print(f"   • 配置目录: ~/.mitmproxy")
        print()
        
        print("🔧 启动参数:")
        print(f"   命令: {' '.join(mitm_cmd)}")
        print()
        
        print("⚡ 启动多账户代理服务...")
        print("📌 请在浏览器中配置代理: 127.0.0.1:8080")
        print("🌐 然后访问巨量引擎工作台")
        print("⭐ 多账户列表将显示虚拟账户名称")
        print("🎯 点击账户后将进入原有的智能引擎流程")
        print()
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动mitmproxy
        process = subprocess.Popen(mitm_cmd)
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断服务")
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("🔨 强制终止进程")
                process.kill()
                process.wait()
        
        print("✅ 多账户代理服务已停止")
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
