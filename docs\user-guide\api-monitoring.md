# 🔍 API监控系统使用指南 - 手动操作篇

## 📋 概述

本指南只包含您需要手动操作的步骤，所有自动化部分已省略。按照这些步骤，您可以完成API监控、学习和集成的完整流程。

---

## 🚀 操作流程

### **步骤一：启动API监控**

```bash
# 启动监控模式 (二选一即可)
python quick_start_monitor.py
# 或
python monitor_mode_launcher.py
```

**您需要做的：**
1. 执行上述命令启动监控
2. 配置浏览器代理：`127.0.0.1:8080`
3. 正常使用巨量引擎后台（系统会自动记录API）

---

### **步骤二：生成配置文件**

```bash
# 生成API配置
python generate_config_simple.py
```

**您需要做的：**
1. 执行命令即可，系统会自动分析监控数据并生成配置

---

### **步骤三：合并配置到系统**

```bash
# 合并配置
python tools/config_merger.py
```

**您需要做的：**
1. 执行命令
2. 查看合并预览
3. 输入 `y` 确认合并操作

---

### **步骤四：重启系统应用新配置**

```bash
# 重启主程序
python mitmPro_AD.py
```

**您需要做的：**
1. 重启系统以应用新的API配置
2. 测试新增的API是否正常工作

---

## 🎯 推荐的监控操作

**为了获得更好的学习效果，建议在监控期间执行以下操作：**

### **✅ 必须操作 (30分钟)**
- 查看项目列表
- 查看广告列表  
- 查看数据报表
- 查看账户余额

### **✅ 推荐操作 (1小时)**
- 搜索项目
- 查看广告详情
- 查看素材列表
- 查看统计图表
- 执行任务操作

### **✅ 高级操作 (可选)**
- 催审操作
- 自动化规则
- 推广潜力分析

---

## 🔧 可选配置

### **自定义数据指标 (可选)**

如果您想自定义显示的数据，可以编辑 `metrics.json` 文件：

```json
{
  "Show Count": 1000,     // 展示数
  "Click Count": 50,      // 点击数
  "Convert Count": 5,     // 转化数
  "Stat Cost": 1000.0     // 消耗金额
}
```

### **配置分析 (可选)**

```bash
# 查看当前配置统计
python tools/api_config_analyzer.py
```

---

## 🚨 故障排除

### **1. 启动失败**
```bash
# 如果提示缺少mitmproxy
pip install mitmproxy

# 如果端口被占用
netstat -ano | findstr :8080
taskkill /PID <进程ID> /F
```

### **2. 代理设置**
- 确认浏览器代理设置为：`127.0.0.1:8080`
- 确认能正常访问巨量引擎网站

### **3. 监控无数据**
- 确认已正确设置浏览器代理
- 确认在监控期间访问了巨量引擎相关页面
- 检查 `data/api_logs/` 目录是否有日志文件

### **4. 配置合并失败**
- 检查是否有写入权限
- 确认生成的配置文件存在
- 必要时手动备份原配置文件

---

## ✅ 完成检查

**确认以下事项表示操作成功：**

- [ ] 监控启动成功，控制台有日志输出
- [ ] 配置文件生成成功，在 `smart_engine/config/` 目录下
- [ ] 配置合并成功，系统显示合并完成
- [ ] 重启后系统正常工作，新API被识别处理

---

## 📋 总结

**您只需要执行 4 个命令：**
1. `python quick_start_monitor.py` - 启动监控
2. `python generate_config_simple.py` - 生成配置  
3. `python tools/config_merger.py` - 合并配置
4. `python mitmPro_AD.py` - 重启系统

**总耗时：约10-30分钟（包含监控时间）**

*📅 更新时间: 2025年6月10日*  
*🎯 用途: 手动操作指南* 