#!/usr/bin/env python3
"""
⚙️ 多账户配置管理器
从 multi_account_proxy_script.py 拆分出来的配置管理逻辑
保持文件长度在200行以内
"""

from typing import Optional, Dict, Any
from smart_engine.utils.debug_logger import info_log, warning_log, error_log, debug_log, success_log, set_mapping_context


class MultiAccountConfigManager:
    """多账户配置管理器"""
    
    def __init__(self, entry_proxy, smart_engine):
        """
        初始化配置管理器
        
        Args:
            entry_proxy: 入口代理实例
            smart_engine: 智能引擎实例
        """
        self.entry_proxy = entry_proxy
        self.smart_engine = smart_engine
        self.current_account_config = None
    
    def update_account_mapping_context(self, account_config):
        """更新账户映射日志上下文"""
        if account_config != self.current_account_config:
            self.current_account_config = account_config
            
            # 获取账户映射配置
            account_mapping = account_config.get('account_mapping', {})
            if account_mapping:
                real_account = account_mapping.get('real_account', {})
                virtual_account = account_mapping.get('virtual_account', {})
                
                # 更新日志上下文，确保记录正确的账户信息
                set_mapping_context(real_account, virtual_account)
                
                info_log(f"✅ 账户配置切换: {virtual_account.get('name', '虚拟账户')} → {real_account.get('name', '真实账户')}")
            else:
                warning_log(f"账户配置缺少映射信息: {account_config.get('Account Name', '未知账户')}")
    
    def configure_smart_engine_for_request(self, detected_account_id: str) -> bool:
        """为请求配置智能引擎"""
        try:
            # 动态切换到检测到的账户配置
            account_config = self.entry_proxy.config_manager.get_account_config(detected_account_id)
            if account_config:
                debug_log(f"🔄 [请求处理] 动态切换到账户: {detected_account_id}")

                # 更新SmartEngine配置
                if self.smart_engine:
                    self.smart_engine.metrics = account_config

                    # 重新初始化AccountMapper
                    if 'account_mapping' in account_config and account_config['account_mapping']:
                        from smart_engine.utils.account_mapper import AccountMapper
                        self.smart_engine.account_mapper = AccountMapper(account_config)

                        # 更新account_extractor引用
                        if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                            self.smart_engine.account_extractor.account_mapper = self.smart_engine.account_mapper

                        mapping = account_config['account_mapping']
                        real_name = mapping['real_account']['name']
                        virtual_name = mapping['virtual_account']['name']
                        info_log(f"✅ [请求处理] 账户配置已切换: {real_name} → {virtual_name}")

                # 更新日志上下文
                self.update_account_mapping_context(account_config)
                return True
            
            return False
        except Exception as e:
            error_log(f"❌ [请求处理] 配置智能引擎失败: {e}")
            return False
    
    def configure_smart_engine_for_response(self, detected_account_id: str) -> bool:
        """为响应配置智能引擎"""
        try:
            # 根据检测到的账户ID获取对应配置
            debug_log(f"🔍 [响应处理] 智能检测到账户ID: {detected_account_id}")
            account_config = self.entry_proxy.config_manager.get_account_config(detected_account_id)

            if account_config:
                debug_log(f"✅ [响应处理] 获取到账户配置: {account_config.get('Account Name', '未知账户')}")

                # 🔥 关键修复：强制设置配置并重置处理器
                self.smart_engine.metrics = account_config

                # 重置所有处理器，确保使用新配置
                self.smart_engine.statistics_handler = None
                self.smart_engine.balance_handler = None
                self.smart_engine.metrics_handler = None
                self.smart_engine.api_handlers = None

                # 🔥 最关键的修复：重新初始化AccountMapper
                if 'account_mapping' in account_config and account_config['account_mapping']:
                    try:
                        debug_log(f"🔄 [响应处理] 重新初始化AccountMapper")
                        from smart_engine.utils.account_mapper import AccountMapper
                        self.smart_engine.account_mapper = AccountMapper(account_config)

                        # 🔥 关键修复：同时更新account_extractor的引用
                        if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                            debug_log(f"🔄 [响应处理] 更新AccountExtractor的映射器引用")
                            self.smart_engine.account_extractor.account_mapper = self.smart_engine.account_mapper

                        mapping = account_config['account_mapping']
                        real_name = mapping['real_account']['name']
                        virtual_name = mapping['virtual_account']['name']
                        success_log(f"✅ [响应处理] AccountMapper重新初始化成功")
                        info_log(f"   📋 映射: {real_name} → {virtual_name}")

                    except Exception as e:
                        error_log(f"❌ [响应处理] AccountMapper重新初始化失败: {e}")
                        import traceback
                        debug_log(f"详细错误: {traceback.format_exc()}")
                        self.smart_engine.account_mapper = None
                        # 同时清空account_extractor的引用
                        if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                            self.smart_engine.account_extractor.account_mapper = None
                else:
                    warning_log(f"⚠️ [响应处理] 账户配置缺少account_mapping")
                    self.smart_engine.account_mapper = None
                    # 同时清空account_extractor的引用
                    if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                        self.smart_engine.account_extractor.account_mapper = None

                # 更新日志上下文
                self.update_account_mapping_context(account_config)

                info_log(f"🔄 [响应处理] 动态切换账户配置: {account_config.get('Account Name', '未知账户')} (ID: {detected_account_id})")
                return True
            else:
                error_log(f"❌ [响应处理] 账户配置未找到: {detected_account_id}")
                self.smart_engine.account_mapper = None
                return False
        except Exception as e:
            error_log(f"❌ [响应处理] 配置智能引擎失败: {e}")
            return False
    
    def configure_smart_engine_with_current_config(self) -> bool:
        """使用当前配置配置智能引擎"""
        try:
            # 如果无法从URL提取账户ID，使用当前账户配置
            debug_log(f"🔍 [响应处理] 无法从URL提取账户ID，使用当前配置")
            current_account_config = self.entry_proxy.get_current_account_config()

            if current_account_config:
                debug_log(f"✅ [响应处理] 获取到当前账户配置: {current_account_config.get('Account Name', '未知账户')}")

                self.smart_engine.metrics = current_account_config

                # 重置所有处理器，确保使用新配置
                self.smart_engine.statistics_handler = None
                self.smart_engine.balance_handler = None
                self.smart_engine.metrics_handler = None
                self.smart_engine.api_handlers = None

                # 🔥 最关键的修复：重新初始化AccountMapper
                if 'account_mapping' in current_account_config and current_account_config['account_mapping']:
                    try:
                        debug_log(f"🔄 [响应处理] 重新初始化AccountMapper（当前配置）")
                        from smart_engine.utils.account_mapper import AccountMapper
                        self.smart_engine.account_mapper = AccountMapper(current_account_config)

                        # 🔥 关键修复：同时更新account_extractor的引用
                        if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                            debug_log(f"🔄 [响应处理] 更新AccountExtractor的映射器引用（当前配置）")
                            self.smart_engine.account_extractor.account_mapper = self.smart_engine.account_mapper

                        mapping = current_account_config['account_mapping']
                        real_name = mapping['real_account']['name']
                        virtual_name = mapping['virtual_account']['name']
                        info_log(f"✅ [响应处理] AccountMapper重新初始化成功（当前配置）")
                        info_log(f"   📋 映射: {real_name} → {virtual_name}")

                    except Exception as e:
                        error_log(f"❌ [响应处理] AccountMapper重新初始化失败（当前配置）: {e}")
                        import traceback
                        debug_log(f"详细错误: {traceback.format_exc()}")
                        self.smart_engine.account_mapper = None
                        # 同时清空account_extractor的引用
                        if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                            self.smart_engine.account_extractor.account_mapper = None
                else:
                    warning_log(f"⚠️ [响应处理] 当前账户配置缺少account_mapping")
                    self.smart_engine.account_mapper = None
                    # 同时清空account_extractor的引用
                    if hasattr(self.smart_engine, 'account_extractor') and self.smart_engine.account_extractor:
                        self.smart_engine.account_extractor.account_mapper = None

                # 更新日志上下文
                self.update_account_mapping_context(current_account_config)

                info_log(f"🔄 [响应处理] 使用当前账户配置: {current_account_config.get('Account Name', '未知账户')}")
                return True
            else:
                warning_log(f"⚠️ [响应处理] 无法获取当前账户配置")
                self.smart_engine.account_mapper = None
                return False
        except Exception as e:
            error_log(f"❌ [响应处理] 使用当前配置失败: {e}")
            return False
