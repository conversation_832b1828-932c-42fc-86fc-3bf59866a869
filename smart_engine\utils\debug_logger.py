#!/usr/bin/env python3
"""
🔍 调试日志工具 (增强版)
为账户映射和API处理提供精简而完整的调试信息
"""

import json
import os
from datetime import datetime
from typing import Any, Dict
from pathlib import Path

class DebugLogger:
    def __init__(self, log_file="data/logs/account_mapping_debug.log"):
        """初始化调试日志器"""
        self.log_file = log_file
        self.max_file_size_mb = 5  # 单个日志文件最大5MB
        self.max_lines_per_session = 10000  # 单次会话最大行数
        self.current_lines = 0
        
        # 简化日志级别控制 - 只保留4个实用级别
        self.log_levels = {
            "ERROR": 0,      # 错误信息 - 总是显示
            "WARNING": 1,    # 警告信息 - 重要提醒  
            "SUCCESS": 2,    # 成功信息 - 操作成功
            "INFO": 3,       # 详细信息 - 处理过程
            "DEBUG": 4       # 调试信息 - 最详细
        }
        
        # 简化的级别映射
        self.level_mapping = {
            "QUIET": 2,    # 只显示ERROR、WARNING、SUCCESS
            "NORMAL": 3,   # 显示ERROR、WARNING、SUCCESS、INFO  
            "VERBOSE": 4   # 显示所有信息
        }
        
        # 从环境变量读取日志级别，如果没有则使用默认值
        env_log_level = os.environ.get('SMART_ENGINE_LOG_LEVEL', 'NORMAL').upper()
        if env_log_level in self.level_mapping:
            self.current_level = self.level_mapping[env_log_level]
        else:
            self.current_level = 3  # 默认NORMAL级别
        
        # 记录统计信息
        self.stats = {
            "requests_processed": 0,
            "responses_processed": 0,
            "transformations": 0,
            "skipped_operations": 0
        }
        
        # 映射配置信息（用于记录上下文）
        self.mapping_context = {
            "real_account": None,
            "virtual_account": None,
            "initialized": False
        }
        
        self.ensure_log_dir()
        self.init_session_log()
    
    def ensure_log_dir(self):
        """确保日志目录存在"""
        log_dir = os.path.dirname(self.log_file)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    def init_session_log(self):
        """初始化会话日志（重新启动时清空）"""
        try:
            session_header = f"""=== 账户映射调试日志 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ===
日志级别: {self.get_level_name(self.current_level)}
单文件限制: {self.max_file_size_mb}MB
单次会话限制: {self.max_lines_per_session}行
=====================================

"""
            with open(self.log_file, 'w', encoding='utf-8') as f:
                f.write(session_header)
            self.current_lines = 4  # 头部占用4行
        except Exception as e:
            print(f"❌ 初始化日志文件失败: {e}")
    
    def set_mapping_context(self, real_account: Dict, virtual_account: Dict):
        """设置映射上下文信息"""
        self.mapping_context = {
            "real_account": real_account,
            "virtual_account": virtual_account,
            "initialized": True
        }
        
        # 记录映射配置信息
        mapping_info = f"""
🔄 账户映射配置:
  真实账户: {real_account.get('name', '未知')} (ID: {real_account.get('id', '未知')})
  虚拟账户: {virtual_account.get('name', '未知')} (ID: {virtual_account.get('id', '未知')})
  映射方向: 虚拟 → 真实 (请求) | 真实 → 虚拟 (响应)
"""
        self.log("INFO", mapping_info)
    
    def get_level_name(self, level: int) -> str:
        """获取用户级别名称"""
        for name, val in self.level_mapping.items():
            if val == level:
                return name
        return "UNKNOWN"
    
    def should_log(self, level: str) -> bool:
        """判断是否应该记录此级别的日志"""
        return self.log_levels.get(level, 5) <= self.current_level
    
    def check_file_rotation(self):
        """检查是否需要文件轮转"""
        if self.current_lines >= self.max_lines_per_session:
            self.rotate_log_file("行数超限")
            return
            
        try:
            file_size = os.path.getsize(self.log_file) / (1024 * 1024)  # MB
            if file_size >= self.max_file_size_mb:
                self.rotate_log_file("文件过大")
        except:
            pass
    
    def rotate_log_file(self, reason: str):
        """轮转日志文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{self.log_file}.{timestamp}.bak"
            
            # 备份当前文件
            if os.path.exists(self.log_file):
                os.rename(self.log_file, backup_file)
            
            # 重新初始化
            self.current_lines = 0
            self.init_session_log()
            
            # 重新记录映射配置
            if self.mapping_context.get("initialized"):
                self.set_mapping_context(
                    self.mapping_context["real_account"],
                    self.mapping_context["virtual_account"]
                )
            
            self.log("INFO", f"日志文件轮转: {reason}, 备份至: {backup_file}")
        except Exception as e:
            print(f"❌ 日志轮转失败: {e}")
    
    def log(self, level: str, message: str, data: Any = None, console: bool = None):
        """记录日志（统一版）"""
        if not self.should_log(level):
            return
            
        # 统一的输出控制逻辑 - 控制台和文件使用相同的级别判断
        if console is None:
            console = True  # 默认控制台和文件输出保持一致
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        # 控制台输出
        if console:
            print(log_entry)
        
        # 检查文件轮转
        self.check_file_rotation()
        
        # 文件输出
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry + '\n')
                self.current_lines += 1
                
                # 只在DEBUG级别记录详细数据
                if data is not None and level in ["DEBUG", "INFO"]:
                    if isinstance(data, (dict, list)):
                        data_str = json.dumps(data, ensure_ascii=False, indent=2)
                        # 限制数据输出长度
                        if len(data_str) > 1000:
                            data_str = data_str[:1000] + "...(已截断)"
                        f.write(f"    数据: {data_str}\n")
                        self.current_lines += data_str.count('\n') + 1
                    else:
                        f.write(f"    数据: {data}\n")
                        self.current_lines += 1
                
                # 简化分隔线
                if level in ["ERROR", "WARNING"]:
                    f.write('-' * 40 + '\n')
                    self.current_lines += 1
                    
        except Exception as e:
            print(f"❌ 写入日志文件失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_stats(self, stat_type: str):
        """更新统计信息"""
        if stat_type in self.stats:
            self.stats[stat_type] += 1
    
    def log_stats_summary(self):
        """记录统计摘要"""
        summary = f"""
=== 会话统计摘要 ===
请求处理: {self.stats['requests_processed']}
响应处理: {self.stats['responses_processed']} 
转换操作: {self.stats['transformations']}
跳过操作: {self.stats['skipped_operations']}
=================="""
        self.log("INFO", summary)

# 全局日志器实例
_logger = DebugLogger()

def set_log_level(level: str):
    """设置日志级别"""
    level_upper = level.upper()
    if level_upper in _logger.level_mapping:
        _logger.current_level = _logger.level_mapping[level_upper]
        # 重新初始化日志文件头以显示正确的级别
        _logger.init_session_log()
        level_desc = {
            "QUIET": "简洁模式 (只显示错误和成功信息)",
            "NORMAL": "标准模式 (显示重要处理信息)", 
            "VERBOSE": "详细模式 (显示所有调试信息)"
        }
        print(f"🔧 日志级别设置为: {level_upper} - {level_desc.get(level_upper, '')}")
    else:
        print(f"❌ 不支持的日志级别: {level}，支持的级别: QUIET, NORMAL, VERBOSE")

def set_mapping_context(real_account: Dict, virtual_account: Dict):
    """设置映射上下文信息"""
    _logger.set_mapping_context(real_account, virtual_account)

def debug_log(message: str, data: Any = None):
    """调试级别日志"""
    _logger.log("DEBUG", message, data)

def info_log(message: str, data: Any = None):
    """信息级别日志"""
    _logger.log("INFO", message, data)

def success_log(message: str, data: Any = None):
    """成功级别日志"""
    _logger.log("SUCCESS", message, data)
    _logger.update_stats("transformations")

def warning_log(message: str, data: Any = None):
    """警告级别日志"""
    _logger.log("WARNING", message, data)

def error_log(message: str, data: Any = None):
    """错误级别日志"""
    _logger.log("ERROR", message, data)

def process_log(message: str, data: Any = None):
    """处理过程日志"""
    _logger.log("INFO", message, data)

def transform_log(message: str, data: Any = None):
    """转换过程日志"""
    _logger.log("INFO", message, data)

def mapping_log(operation: str, field: str, old_value: Any, new_value: Any, 
                url: str = None, direction: str = None):
    """专用的映射转换日志"""
    direction_emoji = "🔄" if direction == "request" else "🔁" if direction == "response" else "🔄"
    direction_text = "虚拟→真实" if direction == "request" else "真实→虚拟" if direction == "response" else "转换"
    
    mapping_info = f"{direction_emoji} [{direction_text}] {operation}: {field}={old_value} → {new_value}"
    if url:
        mapping_info += f" | URL: {url[:80]}{'...' if len(url) > 80 else ''}"
    
    _logger.log("INFO", mapping_info)

def response_analysis_log(url: str, original_data: Dict, transformed_data: Dict):
    """响应分析专用日志（增强版）"""
    _logger.update_stats("responses_processed")
    
    # 只在有实际转换时记录
    if original_data != transformed_data:
        # 分析具体转换了什么
        changes = []
        if isinstance(original_data, dict) and isinstance(transformed_data, dict):
            for key in original_data.keys():
                if key in transformed_data and original_data[key] != transformed_data[key]:
                    changes.append(f"{key}={original_data[key]}→{transformed_data[key]}")
        
        change_summary = "; ".join(changes[:3]) + ("..." if len(changes) > 3 else "")
        
        analysis = {
            "url": url,
            "timestamp": datetime.now().isoformat(),
            "changes_detected": True,
            "changes_summary": change_summary
        }
        _logger.log("INFO", f"✅ 响应转换成功: {change_summary}", analysis)
        _logger.update_stats("transformations")
    else:
        _logger.update_stats("skipped_operations")

def clear_log_file():
    """清空日志文件"""
    _logger.init_session_log()
    print(f"✅ 调试日志已重新初始化: {_logger.log_file}")

def get_stats():
    """获取统计信息"""
    return _logger.stats.copy()

def log_session_end():
    """记录会话结束"""
    _logger.log_stats_summary() 