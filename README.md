# 🚀 巨量引擎广告状态修改工具

[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/)
[![mitmproxy](https://img.shields.io/badge/mitmproxy-10.2.2-green.svg)](https://mitmproxy.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ 核心功能

**智能代理工具**，实时修改巨量引擎广告平台API响应，让所有广告状态显示为"启用中"/"投放中"，并自定义展示完美的广告数据指标。

- 🎯 **状态修改**：所有广告自动显示为"启用中"/"投放中"状态
- 📊 **数据自定义**：完全自定义展示量、点击量、转化量、消耗等指标
- 💰 **账户信息**：自定义账户昵称、ID和余额信息
- 🔄 **环比数据**：智能生成符合逻辑的增长数据
- ⚡ **一键启动**：3分钟内完成配置并看到效果

## 🚀 快速开始

### 📋 环境要求
- Python 3.7+
- Windows/MacOS/Linux
- 支持HTTP代理的浏览器

### ⚡ 30秒快速启动

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **一键启动**
```bash
python launcher.py
```
选择 `2` (多账户模式)，然后选择 `y` (确认启动)，使用默认配置即可体验

3. **配置浏览器代理**
- 代理地址：`127.0.0.1:8080`
- 首次使用访问 [mitm.it](http://mitm.it) 安装证书

4. **访问广告平台**
- 打开 [巨量引擎广告平台](https://ad.oceanengine.com/)
- 🎉 所有广告状态已显示为"启用中"，数据完美！

## 🎯 核心效果展示

### 修改前 vs 修改后
| 项目 | 修改前 | 修改后 |
|------|--------|--------|
| 广告状态 | ❌ 已暂停/未投放 | ✅ 启用中/投放中 |
| 展示量 | 实际数据 | 📈 50,000+ |
| 点击率 | 实际数据 | 📊 5.0% |
| 转化率 | 实际数据 | 🚀 20.0% |
| 账户信息 | 真实账户 | 🏢 自定义公司名称 |

## 📊 智能架构

```
用户浏览器 ↔ 本地代理(8080) ↔ 智能引擎 ↔ 巨量引擎API
                                    ↓
                              实时数据修改
```

### 🏗️ 项目结构 (2025简化版)
```
daping/
├── launcher.py              # 🚀 统一启动入口 (简化版)
├── src/                     # 📦 源代码目录
│   ├── commands/           # ⚡ 命令行工具
│   │   └── quick_start_monitor.py # 监控模式启动
│   ├── launchers/          # 🚀 启动器核心模块
│   │   └── multi_account_launcher_core.py # 多账户模式核心
│   └── generators/         # ⚙️ 配置生成器
│       └── generate_config_simple.py # 智能配置生成
├── smart_engine/           # 🧠 智能引擎核心
│   ├── engine/            # API处理引擎
│   ├── config/            # 配置管理
│   └── utils/             # 引擎工具
├── docs/                   # 📚 文档中心
│   ├── api/               # API参考文档
│   ├── user-guide/        # 用户指南
│   └── technical/         # 技术文档
└── tests/                 # 🧪 测试套件
```

### 🔧 核心特性
- **智能识别**：自动识别26+种API类型
- **配置驱动**：无需编程，纯配置文件控制
- **模块化设计**：清晰的src/目录结构，AI友好
- **错误恢复**：完善的异常处理机制

## 🛠️ 高级使用

### 多账户模式
```bash
python launcher.py  # 选择 2 (多账户模式)
```
支持功能：
- 智能账户检测和切换
- 多账户数据隔离
- 自定义账户信息展示
- 动态配置管理

### API监控模式
```bash
python launcher.py  # 选择 1 (API监控模式)
```
实时监控API调用，自动学习新的API规则

### 配置文件
- `metrics.json` - 自定义数据指标
- `smart_engine/config/api_rules_config.json` - API处理规则

## 📚 详细文档

- 📖 [快速开始指南](docs/user-guide/QUICKSTART.md) - 5分钟完整教程
- 🔧 [账户配置指南](docs/user-guide/account-config.md) - 账户信息定制
- 📊 [API接口分析](docs/api/account_apis_summary.md) - API覆盖详情
- 🏗️ [技术架构文档](docs/) - 开发者文档
- 📝 [变更日志](docs/CHANGELOG.md) - 版本更新记录
- 🔧 [功能实现总结](docs/technical/implementation-summary.md) - 技术实现详情

## 🎉 核心价值

### 🎨 **演示利器**
- 完美的广告数据展示
- 专业的账户信息显示
- 符合逻辑的增长数据

### 🔧 **技术优势**
- 配置驱动，无需编程
- 智能识别，自动适配
- 模块化设计，易于扩展

### 🛡️ **安全可靠**
- 本地代理，数据安全
- 仅修改显示，不影响真实数据
- 完善的错误处理机制

## ⚠️ 使用声明

本工具仅供**学习研究**和**技术演示**使用，请遵守相关法律法规和平台规则。修改的数据仅在本地显示，不影响服务器端实际数据。

## 🔗 相关链接

- [巨量引擎广告平台](https://ad.oceanengine.com/)
- [mitmproxy官网](https://mitmproxy.org/)
- [项目问题反馈](issues)

---

**🚀 让广告数据展示更完美，让业务演示更专业！**

*3分钟体验，终身受益 - 立即开始您的广告数据定制之旅！* 