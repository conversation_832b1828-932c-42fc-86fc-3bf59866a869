#!/usr/bin/env python3
"""
🔄 多账户代理脚本 v2.0 (增强版)
基于"入口层"模式的mitmproxy适配器脚本

🎯 功能：
- 账户列表显示：替换真实账户名称为虚拟账户名称
- 账户跳转处理：映射虚拟ID到真实ID，配置AccountMapper
- 单账户处理：完全交给原有的SmartAPIEngine处理
- 统一日志：使用debug_logger系统，记录正确的账户配置
"""

import sys
import os


# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from mitmproxy import http


from smart_engine.engine.core_engine import SmartAPIEngine
from smart_engine.engine.multi_account_entry_proxy import MultiAccountEntryProxy
from smart_engine.utils.debug_logger import info_log, warning_log, error_log, debug_log, process_log, success_log

# 全局实例
entry_proxy = None
smart_engine = None
current_account_config = None

# 全局处理器
detector = None
config_manager = None
response_analyzer = None

def load_multi_account_system():
    """加载多账户系统"""
    global entry_proxy, smart_engine, detector, config_manager, response_analyzer

    try:
        process_log("多账户代理系统初始化...")

        # 1. 创建入口代理
        entry_proxy = MultiAccountEntryProxy()

        # 2. 创建单账户智能引擎（复用原有逻辑）
        smart_engine = SmartAPIEngine()

        # 3. 初始化处理器
        from smart_engine.engine.multi_account_detector import MultiAccountDetector
        from smart_engine.engine.multi_account_response_analyzer import MultiAccountResponseAnalyzer

        detector = MultiAccountDetector(entry_proxy)
        # 简化：不再使用复杂的配置管理器
        config_manager = None
        response_analyzer = MultiAccountResponseAnalyzer()

        stats = entry_proxy.get_stats()
        process_log(f"多账户代理系统初始化完成，支持{stats['total_accounts']}个账户")

        # 记录支持的账户列表
        debug_log("支持的账户列表", {
            "账户数量": stats['total_accounts'],
            "账户信息": [f"{info['real_name']} → {info['virtual_name']} (ID: {account_id})"
                      for account_id, info in stats['accounts'].items()]
        })

        return True

    except Exception as e:
        error_log("多账户代理系统初始化失败", {"错误": str(e)})
        import traceback
        traceback.print_exc()
        return False



def request(flow: http.HTTPFlow) -> None:
    """处理HTTP请求"""
    global entry_proxy, smart_engine, detector, config_manager

    # 延迟加载系统（第一次请求时）
    if entry_proxy is None:
        if not load_multi_account_system():
            return

    try:
        # 🔥 新架构：使用通用请求转换器
        from smart_engine.utils.universal_request_transformer import transform_request
        from smart_engine.utils.universal_virtual_detector import has_virtual_data

        # 1. 快速检查是否包含虚拟数据
        if has_virtual_data(flow):
            # 2. 执行通用转换
            if transform_request(flow):
                debug_log(f"✅ [通用转换器] 请求转换成功: {flow.request.pretty_url}")
            else:
                debug_log(f"⚠️ [通用转换器] 请求无需转换: {flow.request.pretty_url}")

        # 3. 保留原有逻辑作为备用（用于日志记录和特殊处理）
        # 智能检测账户ID（用于日志记录）
        detected_account_id = detector.detect_account_from_request(flow)
        if detected_account_id:
            debug_log(f"🔍 [请求处理] 检测到账户ID: {detected_account_id}")

        # 入口代理处理账户跳转请求（保留作为备用）
        if entry_proxy.process_request(flow):
            info_log("账户配置已更新，单账户引擎将自动生效")

    except Exception as e:
        error_log("请求处理失败", {"错误": str(e), "URL": flow.request.url[:80] + "..."})



def response(flow: http.HTTPFlow) -> None:
    """处理HTTP响应"""
    global entry_proxy, smart_engine, detector, config_manager, response_analyzer

    if entry_proxy is None:
        return

    try:
        url = flow.request.pretty_url

        # 使用响应分析器分析初始状态
        response_analyzer.analyze_initial_response(flow)

        # 分析响应内容结构
        response_analyzer.analyze_response_content(flow)

        # 阶段1: 入口代理处理账户列表响应（替换账户名称）
        debug_log(f"🔍 [代理脚本] 调用入口代理处理响应: {url}")

        # 记录调用前状态
        original_content = response_analyzer.log_before_entry_proxy(flow, url)

        entry_proxy_result = entry_proxy.process_response(flow)
        debug_log(f"🔍 [代理脚本] 入口代理返回结果: {entry_proxy_result}")

        # 分析处理结果
        response_analyzer.analyze_processing_result(flow, original_content)

        if entry_proxy_result:
            info_log("✅ [代理脚本] 入口代理处理完成")

            # 🔥 关键修复：对于多账户API，检查是否应该跳过单账户引擎
            if detector.should_skip_single_engine(url):
                success_log("✅ [代理脚本] 多账户API处理完成，跳过单账户引擎")
                return

        # 阶段2: 智能检测响应对应的账户
        if smart_engine:
            # 🔥 简化：智能检测响应对应的账户ID
            detected_account_id = detector.detect_account_from_response(flow)

            if detected_account_id:
                # 简化：直接记录检测到的账户ID
                debug_log(f"🔍 [响应处理] 检测到账户ID: {detected_account_id}")
            else:
                # 使用默认配置
                debug_log(f"🔍 [响应处理] 使用默认配置")
            
            # 阶段3: 让SmartAPIEngine处理响应（实现数据替换）
            debug_log(f"🔄 [响应处理] 开始执行数据替换: {url}")
            if hasattr(smart_engine, 'process_response_flow'):
                smart_engine.process_response_flow(flow)
            elif hasattr(smart_engine, 'process_flow'):
                smart_engine.process_flow(flow)
            debug_log("✅ [响应处理] 响应数据替换处理完成")

        # 🔥 最终验证：检查最终响应状态
        response_analyzer.analyze_final_response(flow, url)

    except Exception as e:
        error_log("响应处理失败", {"错误": str(e), "URL": flow.request.url[:80] + "..."})

# mitmproxy入口点
addons = [
    # 这个脚本本身就是addon
]

if __name__ == "__main__":
    process_log("多账户代理脚本 v2.0 已加载")
    debug_log("入口层模式：只在关键点插入处理逻辑")
    debug_log("准备处理多账户入口和单账户智能引擎的整合") 