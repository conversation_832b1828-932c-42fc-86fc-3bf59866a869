#!/usr/bin/env python3
"""
🧪 配置系统高级测试模块
专门测试大文件处理、数据类型支持、集成加载等高级配置功能
文件长度限制：200行以内
"""

import os
import sys
import json
import unittest
import tempfile
import shutil

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器和要测试的模块
from tests.test_data_loader import test_data_loader
from smart_engine.utils import ad_utils

class TestConfigurationAdvanced(unittest.TestCase):
    """配置系统高级功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_large_config_file_handling(self):
        """测试大配置文件处理"""
        print("\n🧪 测试大配置文件处理")
        
        # 创建较大的配置数据
        large_data = {
            'large_array': list(range(1000)),
            'large_object': {f'key_{i}': f'value_{i}' for i in range(100)},
            'repeated_data': ['test_string'] * 500
        }
        
        # 保存和加载大文件
        ad_utils.save_metrics_to_file(large_data, 'large_config.json')
        loaded_large = ad_utils.load_metrics_from_file('large_config.json')
        
        # 验证数据完整性
        self.assertEqual(len(loaded_large['large_array']), 1000)
        self.assertEqual(len(loaded_large['large_object']), 100)
        self.assertEqual(len(loaded_large['repeated_data']), 500)
        self.assertEqual(loaded_large['large_array'][999], 999)
        self.assertEqual(loaded_large['large_object']['key_50'], 'value_50')
        
        print("✅ 大配置文件处理测试通过")

    def test_config_data_types(self):
        """测试配置数据类型支持"""
        print("\n🧪 测试配置数据类型支持")
        
        # 测试各种数据类型
        type_test_data = {
            'string': 'test_string',
            'integer': 42,
            'float': 3.14159,
            'boolean_true': True,
            'boolean_false': False,
            'null_value': None,
            'empty_string': '',
            'zero_int': 0,
            'zero_float': 0.0,
            'negative_int': -100,
            'negative_float': -99.99,
            'scientific_notation': 1.23e-4
        }
        
        # 保存和加载
        ad_utils.save_metrics_to_file(type_test_data, 'types_test.json')
        loaded_types = ad_utils.load_metrics_from_file('types_test.json')
        
        # 验证所有数据类型
        self.assertEqual(loaded_types['string'], 'test_string')
        self.assertEqual(loaded_types['integer'], 42)
        self.assertAlmostEqual(loaded_types['float'], 3.14159, places=5)
        self.assertEqual(loaded_types['boolean_true'], True)
        self.assertEqual(loaded_types['boolean_false'], False)
        self.assertIsNone(loaded_types['null_value'])
        self.assertEqual(loaded_types['empty_string'], '')
        self.assertEqual(loaded_types['zero_int'], 0)
        self.assertEqual(loaded_types['zero_float'], 0.0)
        self.assertEqual(loaded_types['negative_int'], -100)
        self.assertEqual(loaded_types['negative_float'], -99.99)
        self.assertAlmostEqual(loaded_types['scientific_notation'], 1.23e-4, places=6)
        
        print("✅ 配置数据类型支持测试通过")

    def test_integration_config_loading(self):
        """测试配置系统集成加载"""
        print("\n🧪 测试配置系统集成加载")
        
        # 测试多个配置文件的加载
        config1 = {'module1': {'setting1': 'value1'}}
        config2 = {'module2': {'setting2': 'value2'}}
        
        ad_utils.save_metrics_to_file(config1, 'config1.json')
        ad_utils.save_metrics_to_file(config2, 'config2.json')
        
        # 分别加载
        loaded_config1 = ad_utils.load_metrics_from_file('config1.json')
        loaded_config2 = ad_utils.load_metrics_from_file('config2.json')
        
        # 验证独立性
        self.assertIn('module1', loaded_config1)
        self.assertNotIn('module2', loaded_config1)
        self.assertIn('module2', loaded_config2)
        self.assertNotIn('module1', loaded_config2)
        
        print("✅ 配置系统集成加载测试通过")

    def test_nested_config_structures(self):
        """测试嵌套配置结构"""
        print("\n🧪 测试嵌套配置结构")
        
        # 创建深度嵌套的配置
        nested_config = {
            'level1': {
                'level2': {
                    'level3': {
                        'level4': {
                            'deep_value': 'found_it',
                            'deep_number': 12345
                        },
                        'level3_value': 'level3_data'
                    },
                    'level2_array': [1, 2, {'nested_in_array': True}]
                },
                'level1_config': {
                    'setting_a': 'value_a',
                    'setting_b': 'value_b'
                }
            },
            'root_level': 'root_value'
        }
        
        # 保存和加载
        ad_utils.save_metrics_to_file(nested_config, 'nested.json')
        loaded_nested = ad_utils.load_metrics_from_file('nested.json')
        
        # 验证深度嵌套数据
        self.assertEqual(loaded_nested['level1']['level2']['level3']['level4']['deep_value'], 'found_it')
        self.assertEqual(loaded_nested['level1']['level2']['level3']['level4']['deep_number'], 12345)
        self.assertEqual(loaded_nested['level1']['level2']['level3']['level3_value'], 'level3_data')
        self.assertEqual(loaded_nested['level1']['level2']['level2_array'][2]['nested_in_array'], True)
        self.assertEqual(loaded_nested['level1']['level1_config']['setting_a'], 'value_a')
        self.assertEqual(loaded_nested['root_level'], 'root_value')
        
        print("✅ 嵌套配置结构测试通过")

    def test_config_with_special_characters(self):
        """测试包含特殊字符的配置"""
        print("\n🧪 测试特殊字符配置")
        
        # 包含各种特殊字符的配置
        special_config = {
            'chinese': '中文测试数据',
            'japanese': 'テストデータ',
            'korean': '테스트 데이터',
            'emoji': '🎯📊💼🚀✅❌',
            'symbols': '!@#$%^&*()_+-=[]{}|;:,.<>?',
            'quotes': 'single\'quote and "double"quote',
            'newlines': 'line1\nline2\nline3',
            'tabs': 'col1\tcol2\tcol3',
            'unicode_escape': '\u4e2d\u6587\u6d4b\u8bd5',
            'mixed': 'English + 中文 + 123 + 🎯'
        }
        
        # 保存和加载
        ad_utils.save_metrics_to_file(special_config, 'special.json')
        loaded_special = ad_utils.load_metrics_from_file('special.json')
        
        # 验证特殊字符处理
        self.assertEqual(loaded_special['chinese'], '中文测试数据')
        self.assertEqual(loaded_special['japanese'], 'テストデータ')
        self.assertEqual(loaded_special['korean'], '테스트 데이터')
        self.assertEqual(loaded_special['emoji'], '🎯📊💼🚀✅❌')
        self.assertEqual(loaded_special['symbols'], '!@#$%^&*()_+-=[]{}|;:,.<>?')
        self.assertEqual(loaded_special['quotes'], 'single\'quote and "double"quote')
        self.assertEqual(loaded_special['newlines'], 'line1\nline2\nline3')
        self.assertEqual(loaded_special['tabs'], 'col1\tcol2\tcol3')
        self.assertEqual(loaded_special['unicode_escape'], '\u4e2d\u6587\u6d4b\u8bd5')
        self.assertEqual(loaded_special['mixed'], 'English + 中文 + 123 + 🎯')
        
        print("✅ 特殊字符配置测试通过")

    def test_config_performance(self):
        """测试配置文件性能"""
        print("\n🧪 测试配置文件性能")
        
        import time
        
        # 创建中等大小的配置文件
        medium_config = {
            'arrays': [list(range(100)) for _ in range(10)],
            'objects': {f'section_{i}': {f'key_{j}': f'value_{i}_{j}' for j in range(20)} for i in range(20)},
            'mixed_data': {
                'strings': [f'string_{i}' for i in range(200)],
                'numbers': [i * 1.5 for i in range(200)],
                'booleans': [i % 2 == 0 for i in range(200)]
            }
        }
        
        # 测试保存性能
        start_time = time.time()
        ad_utils.save_metrics_to_file(medium_config, 'performance.json')
        save_time = time.time() - start_time
        
        # 测试加载性能
        start_time = time.time()
        loaded_config = ad_utils.load_metrics_from_file('performance.json')
        load_time = time.time() - start_time
        
        # 验证数据正确性
        self.assertEqual(len(loaded_config['arrays']), 10)
        self.assertEqual(len(loaded_config['objects']), 20)
        self.assertEqual(len(loaded_config['mixed_data']['strings']), 200)
        
        # 性能应该在合理范围内（通常小于1秒）
        self.assertLess(save_time, 5.0, "保存时间过长")
        self.assertLess(load_time, 5.0, "加载时间过长")
        
        print(f"  📊 保存时间: {save_time:.3f}秒")
        print(f"  📊 加载时间: {load_time:.3f}秒")
        print("✅ 配置文件性能测试通过")

def run_configuration_advanced_tests():
    """运行配置系统高级测试"""
    print("🚀 开始配置系统高级测试...")
    print("=" * 60)
    
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestConfigurationAdvanced)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print(f"📊 配置高级测试结果: {result.testsRun - len(result.failures) - len(result.errors)}/{result.testsRun} 通过")
    
    if result.failures:
        print("❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("🚨 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("🎉 所有配置高级测试通过！")
    else:
        print("❌ 部分配置高级测试失败，需要修复")
    
    return success

if __name__ == "__main__":
    run_configuration_advanced_tests()
