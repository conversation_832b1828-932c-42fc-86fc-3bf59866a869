#!/usr/bin/env python3
"""
🧪 回归测试套件类 - 中间部分
从test_regression.py拆分出来的核心组件测试，符合200行文件长度限制
"""

import os
import sys
import json
import unittest
import tempfile
import shutil
from unittest.mock import patch, MagicMock, mock_open

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入测试数据加载器
from tests.test_data_loader import test_data_loader

# 导入要测试的模块
from smart_engine.engine.api_identifier import APIIdentifier
from smart_engine.engine.balance_handler import BalanceHandler

class TestRegressionSuiteMiddle(unittest.TestCase):
    """回归测试主套件 - 中间部分测试（测试5-6）"""
    
    def setUp(self):
        """测试前准备 - 使用数据驱动的测试数据"""
        self.test_dir = tempfile.mkdtemp()
        self.old_cwd = os.getcwd()
        os.chdir(self.test_dir)
        
        # 🆕 从测试数据加载器获取测试数据
        default_account = test_data_loader.get_account_by_name('basic_account')
        
        # 构建测试用的metrics.json（保持向后兼容）
        self.test_metrics = {
            'CTR (%)': 5.0,
            'Conversion Rate (%)': 20.0,
            'CPM': 10.0,
            'CPC': 0.5,
            'Conversion Cost': 2.5,
            'Stat Cost': 100.0,
            'Show Count': 50000,
            'Click Count': 2500,
            'Convert Count': 500,
            'Account Name': default_account.get('virtual_account', {}).get('name', 'WH-测试公司'),
            'Account ID': default_account.get('virtual_account', {}).get('id', '****************')
        }
        
        # 🆕 添加账户映射数据
        if default_account:
            self.test_metrics['account_mapping'] = {
                'real_account': default_account.get('real_account', {}),
                'virtual_account': default_account.get('virtual_account', {})
            }
        
        with open('metrics.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_metrics, f)
    
    def tearDown(self):
        """测试后清理"""
        os.chdir(self.old_cwd)
        shutil.rmtree(self.test_dir)

    def test_05_api_identifier(self):
        """测试5：API识别器"""
        print("\n🧪 测试5：API识别器")
        
        # 创建正确的配置格式
        default_config = {
            "api_patterns": {
                "balance_api": {
                    "type": "balance",
                    "handler": "modify_balance",
                    "patterns": ["/advertiser/info", "/fund/daily_stat"]
                },
                "statistics_api": {
                    "type": "statistics", 
                    "handler": "modify_statistics",
                    "patterns": ["/report/advertiser/get", "/report/custom"]
                }
            }
        }
        
        identifier = APIIdentifier(default_config)
        
        # 模拟HTTP流对象
        mock_flow = MagicMock()
        mock_flow.request.url = "https://ad.oceanengine.com/openapi/2/advertiser/info/"
        mock_flow.request.text = ""
        
        # 测试API识别
        api_info = identifier.identify_api(mock_flow)
        self.assertIsNotNone(api_info)
        self.assertEqual(api_info['type'], 'balance')
        
        # 测试未知API
        mock_flow.request.url = "https://unknown.com/unknown/api"
        api_info_unknown = identifier.identify_api(mock_flow)
        self.assertIsNone(api_info_unknown)
        
        print("✅ API识别器测试通过")

    def test_06_balance_handler(self):
        """测试6：余额处理器 - 使用真实响应数据"""
        print("\n🧪 测试6：余额处理器（真实响应数据）")
        
        handler = BalanceHandler(self.test_metrics)
        
        # 🆕 获取真实API响应数据  
        api_responses = test_data_loader.get_api_responses('balance_api')
        
        # 测试成功响应场景
        success_response = api_responses.get('success', {})
        if success_response:
            mock_flow = MagicMock()
            mock_flow.response.text = json.dumps(success_response)
            
            api_info = {"type": "balance", "handler": "modify_balance"}
            handler.modify_balance(mock_flow, api_info)
            
            # 验证处理器被调用
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 成功响应场景测试通过")
        else:
            # 回退到原有测试
            mock_flow = MagicMock()
            mock_response_data = {
                "data": {
                    "advertiser_name": "原始账户名", 
                    "advertiser_id": "原始ID",
                    "balance": 1000.0
                }
            }
            mock_flow.response.text = json.dumps(mock_response_data)
            
            api_info = {"type": "balance", "handler": "modify_balance"}
            handler.modify_balance(mock_flow, api_info)
            self.assertTrue(mock_flow.response.set_text.called)
            print("  ✅ 默认测试场景通过")
        
        print("✅ 余额处理器测试通过") 