import json
from mitmproxy import http

# 使用绝对导入
from smart_engine.utils.ad_utils import check_nested_dict_for_key

class DetailHandler:
    """详情数据处理器"""
    
    def __init__(self, metrics_handler):
        """初始化详情处理器
        
        Args:
            metrics_handler: Metrics处理器实例
        """
        self.metrics_handler = metrics_handler
    
    def modify_project_detail(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目详情API"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
            
            # 处理单个项目详情
            if "project" in response_data["data"]:
                project = response_data["data"]["project"]
                self.metrics_handler.update_status_fields(project, 'project')
            else:
                # 直接在data级别处理
                self.metrics_handler.update_status_fields(response_data["data"], 'project')
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 项目详情API修改完成")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改项目详情API出错: {e}")
            raise e
    
    def modify_project_home(self, flow: http.HTTPFlow, api_info: dict):
        """修改项目首页API"""
        try:
            response_data = json.loads(flow.response.text)
            
            # 递归处理所有可能的项目数据
            self.metrics_handler.update_status_fields(response_data, 'project')
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 项目首页API修改完成")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改项目首页API出错: {e}")
            raise e
    
    def modify_promotion_detail(self, flow: http.HTTPFlow, api_info: dict):
        """修改广告详情API"""
        try:
            response_data = json.loads(flow.response.text)
            
            if not check_nested_dict_for_key(response_data, "data"):
                return
            
            # 处理单个广告详情
            if "ad" in response_data["data"]:
                ad = response_data["data"]["ad"]
                self.metrics_handler.update_status_fields(ad, 'promotion')
            else:
                # 直接在data级别处理
                self.metrics_handler.update_status_fields(response_data["data"], 'promotion')
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 广告详情API修改完成")
            
        except Exception as e:
            print(f"❌ [新引擎] 修改广告详情API出错: {e}")
            raise e
    
    def modify_unknown(self, flow: http.HTTPFlow, api_info: dict, metrics):
        """修改未知类型API - 重点处理用户会话信息"""
        try:
            response_data = json.loads(flow.response.text)
            
            # 处理用户会话API
            if '/session/human/latest' in flow.request.url:
                print(f"🔄 [新引擎] 处理用户会话API - 修改登录账户信息")
                
                # 获取用户自定义的账户信息
                account_name = metrics.get('Account Name', 'WH-我的广告公司')
                account_id = metrics.get('Account ID', '****************')
                
                # 修改用户会话中的账户信息
                if 'data' in response_data:
                    if isinstance(response_data['data'], dict):
                        # 注入账户信息到用户会话
                        response_data['data']['user_name'] = account_name
                        response_data['data']['user_id'] = account_id
                        response_data['data']['advertiser_name'] = account_name
                        response_data['data']['advertiser_id'] = account_id
                        response_data['data']['account_name'] = account_name
                        response_data['data']['account_id'] = account_id
                        
                        print(f"✅ [新引擎] 用户会话信息已更新: {account_name} ({account_id})")
            
            # 通用状态字段处理（增加安全检查）
            if isinstance(response_data, dict):
                self.metrics_handler.update_status_fields(response_data, 'unknown')
            
            flow.response.set_text(json.dumps(response_data))
            print(f"✅ [新引擎] 未知类型API修改完成")
            
        except json.JSONDecodeError:
            # 忽略非JSON响应
            print(f"⚠️ [新引擎] 跳过非JSON响应")
        except Exception as e:
            print(f"❌ [新引擎] 修改未知类型API出错: {e}")
            raise e 