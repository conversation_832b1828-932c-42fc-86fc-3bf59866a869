#!/usr/bin/env python3
"""
📚 文档统一整理器
整理项目中散落的文档，建立统一的文档管理体系
"""
import os
import shutil
import json
from datetime import datetime
from typing import Dict, List, Any


class DocumentationOrganizer:
    """文档统一整理器"""
    
    def __init__(self):
        """初始化整理器"""
        self.project_root = os.path.dirname(os.path.dirname(__file__))
        self.docs_root = os.path.join(self.project_root, 'docs')
        self.report = {
            'moved_files': [],
            'organized_docs': [],
            'removed_duplicates': [],
            'created_structure': [],
            'errors': []
        }
        
        # 文档分类规则
        self.doc_categories = {
            'reports': {
                'path': 'reports',
                'description': '项目报告和总结',
                'patterns': ['报告.md', '完成报告.md', 'summary.md', 'SUMMARY.md']
            },
            'development': {
                'path': 'development',
                'description': '开发相关文档',
                'patterns': ['重构.md', '方案.md', '改进.md', 'improvements.md']
            },
            'testing': {
                'path': 'testing',
                'description': '测试相关文档',
                'patterns': ['测试.md', 'test', 'coverage']
            },
            'technical': {
                'path': 'technical',
                'description': '技术文档',
                'patterns': ['technical', 'architecture', 'implementation']
            }
        }
    
    def run_organization(self):
        """运行完整的文档整理"""
        print("📚 开始文档统一整理")
        print("=" * 60)
        
        try:
            # 1. 创建文档目录结构
            self.create_docs_structure()
            
            # 2. 整理根目录散落的文档
            self.organize_root_documents()
            
            # 3. 整理tests目录中的文档
            self.organize_test_documents()
            
            # 4. 清理重复文档
            self.remove_duplicate_documents()
            
            # 5. 创建文档索引
            self.create_documentation_index()
            
            # 6. 生成整理报告
            self.create_organization_report()
            
            print("\n✅ 文档统一整理完成！")
            return True
            
        except Exception as e:
            print(f"❌ 整理过程中出现错误: {e}")
            self.report['errors'].append(str(e))
            return False
    
    def create_docs_structure(self):
        """创建统一的文档目录结构"""
        print("\n📁 创建文档目录结构...")
        
        # 确保docs根目录存在
        if not os.path.exists(self.docs_root):
            os.makedirs(self.docs_root)
            self.report['created_structure'].append('docs/')
        
        # 创建分类目录
        for category, info in self.doc_categories.items():
            category_path = os.path.join(self.docs_root, info['path'])
            if not os.path.exists(category_path):
                os.makedirs(category_path)
                self.report['created_structure'].append(f"docs/{info['path']}/")
                print(f"   📁 创建目录: docs/{info['path']}/")
        
        print(f"✅ 文档目录结构创建完成")
    
    def organize_root_documents(self):
        """整理根目录散落的文档"""
        print("\n📄 整理根目录文档...")
        
        # 扫描根目录的markdown文件
        root_docs = []
        for file in os.listdir(self.project_root):
            if file.endswith('.md') and file != 'README.md':
                root_docs.append(file)
        
        print(f"发现 {len(root_docs)} 个散落文档")
        
        for doc_file in root_docs:
            source_path = os.path.join(self.project_root, doc_file)
            category = self._categorize_document(doc_file)
            
            if category:
                target_dir = os.path.join(self.docs_root, self.doc_categories[category]['path'])
                target_path = os.path.join(target_dir, doc_file)
                
                try:
                    shutil.move(source_path, target_path)
                    self.report['moved_files'].append({
                        'from': doc_file,
                        'to': f"docs/{self.doc_categories[category]['path']}/{doc_file}",
                        'category': category
                    })
                    print(f"   📄 移动: {doc_file} → docs/{self.doc_categories[category]['path']}/")
                except Exception as e:
                    self.report['errors'].append(f"移动文件失败 {doc_file}: {e}")
        
        print(f"✅ 根目录文档整理完成")
    
    def organize_test_documents(self):
        """整理tests目录中的文档"""
        print("\n🧪 整理测试文档...")
        
        tests_dir = os.path.join(self.project_root, 'tests')
        test_docs = []
        
        # 扫描tests目录的文档
        for file in os.listdir(tests_dir):
            if file.endswith('.md') and file != 'README.md':
                test_docs.append(file)
        
        # 扫描根目录的测试相关文档
        for file in os.listdir(self.project_root):
            if file.endswith('.md') and ('测试' in file or 'test' in file.lower()):
                if os.path.exists(os.path.join(self.project_root, file)):
                    test_docs.append(file)
        
        print(f"发现 {len(test_docs)} 个测试相关文档")
        
        testing_dir = os.path.join(self.docs_root, 'testing')
        
        for doc_file in test_docs:
            # 确定源路径
            source_path = os.path.join(tests_dir, doc_file)
            if not os.path.exists(source_path):
                source_path = os.path.join(self.project_root, doc_file)
            
            if os.path.exists(source_path):
                target_path = os.path.join(testing_dir, doc_file)
                
                try:
                    shutil.move(source_path, target_path)
                    self.report['moved_files'].append({
                        'from': doc_file,
                        'to': f"docs/testing/{doc_file}",
                        'category': 'testing'
                    })
                    print(f"   📄 移动: {doc_file} → docs/testing/")
                except Exception as e:
                    self.report['errors'].append(f"移动测试文档失败 {doc_file}: {e}")
        
        print(f"✅ 测试文档整理完成")
    
    def remove_duplicate_documents(self):
        """清理重复文档"""
        print("\n🗑️ 清理重复文档...")
        
        # 检查重复的启动器报告
        reports_dir = os.path.join(self.docs_root, 'reports')
        if os.path.exists(reports_dir):
            launcher_reports = []
            for file in os.listdir(reports_dir):
                if '启动器' in file and '报告' in file:
                    launcher_reports.append(file)
            
            # 保留最新的，删除旧的
            if len(launcher_reports) > 1:
                launcher_reports.sort(key=lambda x: os.path.getmtime(os.path.join(reports_dir, x)), reverse=True)
                for old_report in launcher_reports[1:]:
                    old_path = os.path.join(reports_dir, old_report)
                    try:
                        os.remove(old_path)
                        self.report['removed_duplicates'].append(old_report)
                        print(f"   🗑️ 删除重复文档: {old_report}")
                    except Exception as e:
                        self.report['errors'].append(f"删除重复文档失败 {old_report}: {e}")
        
        print(f"✅ 重复文档清理完成")
    
    def _categorize_document(self, filename: str) -> str:
        """根据文件名分类文档"""
        filename_lower = filename.lower()
        
        for category, info in self.doc_categories.items():
            for pattern in info['patterns']:
                if pattern.lower() in filename_lower:
                    return category
        
        # 默认分类为reports
        return 'reports'
    
    def create_documentation_index(self):
        """创建文档索引"""
        print("\n📋 创建文档索引...")
        
        index_content = self._generate_index_content()
        index_path = os.path.join(self.docs_root, 'INDEX.md')
        
        try:
            with open(index_path, 'w', encoding='utf-8') as f:
                f.write(index_content)
            self.report['created_structure'].append('docs/INDEX.md')
            print(f"✅ 文档索引创建完成: docs/INDEX.md")
        except Exception as e:
            self.report['errors'].append(f"创建文档索引失败: {e}")
    
    def _generate_index_content(self) -> str:
        """生成文档索引内容"""
        content = """# 📚 项目文档索引

## 🎯 文档导航

本项目采用统一的文档管理体系，所有文档按功能分类组织。

### 📁 文档目录结构

```
docs/
├── INDEX.md                    # 📋 文档索引（本文件）
├── README.md                   # 📖 文档中心说明
├── CHANGELOG.md               # 📝 版本更新日志
├── reports/                   # 📊 项目报告和总结
├── development/               # 🔧 开发相关文档
├── testing/                   # 🧪 测试相关文档
├── technical/                 # 🏗️ 技术文档
├── api/                      # 📊 API参考文档
├── user-guide/               # 👤 用户指南
└── design-archive/           # 📋 设计归档
```

### 🚀 快速导航

#### 📊 项目报告 (reports/)
- 项目各阶段完成报告
- 优化和重构总结
- 功能实现报告

#### 🔧 开发文档 (development/)
- 开发方案和改进建议
- 代码重构文档
- 架构优化记录

#### 🧪 测试文档 (testing/)
- 测试套件说明
- 测试覆盖率报告
- 测试体系分析

#### 🏗️ 技术文档 (technical/)
- 系统架构说明
- API实现细节
- 配置文件详解

#### 👤 用户指南 (user-guide/)
- 快速开始指南
- 功能使用说明
- 配置教程

## 📖 主要文档

### 🎯 新用户必读
1. [项目概述](../README.md) - 项目主页和快速开始
2. [用户指南](user-guide/) - 详细使用说明
3. [技术文档](technical/) - 技术实现细节

### 🔧 开发者参考
1. [技术架构](technical/) - 系统设计和实现
2. [API文档](api/) - 接口参考
3. [测试文档](testing/) - 测试体系说明

### 📊 项目管理
1. [项目报告](reports/) - 各阶段完成情况
2. [变更日志](CHANGELOG.md) - 版本更新记录
3. [开发文档](development/) - 开发过程记录

## 🎉 文档特色

### ✅ 统一管理优势
- **结构清晰**: 按功能分类，快速定位
- **避免重复**: 消除文档重复和冗余
- **便于维护**: 统一的文档管理规范
- **查找高效**: 清晰的导航和索引

### 📊 整理效果
- **文档集中**: 所有文档统一管理
- **分类明确**: 按用途和受众分类
- **索引完整**: 提供完整的文档导航
- **维护简单**: 规范化的文档结构

---

**📚 文档统一管理，让信息查找更高效！**

*最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        return content
    
    def create_organization_report(self):
        """创建整理报告"""
        print("\n📊 生成整理报告...")
        
        report = {
            'organization_summary': {
                'completed_at': datetime.now().isoformat(),
                'total_moved_files': len(self.report['moved_files']),
                'total_removed_duplicates': len(self.report['removed_duplicates']),
                'total_created_structure': len(self.report['created_structure']),
                'total_errors': len(self.report['errors'])
            },
            'moved_files': self.report['moved_files'],
            'removed_duplicates': self.report['removed_duplicates'],
            'created_structure': self.report['created_structure'],
            'errors': self.report['errors'],
            'final_structure': self._get_final_structure()
        }
        
        report_file = os.path.join(self.project_root, f'documentation_organization_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"✅ 整理报告已保存: {report_file}")
        except Exception as e:
            print(f"⚠️ 保存报告失败: {e}")
    
    def _get_final_structure(self) -> Dict[str, List[str]]:
        """获取最终的文档结构"""
        structure = {}
        
        if os.path.exists(self.docs_root):
            for item in os.listdir(self.docs_root):
                item_path = os.path.join(self.docs_root, item)
                if os.path.isdir(item_path):
                    structure[f"docs/{item}/"] = [
                        f"📄 {f}" for f in os.listdir(item_path) 
                        if os.path.isfile(os.path.join(item_path, f))
                    ]
                else:
                    if 'docs/' not in structure:
                        structure['docs/'] = []
                    structure['docs/'].append(f"📄 {item}")
        
        return structure
    
    def show_current_status(self):
        """显示当前文档分布状态"""
        print("\n📊 当前文档分布状态")
        print("=" * 50)
        
        # 根目录文档
        root_docs = [f for f in os.listdir(self.project_root) if f.endswith('.md')]
        print(f"\n📁 根目录文档: {len(root_docs)} 个")
        for doc in root_docs[:5]:
            print(f"   📄 {doc}")
        if len(root_docs) > 5:
            print(f"   ... 还有 {len(root_docs) - 5} 个文档")
        
        # tests目录文档
        tests_dir = os.path.join(self.project_root, 'tests')
        if os.path.exists(tests_dir):
            test_docs = [f for f in os.listdir(tests_dir) if f.endswith('.md')]
            print(f"\n📁 tests目录文档: {len(test_docs)} 个")
            for doc in test_docs:
                print(f"   📄 {doc}")
        
        # docs目录文档
        if os.path.exists(self.docs_root):
            docs_files = [f for f in os.listdir(self.docs_root) if f.endswith('.md')]
            print(f"\n📁 docs目录文档: {len(docs_files)} 个")
            for doc in docs_files:
                print(f"   📄 {doc}")


def main():
    """主函数"""
    organizer = DocumentationOrganizer()
    
    print("📚 文档统一整理器")
    print("=" * 60)
    print("功能:")
    print("1. 整理根目录散落的文档")
    print("2. 统一文档目录结构")
    print("3. 清理重复文档")
    print("4. 创建文档索引")
    print()
    
    # 显示当前状态
    organizer.show_current_status()
    
    # 确认执行
    confirm = input("\n是否开始文档统一整理? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        success = organizer.run_organization()
        if success:
            print("\n🎉 文档统一整理完成！")
            print("✅ 项目文档已统一管理，查找更高效")
        else:
            print("\n❌ 整理过程中出现问题，请检查错误信息")
    else:
        print("👋 取消整理")


if __name__ == "__main__":
    main()
