from mitmproxy import http

class APIIdentifier:
    """API识别器"""
    
    def __init__(self, config):
        """初始化API识别器
        
        Args:
            config: 配置字典
        """
        self.config = config
    
    def identify_api(self, flow: http.HTTPFlow) -> dict:
        """增强的API识别逻辑"""
        url = flow.request.url
        request_text = getattr(flow.request, 'text', '') or ''
        
        # 检查配置是否包含api_patterns
        if 'api_patterns' not in self.config:
            return None
        
        for api_name, api_config in self.config['api_patterns'].items():
            patterns = api_config.get('patterns', [])
            
            for pattern in patterns:
                if isinstance(pattern, str):
                    # URL匹配
                    if pattern in url:
                        return {
                            'name': api_name,
                            'type': api_config['type'],
                            'handler': api_config['handler']
                        }
                elif isinstance(pattern, dict):
                    # 请求体匹配
                    if 'request_body' in pattern:
                        keywords = pattern['request_body']
                        if all(keyword in request_text for keyword in keywords):
                            return {
                                'name': api_name,
                                'type': api_config['type'],
                                'handler': api_config['handler']
                            }
                    # URL参数匹配
                    elif 'url_param' in pattern:
                        if pattern['url_param'] in url:
                            return {
                                'name': api_name,
                                'type': api_config['type'],
                                'handler': api_config['handler']
                            }
        
        return None 