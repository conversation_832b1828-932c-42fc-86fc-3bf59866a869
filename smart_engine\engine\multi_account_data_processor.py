#!/usr/bin/env python3
"""
📊 多账户数据处理器
从 multi_account_entry_proxy.py 拆分出来的数据处理逻辑
保持文件长度在200行以内
"""

from typing import Dict, Any, Optional
from smart_engine.utils.debug_logger import info_log, debug_log, error_log


class MultiAccountDataProcessor:
    """多账户数据处理器"""
    
    def __init__(self, mappings: Dict[str, Dict[str, Any]]):
        """
        初始化数据处理器
        
        Args:
            mappings: 账户映射配置
        """
        self.mappings = mappings
    
    def aggregate_virtual_performance_data(self) -> Optional[Dict[str, Dict[str, Any]]]:
        """
        聚合各账户的虚拟投放数据

        🔥 修复：从专用配置文件读取真实数据，不再使用硬编码
        - 从各账户的专用配置文件读取数据
        - 确保数据逻辑一致性
        - 概览数据 = 各账户数据汇总

        Returns:
            Dict[str, Dict[str, Any]]: 账户ID -> 虚拟数据映射
        """
        try:
            import json
            import os

            aggregated_data = {}

            for account_id in self.mappings:
                # 🔥 修复：从专用配置文件读取数据
                config_path = f"configs/accounts/metrics_{account_id}.json"
                if os.path.exists(config_path):
                    try:
                        with open(config_path, 'r', encoding='utf-8') as f:
                            account_config = json.load(f)

                        # 从配置文件读取数据
                        cost = float(account_config.get('Stat Cost', 0))
                        show = int(account_config.get('Show Count', 0))
                        click = int(account_config.get('Click Count', 0))
                        convert = int(account_config.get('Convert Count', 0))

                        # 计算比率指标
                        ctr = (click / show * 100) if show > 0 else 0
                        cvr = (convert / click * 100) if click > 0 else 0
                        cpc = (cost / click) if click > 0 else 0
                        cpm = (cost / show * 1000) if show > 0 else 0

                        aggregated_data[account_id] = {
                            'cost': round(cost, 2),
                            'show': show,
                            'click': click,
                            'convert': convert,
                            'ctr': round(ctr, 2),
                            'cvr': round(cvr, 2),
                            'cpc': round(cpc, 2),
                            'cpm': round(cpm, 2)
                        }

                        debug_log(f"📊 [数据处理器] 读取账户 {account_id} 数据: 消耗¥{cost:.2f} 展示{show:,} 点击{click} 转化{convert}")

                    except Exception as e:
                        error_log(f"❌ [数据处理器] 读取账户 {account_id} 配置失败: {e}")
                        # 使用默认值
                        aggregated_data[account_id] = {
                            'cost': 0.0, 'show': 0, 'click': 0, 'convert': 0,
                            'ctr': 0.0, 'cvr': 0.0, 'cpc': 0.0, 'cpm': 0.0
                        }
                else:
                    debug_log(f"⚠️ [数据处理器] 账户 {account_id} 配置文件不存在: {config_path}")
                    # 使用默认值
                    aggregated_data[account_id] = {
                        'cost': 0.0, 'show': 0, 'click': 0, 'convert': 0,
                        'ctr': 0.0, 'cvr': 0.0, 'cpc': 0.0, 'cpm': 0.0
                    }

            # 🔥 修复：计算汇总数据用于验证
            total_cost = sum(data['cost'] for data in aggregated_data.values())
            total_show = sum(data['show'] for data in aggregated_data.values())
            total_click = sum(data['click'] for data in aggregated_data.values())
            total_convert = sum(data['convert'] for data in aggregated_data.values())

            info_log(f"📈 [数据处理器] 虚拟数据汇总: 消耗¥{total_cost:.2f} 展示{total_show:,} 点击{total_click:,} 转化{total_convert}")

            return aggregated_data

        except Exception as e:
            error_log(f"❌ [数据处理器] 虚拟数据聚合失败: {e}")
            return None

    def get_virtual_overview_data(self, ad_type: str = "feed") -> Optional[Dict[str, Any]]:
        """
        获取虚拟概览数据用于图表显示

        Args:
            ad_type: 广告类型 ("feed" 或 "search")

        Returns:
            包含各指标虚拟数据的字典
        """
        try:
            # 🔥 修复：使用聚合方法获取一致的数据
            aggregated_data = self.aggregate_virtual_data_for_overview(ad_type)
            if aggregated_data and 'overview' in aggregated_data:
                overview = aggregated_data['overview']
                return {
                    'stat_cost': overview['cost'],
                    'show_cnt': overview['show'],
                    'click_cnt': overview['click'],
                    'convert_cnt': overview['convert'],
                    'conversion_cost': overview['conversion_cost'],
                    'conversion_rate': overview['conversion_rate']
                }
            else:
                # 备用方案：根据广告类型返回不同的数据
                if ad_type == "search":
                    # 搜索广告返回0数据（符合业务逻辑）
                    return {
                        'stat_cost': 0,
                        'show_cnt': 0,
                        'click_cnt': 0,
                        'convert_cnt': 0,
                        'conversion_cost': 0,
                        'conversion_rate': 0
                    }
                else:
                    # 如果聚合失败，返回默认数据
                    return {
                        'stat_cost': 0,
                        'show_cnt': 0,
                        'click_cnt': 0,
                        'convert_cnt': 0,
                        'conversion_cost': 0,
                        'conversion_rate': 0
                    }
        except Exception as e:
            error_log(f"❌ [数据处理器] 获取虚拟概览数据失败: {e}")
            return None

    def aggregate_virtual_data_for_overview(self, ad_type: str) -> Optional[Dict[str, Any]]:
        """
        为概览页面聚合虚拟数据

        Args:
            ad_type: 广告类型 ('feed', 'search', 'all')

        Returns:
            Dict: 包含概览数据和各账户数据的字典
        """
        try:
            # 🔥 修复：从各账户配置文件读取实际数据进行聚合
            accounts_data = {}
            total_cost = 0.0
            total_show = 0
            total_click = 0
            total_convert = 0

            # 🎯 根据广告类型定义基础数据
            if ad_type == 'search':
                # 搜索广告：所有数据为0（符合业务实际）
                for account_id in self.mappings:
                    accounts_data[account_id] = {
                        'cost': 0.0,
                        'show': 0,
                        'click': 0,
                        'convert': 0,
                        'conversion_cost': 0.0,
                        'conversion_rate': 0.0,
                        'ctr': 0.0,
                        'cpm': 0.0
                    }
            else:
                # 🔥 修复：从配置文件读取各账户的实际虚拟数据
                import json
                import os

                for account_id in self.mappings:
                    config_path = f"configs/accounts/metrics_{account_id}.json"
                    if os.path.exists(config_path):
                        try:
                            with open(config_path, 'r', encoding='utf-8') as f:
                                account_config = json.load(f)

                            # 从配置文件读取数据
                            cost = float(account_config.get('Stat Cost', 0))
                            show = int(account_config.get('Show Count', 0))
                            click = int(account_config.get('Click Count', 0))
                            convert = int(account_config.get('Convert Count', 0))

                            # 计算衍生指标
                            conversion_cost = (cost / convert) if convert > 0 else 0.0
                            conversion_rate = (convert / show * 100) if show > 0 else 0.0
                            ctr = (click / show * 100) if show > 0 else 0.0
                            cpm = (cost / show * 1000) if show > 0 else 0.0

                            accounts_data[account_id] = {
                                'cost': cost,
                                'show': show,
                                'click': click,
                                'convert': convert,
                                'conversion_cost': conversion_cost,
                                'conversion_rate': conversion_rate,
                                'ctr': ctr,
                                'cpm': cpm
                            }

                            # 累加到总数
                            total_cost += cost
                            total_show += show
                            total_click += click
                            total_convert += convert

                            debug_log(f"📊 [数据处理器] 账户 {account_id} 配置数据: 消耗¥{cost:.2f} 展示{show:,} 点击{click} 转化{convert}")

                        except Exception as e:
                            error_log(f"❌ [数据处理器] 读取账户 {account_id} 配置失败: {e}")
                            # 使用默认值
                            accounts_data[account_id] = {
                                'cost': 0.0, 'show': 0, 'click': 0, 'convert': 0,
                                'conversion_cost': 0.0, 'conversion_rate': 0.0, 'ctr': 0.0, 'cpm': 0.0
                            }
                    else:
                        debug_log(f"⚠️ [数据处理器] 账户 {account_id} 配置文件不存在: {config_path}")
                        # 使用默认值
                        accounts_data[account_id] = {
                            'cost': 0.0, 'show': 0, 'click': 0, 'convert': 0,
                            'conversion_cost': 0.0, 'conversion_rate': 0.0, 'ctr': 0.0, 'cpm': 0.0
                        }

            # 🔥 修复：概览数据基于各账户数据的真实聚合
            overview_data = {
                'cost': total_cost,
                'show': total_show,
                'click': total_click,
                'convert': total_convert,
                'conversion_cost': (total_cost / total_convert) if total_convert > 0 else 0.0,
                'conversion_rate': (total_convert / total_show * 100) if total_show > 0 else 0.0,
                'ctr': (total_click / total_show * 100) if total_show > 0 else 0.0,
                'cpm': (total_cost / total_show * 1000) if total_show > 0 else 0.0
            }

            # 验证数据一致性（概览数据应该等于各账户数据汇总）

            info_log(f"📈 [数据处理器] 数据一致性验证:")
            info_log(f"   概览: 消耗¥{overview_data['cost']:.2f} 展示{overview_data['show']:,} 点击{overview_data['click']} 转化{overview_data['convert']}")
            info_log(f"   汇总: 消耗¥{total_cost:.2f} 展示{total_show:,} 点击{total_click} 转化{total_convert}")

            return {
                'overview': overview_data,
                'accounts': accounts_data,
                'ad_type': ad_type
            }

        except Exception as e:
            error_log(f"❌ [数据处理器] 概览数据聚合失败: {e}")
            return None

    def replace_account_names_in_text(self, text: str) -> str:
        """
        替换文本中的真实账户名为虚拟名称

        Args:
            text: 原始文本

        Returns:
            替换后的文本
        """
        modified_text = text

        for account_id, mapping in self.mappings.items():
            real_name = mapping['real_name']
            virtual_name = mapping['virtual_name']

            # 替换账户名称
            if real_name in modified_text:
                modified_text = modified_text.replace(real_name, virtual_name)

            # 替换账户ID（如果出现在文本中）
            if account_id in modified_text:
                # 🔧 修复：使用统一的虚拟ID管理器获取标准虚拟ID
                from smart_engine.utils.virtual_id_manager import VirtualIdManager
                standard_virtual_id = VirtualIdManager.get_standard_virtual_id(account_id, virtual_name, self.mappings)
                modified_text = modified_text.replace(account_id, standard_virtual_id)

        return modified_text

    def identify_ad_type_from_request(self, request) -> str:
        """
        从请求中识别广告类型

        Args:
            request: HTTP请求对象

        Returns:
            str: 广告类型 ('feed', 'search', 'all')
        """
        try:
            # 🔥 修复：从请求体中解析广告类型（使用正确的属性）
            if hasattr(request, 'content') and request.content:
                import json
                request_body = json.loads(request.content.decode('utf-8'))
                pricing_category = request_body.get('filter', {}).get('pricingCategory', [])

                if 2 in pricing_category:
                    debug_log(f"🎯 [数据处理器] 识别为通投广告 (pricingCategory: {pricing_category})")
                    return 'feed'  # 通投广告
                elif 1 in pricing_category:
                    debug_log(f"🎯 [数据处理器] 识别为搜索广告 (pricingCategory: {pricing_category})")
                    return 'search'  # 搜索广告
                else:
                    debug_log(f"🎯 [数据处理器] 识别为全部广告 (pricingCategory: {pricing_category})")
                    return 'all'  # 全部广告

            # 🔥 新增：从URL参数中识别广告类型（备用方案）
            url = request.pretty_url if hasattr(request, 'pretty_url') else str(request.url)
            if 'campaignType' in url:
                if 'campaignType%5D%5B%5D=2' in url or 'campaignType[]=2' in url:
                    debug_log(f"🎯 [数据处理器] 从URL识别为搜索广告")
                    return 'search'
                else:
                    debug_log(f"🎯 [数据处理器] 从URL识别为通投广告")
                    return 'feed'

        except Exception as e:
            debug_log(f"⚠️ [数据处理器] 广告类型识别失败: {e}")

        return 'feed'  # 🔥 修改：默认返回通投广告（更常见）
