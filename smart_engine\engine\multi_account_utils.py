#!/usr/bin/env python3
"""
🛠️ 多账户工具类
从 multi_account_entry_proxy.py 拆分出来的工具方法
保持文件长度在200行以内
"""

from typing import Dict, Any, Optional


class MultiAccountUtils:
    """多账户工具类"""
    
    def __init__(self, mappings: Dict[str, Dict[str, Any]]):
        """
        初始化工具类
        
        Args:
            mappings: 账户映射配置
        """
        self.mappings = mappings
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_accounts': len(self.mappings),
            'accounts': {
                account_id: {
                    'real_name': mapping['real_name'],
                    'virtual_name': mapping['virtual_name']
                }
                for account_id, mapping in self.mappings.items()
            }
        }
    
    def get_all_account_mappings(self) -> Dict[str, Dict[str, str]]:
        """获取所有账户映射配置 - 使用统一的虚拟ID管理器"""
        from smart_engine.utils.virtual_id_manager import VirtualIdManager
        return VirtualIdManager.get_all_standard_mappings(self.mappings)
    
    def get_virtual_name_by_account_id(self, account_id: str) -> Optional[str]:
        """根据账户ID获取虚拟名称"""
        if account_id in self.mappings:
            return self.mappings[account_id]['virtual_name']
        return None
    
    def get_real_name_by_account_id(self, account_id: str) -> Optional[str]:
        """根据账户ID获取真实名称"""
        if account_id in self.mappings:
            return self.mappings[account_id]['real_name']
        return None
    
    def get_account_id_by_virtual_name(self, virtual_name: str) -> Optional[str]:
        """根据虚拟名称获取账户ID"""
        for account_id, mapping in self.mappings.items():
            if mapping['virtual_name'] == virtual_name:
                return account_id
        return None
    
    def get_account_id_by_real_name(self, real_name: str) -> Optional[str]:
        """根据真实名称获取账户ID"""
        for account_id, mapping in self.mappings.items():
            if mapping['real_name'] == real_name:
                return account_id
        return None
    
    def is_managed_account(self, account_id: str) -> bool:
        """检查是否是管理的账户"""
        return account_id in self.mappings
    
    def get_account_count(self) -> int:
        """获取账户数量"""
        return len(self.mappings)
    
    def get_account_list(self) -> list:
        """获取账户列表"""
        return [
            {
                'account_id': account_id,
                'real_name': mapping['real_name'],
                'virtual_name': mapping['virtual_name']
            }
            for account_id, mapping in self.mappings.items()
        ]
