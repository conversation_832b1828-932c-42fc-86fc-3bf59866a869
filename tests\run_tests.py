#!/usr/bin/env python3
"""
🧪 完整测试套件运行脚本
运行全部35个测试（回归+端到端+补充+Handler路由验证），提供多种测试运行方式和结果比较功能
"""

import os
import sys
import json
import argparse
import subprocess
from datetime import datetime

def run_unittest_suite():
    """运行完整测试套件（所有29个测试）"""
    print("🚀 运行完整测试套件...")
    
    # 确保在正确的目录
    script_dir = os.path.dirname(__file__)
    project_root = os.path.dirname(script_dir)
    os.chdir(project_root)
    
    # 添加项目根目录到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    # 运行所有测试套件
    total_tests = 0
    total_passed = 0
    total_failed = 0
    total_errors = 0

    test_suites = [
        ("核心计算", "tests.test_core_calculations", "run_core_calculation_tests"),
        ("配置系统", "tests.test_configuration_system", "run_configuration_tests"),
        ("账户映射", "tests.test_account_mapping", "run_account_mapping_tests"),
        ("多账户系统", "tests.test_multi_account_system", "run_multi_account_tests"),
        ("端到端测试", "tests.test_end_to_end", "run_end_to_end_tests"),
        ("补充测试", "tests.test_missing_coverage", "run_missing_coverage_tests"),
        ("Handler路由验证", "tests.test_handler_routing", "run_handler_routing_tests"),
        ("mitmproxy集成测试", "tests.test_mitmproxy_integration", "run_mitmproxy_integration_tests")
    ]

    for suite_name, module_name, func_name in test_suites:
        print(f"\n📋 运行{suite_name}套件...")
        print("=" * 60)

        try:
            module = __import__(module_name, fromlist=[func_name])
            run_func = getattr(module, func_name)
            success = run_func()

            if success:
                print(f"✅ {suite_name}套件通过")
                total_passed += 1
            else:
                print(f"❌ {suite_name}套件失败")
                total_failed += 1

        except ImportError as e:
            print(f"❌ 导入{suite_name}模块失败: {e}")
            total_errors += 1
        except AttributeError as e:
            print(f"❌ {suite_name}函数不存在: {e}")
            total_errors += 1
        except Exception as e:
            print(f"❌ {suite_name}运行出错: {e}")
            total_errors += 1
    
    # 输出总结
    print(f"\n🎯 完整测试套件结果摘要")
    print("=" * 60)
    print(f"🧮 核心计算: ~8个测试")
    print(f"⚙️ 配置系统: ~10个测试")
    print(f"🔄 账户映射: ~8个测试")
    print(f"👥 多账户系统: ~8个测试")
    print(f"🚀 端到端测试: 7个测试")
    print(f"🔧 补充测试: 8个测试")
    print(f"🔄 Handler路由验证: 6个测试")
    print(f"🔌 mitmproxy集成测试: 6个测试")
    print(f"📈 总计: ~61个测试")
    print(f"📊 套件统计: 通过 {total_passed}, 失败 {total_failed}, 错误 {total_errors}")

    if total_failed == 0 and total_errors == 0:
        print(f"🎉 所有测试套件通过！")
        return True
    else:
        print(f"⚠️ 有{total_failed}个套件失败，{total_errors}个套件出错")
        return False

def run_pytest_suite():
    """运行pytest版本的测试"""
    print("🚀 运行pytest测试套件...")

    # 确保在正确的目录
    script_dir = os.path.dirname(__file__)
    project_root = os.path.dirname(script_dir)
    os.chdir(project_root)

    # 运行pytest
    cmd = ["python", "-m", "pytest", "tests/", "-v", "--tb=short"]

    try:
        # 设置环境变量以处理编码问题
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        result = subprocess.run(cmd, capture_output=True, text=True,
                              encoding='utf-8', errors='replace', env=env)
        print(result.stdout)
        if result.stderr:
            print("⚠️ 警告信息:")
            print(result.stderr)
        return result.returncode == 0
    except FileNotFoundError:
        print("❌ pytest未安装，请运行: pip install -r tests/requirements-test.txt")
        return False
    except UnicodeDecodeError as e:
        print(f"❌ 编码错误: {e}")
        print("💡 建议使用unittest模式: python tests/run_tests.py")
        return False

def run_quick_test():
    """运行快速测试（只测试核心功能）"""
    print("⚡ 运行快速回归测试...")
    
    # 这里可以只运行最关键的几个测试
    script_dir = os.path.dirname(__file__)
    project_root = os.path.dirname(script_dir)
    os.chdir(project_root)
    
    # 添加项目根目录到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    try:
        # 只运行关键测试模块
        from tests.test_core_calculations import run_core_calculation_tests
        from tests.test_account_mapping import run_account_mapping_tests

        print("🧮 运行核心计算测试...")
        success1 = run_core_calculation_tests()

        print("\n🔄 运行账户映射测试...")
        success2 = run_account_mapping_tests()

        return success1 and success2
    except ImportError as e:
        print(f"❌ 导入测试模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def save_test_baseline():
    """保存测试基准线"""
    print("💾 保存测试基准线...")
    
    baseline_data = {
        "timestamp": datetime.now().isoformat(),
        "version": "baseline",
        "test_results": "full_pass",
        "note": "重构前的基准测试结果"
    }
    
    with open("tests/baseline.json", "w", encoding="utf-8") as f:
        json.dump(baseline_data, f, indent=2, ensure_ascii=False)
    
    print("✅ 基准线已保存到 tests/baseline.json")

def compare_with_baseline():
    """与基准线比较"""
    print("📊 与基准线比较...")
    
    try:
        with open("tests/baseline.json", "r", encoding="utf-8") as f:
            baseline = json.load(f)
        
        print(f"📅 基准时间: {baseline['timestamp']}")
        print(f"📋 基准版本: {baseline['version']}")
        print(f"✅ 基准结果: {baseline['test_results']}")
        
    except FileNotFoundError:
        print("⚠️ 未找到基准文件，请先运行 --save-baseline")
        return False
    
    # 运行当前测试
    current_success = run_unittest_suite()
    
    if current_success:
        print("🎉 当前测试通过，与基准线一致")
        return True
    else:
        print("❌ 当前测试失败，可能存在回归问题")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="完整测试套件运行工具（35个测试）")
    
    group = parser.add_mutually_exclusive_group()
    group.add_argument("--unittest", action="store_true", help="运行unittest套件（默认）")
    group.add_argument("--pytest", action="store_true", help="运行pytest套件")
    group.add_argument("--quick", action="store_true", help="运行快速测试")
    group.add_argument("--save-baseline", action="store_true", help="保存测试基准线")
    group.add_argument("--compare-baseline", action="store_true", help="与基准线比较")
    
    args = parser.parse_args()
    
    print("🧪 完整测试套件运行工具")
    print("=" * 50)
    
    if args.pytest:
        success = run_pytest_suite()
    elif args.quick:
        success = run_quick_test()
    elif args.save_baseline:
        run_unittest_suite()  # 先运行一次完整测试
        save_test_baseline()
        return
    elif args.compare_baseline:
        success = compare_with_baseline()
    else:
        # 默认运行unittest
        success = run_unittest_suite()
    
    if success:
        print("\n🎉 测试完成，所有功能正常！")
        sys.exit(0)
    else:
        print("\n⚠️ 测试失败，请检查问题后再进行重构！")
        sys.exit(1)

if __name__ == "__main__":
    main() 